
import React from 'react';
import { UseFormRegister, FieldErrors } from 'react-hook-form';
import FormField from './FormField';
import { useSettings } from '@/hooks/useSettings';

type CategorySelectorProps = {
  register: UseFormRegister<any>;
  errors: FieldErrors;
};

const CategorySelector: React.FC<CategorySelectorProps> = ({ register, errors }) => {
  const { getArrayConfig } = useSettings();
  const categories = getArrayConfig('categorias');

  return (
    <FormField
      label="Categoria"
      required
      error={errors.categoria?.message?.toString()}
    >
      <select
        {...register('categoria', { required: 'Categoria é obrigatória' })}
        className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-200 focus:border-primary-300"
      >
        <option value="">Selecione</option>
        {categories.length > 0 ? (
          categories.map((category) => (
            <option key={category} value={category}>
              {category}
            </option>
          ))
        ) : (
          <>
            <option value="Quotas">Quotas</option>
            <option value="Multas">Multas</option>
            <option value="Aluguéis">Aluguéis</option>
            <option value="Manutenção">Manutenção</option>
            <option value="Salários">Salários</option>
            <option value="Limpeza">Limpeza</option>
            <option value="Contas">Contas</option>
          </>
        )}
      </select>
    </FormField>
  );
};

export default CategorySelector;
