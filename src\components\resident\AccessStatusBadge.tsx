
import React from 'react';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, Clock } from 'lucide-react';

interface AccessStatusBadgeProps {
  hasAccess: boolean;
  firstLogin?: boolean | null;
}

export const AccessStatusBadge: React.FC<AccessStatusBadgeProps> = ({
  hasAccess,
  firstLogin
}) => {
  if (!hasAccess) {
    return (
      <Badge variant="destructive" className="gap-1">
        <XCircle className="h-3 w-3" />
        Sem Acesso
      </Badge>
    );
  }

  if (firstLogin) {
    return (
      <Badge variant="secondary" className="gap-1">
        <Clock className="h-3 w-3" />
        Primeiro Acesso
      </Badge>
    );
  }

  return (
    <Badge variant="default" className="gap-1 bg-green-500">
      <CheckCircle className="h-3 w-3" />
      Acesso Ativo
    </Badge>
  );
};
