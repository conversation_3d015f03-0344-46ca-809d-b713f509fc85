
import React from 'react';
import EditQuotaModal from '@/components/modals/EditQuotaModal';
import PaymentDateModal from '@/components/modals/PaymentDateModal';
import FineRegularizationModal from '@/components/modals/FineRegularizationModal';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Quota } from '@/types';

interface QuotasModalsProps {
  // Edit Modal
  isModalOpen: boolean;
  onCloseModal: () => void;
  selectedQuota: Partial<Quota> | null;
  onSaveQuota: (quota: any) => void;
  isAddingQuota: boolean;
  isEditingQuota: boolean;
  
  // Payment Modal
  paymentModal: { isOpen: boolean; quota: Quota | null };
  onClosePaymentModal: () => void;
  onConfirmPayment: (data: any) => void;
  
  // Fine Modal
  fineRegularizationModal: { isOpen: boolean; quota: Quota | null };
  onCloseFineModal: () => void;
  onConfirmFineRegularization: (data: any) => void;
  isRegularizingFine: boolean;
  
  // Delete Modal
  quotaToDelete: Quota | null;
  onCancelDelete: () => void;
  onConfirmDelete: () => void;
  
  // Confirm Dialog
  confirmDialog: React.ReactNode;
}

const QuotasModals: React.FC<QuotasModalsProps> = ({
  isModalOpen,
  onCloseModal,
  selectedQuota,
  onSaveQuota,
  isAddingQuota,
  isEditingQuota,
  paymentModal,
  onClosePaymentModal,
  onConfirmPayment,
  fineRegularizationModal,
  onCloseFineModal,
  onConfirmFineRegularization,
  isRegularizingFine,
  quotaToDelete,
  onCancelDelete,
  onConfirmDelete,
  confirmDialog
}) => {
  return (
    <>
      {isModalOpen && (
        <EditQuotaModal
          isOpen={isModalOpen}
          onClose={onCloseModal}
          quota={selectedQuota}
          onSave={onSaveQuota}
          isLoading={isAddingQuota || isEditingQuota}
        />
      )}

      <PaymentDateModal
        isOpen={paymentModal.isOpen}
        onClose={onClosePaymentModal}
        onConfirm={onConfirmPayment}
        isLoading={false}
        quotaInfo={paymentModal.quota ? {
          morador: paymentModal.quota.moradores?.nome || 'N/A',
          mes: paymentModal.quota.mes,
          ano: paymentModal.quota.ano,
          valor: paymentModal.quota.valor
        } : undefined}
      />

      <FineRegularizationModal
        isOpen={fineRegularizationModal.isOpen}
        onClose={onCloseFineModal}
        onConfirm={onConfirmFineRegularization}
        isLoading={isRegularizingFine}
        quotaInfo={fineRegularizationModal.quota ? {
          morador: fineRegularizationModal.quota.moradores?.nome || 'N/A',
          mes: fineRegularizationModal.quota.mes,
          ano: fineRegularizationModal.quota.ano,
          valorMulta: fineRegularizationModal.quota.multa
        } : undefined}
      />

      {confirmDialog}

      <AlertDialog open={quotaToDelete !== null} onOpenChange={(open) => !open && onCancelDelete()}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Excluir Quota</AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja excluir esta quota?
              Esta ação não pode ser desfeita.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction
              onClick={onConfirmDelete}
              className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
            >
              Excluir
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default QuotasModals;
