
import { useState } from 'react';

interface ConfirmDialogState {
  isOpen: boolean;
  title: string;
  description: string;
  onConfirm: () => void;
  confirmText?: string;
  cancelText?: string;
}

export const useConfirmDialog = () => {
  const [dialogState, setDialogState] = useState<ConfirmDialogState>({
    isOpen: false,
    title: '',
    description: '',
    onConfirm: () => {},
    confirmText: 'Confirmar',
    cancelText: 'Cancelar'
  });

  const showConfirmDialog = (config: Omit<ConfirmDialogState, 'isOpen'>) => {
    setDialogState({
      ...config,
      isOpen: true,
      confirmText: config.confirmText || 'Confirmar',
      cancelText: config.cancelText || 'Cancelar'
    });
  };

  const hideConfirmDialog = () => {
    setDialogState(prev => ({ ...prev, isOpen: false }));
  };

  const handleConfirm = () => {
    dialogState.onConfirm();
    hideConfirmDialog();
  };

  return {
    dialogState,
    showConfirmDialog,
    hideConfirmDialog,
    handleConfirm
  };
};
