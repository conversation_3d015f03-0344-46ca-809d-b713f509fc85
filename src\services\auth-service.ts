
import { supabase } from '@/integrations/supabase/client';

/**
 * Sign in with email and password
 */
export const signInWithCredentials = async (email: string, password: string) => {
  try {
    console.log('🔐 Attempting sign in for:', email);
    
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) {
      console.error('❌ Sign in error:', error);
      return { error };
    }

    console.log('✅ Sign in successful:', data.user?.email);
    return { error: null };
  } catch (error) {
    console.error('💥 Sign in exception:', error);
    return { error };
  }
};

/**
 * Sign up with email, password and name
 */
export const signUpWithCredentials = async (email: string, password: string, name: string) => {
  try {
    console.log('📝 Attempting sign up for:', email);
    
    const redirectUrl = `${window.location.origin}/`;
    
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: redirectUrl,
        data: {
          name: name
        }
      }
    });

    if (error) {
      console.error('❌ Sign up error:', error);
      return { error };
    }

    console.log('✅ Sign up successful:', data.user?.email);
    return { error: null };
  } catch (error) {
    console.error('💥 Sign up exception:', error);
    return { error };
  }
};

/**
 * Sign out user
 */
export const signOutUser = async () => {
  try {
    console.log('🚪 Signing out...');
    
    const { error } = await supabase.auth.signOut();
    
    if (error) {
      console.error('❌ Sign out error:', error);
      throw error;
    }

    console.log('✅ Sign out successful');
    
    // Redirect to login
    window.location.href = '/login';
  } catch (error) {
    console.error('💥 Sign out exception:', error);
    throw error;
  }
};
