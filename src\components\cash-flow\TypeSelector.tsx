
import React from 'react';
import { UseFormRegister, FieldErrors } from 'react-hook-form';
import FormField from './FormField';
import { useSettings } from '@/hooks/useSettings';

type TypeSelectorProps = {
  register: UseFormRegister<any>;
  errors: FieldErrors;
  value?: string;
};

const TypeSelector: React.FC<TypeSelectorProps> = ({ register, errors, value }) => {
  const { getArrayConfig } = useSettings();
  const transactionTypes = getArrayConfig('tipos_transacao');

  // Garantir valores válidos fixos
  const validTypes = [
    { value: 'entrada', label: 'Entrada' },
    { value: 'saida', label: '<PERSON>í<PERSON>' }
  ];

  return (
    <FormField
      label="Tipo"
      required
      error={errors.tipo?.message?.toString()}
      helperText={`Valor atual: ${value || 'não definido'}`}
    >
      <select
        {...register('tipo', { 
          required: 'Tipo é obrigatório',
          onChange: (e) => {
            console.log('🔄 TypeSelector: Tipo select changed to:', JSON.stringify(e.target.value));
          }
        })}
        className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-200 focus:border-primary-300"
      >
        <option value="">Selecione o tipo</option>
        {validTypes.map((type) => (
          <option key={type.value} value={type.value}>
            {type.label}
          </option>
        ))}
      </select>
    </FormField>
  );
};

export default TypeSelector;
