
import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Eye, EyeOff, Lock, Mail } from 'lucide-react';

const Login = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [emailError, setEmailError] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [loginError, setLoginError] = useState('');
  const { signIn, isAuthenticated, profile } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const { toast } = useToast();

  // Get the redirect path from location state or default based on user role
  const from = (location.state as any)?.from;

  React.useEffect(() => {
    if (isAuthenticated && profile) {
      // Redirect based on user role
      const redirectPath = from || (profile.role === 'resident' ? '/resident/dashboard' : '/admin/dashboard');
      console.log('🔄 Redirecting authenticated user to:', redirectPath);
      navigate(redirectPath, { replace: true });
    }
  }, [isAuthenticated, profile, navigate, from]);

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!email) {
      setEmailError('O e-mail é obrigatório');
      return false;
    }
    if (!emailRegex.test(email)) {
      setEmailError('Formato de e-mail inválido');
      return false;
    }
    setEmailError('');
    return true;
  };

  const validatePassword = (password: string) => {
    if (!password) {
      setPasswordError('A senha é obrigatória');
      return false;
    }
    setPasswordError('');
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Clear previous errors
    setLoginError('');

    const isEmailValid = validateEmail(email);
    const isPasswordValid = validatePassword(password);

    if (!isEmailValid || !isPasswordValid) {
      return;
    }

    setIsLoading(true);

    try {
      console.log('🔐 Attempting login with:', email);
      await signIn(email, password);
      
      // Only show success toast if we get here without errors
      toast({
        title: "Login bem-sucedido",
        description: "Bem-vindo ao sistema Prédio Azul!",
      });
    } catch (error: any) {
      console.error('❌ Login error:', error);
      
      // Handle specific error types
      if (error?.message?.includes('Invalid login credentials')) {
        setLoginError('Email ou senha incorretos. Verifique suas credenciais e tente novamente.');
      } else if (error?.message?.includes('Email not confirmed')) {
        setLoginError('Por favor, confirme seu email antes de fazer login.');
      } else if (error?.message?.includes('Too many requests')) {
        setLoginError('Muitas tentativas de login. Tente novamente em alguns minutos.');
      } else {
        setLoginError('Erro ao fazer login. Tente novamente.');
      }
      
      toast({
        variant: "destructive",
        title: "Erro no login",
        description: "Verifique suas credenciais e tente novamente.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-gray-50 p-4">
      <div className="w-full max-w-md space-y-8">
        <div className="mt-8 bg-white p-8 shadow-md rounded-xl">
          <h1 className="text-center text-3xl font-bold text-primary">Prédio Azul</h1>
          <h2 className="mb-6 text-center text-2xl font-semibold text-gray-800">Acesso ao Sistema</h2>

          <form onSubmit={handleSubmit} className="space-y-6">
            {loginError && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-sm text-red-700">{loginError}</p>
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="email">E-mail</Label>
              <div className="relative">
                <Input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => {
                    setEmail(e.target.value);
                    if (emailError) validateEmail(e.target.value);
                    if (loginError) setLoginError('');
                  }}
                  placeholder="<EMAIL>"
                  className={`pl-10 ${emailError ? 'border-red-500' : ''}`}
                  autoComplete="email"
                />
                <Mail className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-gray-400" />
              </div>
              {emailError && <p className="text-xs text-red-500">{emailError}</p>}
            </div>

            <div className="space-y-2">
              <div className="flex justify-between">
                <Label htmlFor="password">Senha</Label>
                <a href="#" className="text-xs text-primary hover:underline">
                  Esqueci minha senha
                </a>
              </div>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  value={password}
                  onChange={(e) => {
                    setPassword(e.target.value);
                    if (passwordError) validatePassword(e.target.value);
                    if (loginError) setLoginError('');
                  }}
                  placeholder="********"
                  className={`pl-10 ${passwordError ? 'border-red-500' : ''}`}
                  autoComplete="current-password"
                />
                <Lock className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-gray-400" />
                <button
                  type="button"
                  onClick={togglePasswordVisibility}
                  className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400"
                >
                  {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                </button>
              </div>
              {passwordError && <p className="text-xs text-red-500">{passwordError}</p>}
            </div>

            <Button
              type="submit"
              className="w-full bg-primary hover:bg-primary-600"
              disabled={isLoading}
            >
              {isLoading ? (
                <span className="flex items-center justify-center">
                  <span className="mr-2 h-4 w-4 animate-spin rounded-full border-t-2 border-white"></span>
                  Entrando...
                </span>
              ) : (
                'Entrar'
              )}
            </Button>
          </form>
        </div>

        <div className="text-center text-sm text-gray-500">
          <p>© {new Date().getFullYear()} Prédio Azul. Todos os direitos reservados.</p>
        </div>
      </div>
    </div>
  );
};

export default Login;
