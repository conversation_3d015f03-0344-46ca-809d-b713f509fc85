
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Calendar } from 'lucide-react';
import { QuotaGeneratorDialog } from './QuotaGeneratorDialog';

interface QuotaGeneratorProps {
  onGenerateQuotas: (quotas: any[]) => void;
  isGenerating?: boolean;
}

export const QuotaGenerator: React.FC<QuotaGeneratorProps> = ({
  onGenerateQuotas,
  isGenerating = false
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <>
      <Button
        onClick={() => setIsModalOpen(true)}
        disabled={isGenerating}
        className="flex items-center gap-2"
        variant="outline"
      >
        <Calendar size={18} />
        {isGenerating ? 'Gerando...' : 'Gerar Quotas'}
      </Button>

      <QuotaGeneratorDialog
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onGenerateQuotas={onGenerateQuotas}
        isGenerating={isGenerating}
      />
    </>
  );
};
