
import React from 'react';
import ResidentLayout from '@/components/layout/resident/ResidentLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import QuotaHistory from '@/components/resident/QuotaHistory';
import { useResidentQuotas } from '@/hooks/resident/useResidentQuotas';
import { AlertTriangle, Loader2 } from 'lucide-react';

const ResidentQuotas = () => {
  const { 
    quotas, 
    loading, 
    error, 
    getPaymentStats, 
    downloadReceipt 
  } = useResidentQuotas();

  if (loading) {
    return (
      <ResidentLayout 
        title="Minhas Quotas"
        subtitle="Acompanhe o histórico de pagamentos e situação atual"
      >
        <div className="flex items-center justify-center h-64">
          <div className="flex items-center space-x-2">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span>Carregando quotas...</span>
          </div>
        </div>
      </ResidentLayout>
    );
  }

  if (error) {
    return (
      <ResidentLayout 
        title="Minhas Quotas"
        subtitle="Acompanhe o histórico de pagamentos e situação atual"
      >
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <p className="text-gray-600 mb-2">Erro ao carregar quotas</p>
            <p className="text-sm text-gray-500">{error}</p>
          </div>
        </div>
      </ResidentLayout>
    );
  }

  const stats = getPaymentStats();

  return (
    <ResidentLayout 
      title="Minhas Quotas"
      subtitle="Acompanhe o histórico de pagamentos e situação atual"
    >
      {/* Resumo */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mt-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Quotas Pagas</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.paidQuotas}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Quotas Pendentes</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{stats.pendingQuotas}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Em Atraso</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.overdueQuotas}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Total de Multas</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.totalFines.toLocaleString()} Kz</div>
          </CardContent>
        </Card>
      </div>

      {/* Histórico de Quotas */}
      <div className="mt-8">
        <QuotaHistory 
          quotas={quotas} 
          onDownloadReceipt={downloadReceipt}
        />
      </div>
    </ResidentLayout>
  );
};

export default ResidentQuotas;
