
import React from 'react';
import { UseFormRegister, FieldErrors } from 'react-hook-form';
import FormField from './FormField';

type ValueInputProps = {
  register: UseFormRegister<any>;
  errors: FieldErrors;
};

const ValueInput: React.FC<ValueInputProps> = ({ register, errors }) => {
  return (
    <FormField
      label="Valor"
      required
      error={errors.valor?.message?.toString()}
    >
      <div className="relative">
        <input
          {...register('valor', { required: 'Valor é obrigatório' })}
          type="text"
          className="w-full pl-10 px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-200 focus:border-primary-300"
          placeholder="0,00"
        />
        <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">Kz</span>
      </div>
    </FormField>
  );
};

export default ValueInput;
