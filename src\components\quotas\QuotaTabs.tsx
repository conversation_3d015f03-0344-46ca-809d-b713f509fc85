
import React from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, TabsContent } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Calendar, AlertTriangle, DollarSign, Clock } from 'lucide-react';

interface QuotaTabsProps {
  quotas: any[];
  activeTab: string;
  onTabChange: (tab: string) => void;
  children: React.ReactNode;
}

export const QuotaTabs: React.FC<QuotaTabsProps> = ({
  quotas,
  activeTab,
  onTabChange,
  children
}) => {
  const getCurrentMonthQuotas = () => {
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth() + 1;
    const currentYear = currentDate.getFullYear();
    
    return quotas.filter(quota => 
      quota.mes === currentMonth && quota.ano === currentYear
    );
  };

  const getOverdueQuotas = () => {
    const currentDate = new Date();
    return quotas.filter(quota => {
      const dueDate = new Date(quota.data_vencimento);
      return quota.status !== 'Pago' && dueDate < currentDate;
    });
  };

  const getQuotasWithFines = () => {
    return quotas.filter(quota => quota.multa > 0);
  };

  const currentMonthQuotas = getCurrentMonthQuotas();
  const overdueQuotas = getOverdueQuotas();
  const quotasWithFines = getQuotasWithFines();

  return (
    <Tabs value={activeTab} onValueChange={onTabChange} className="w-full">
      {/* Container com scroll horizontal para mobile */}
      <div className="w-full overflow-x-auto mb-6 scrollbar-hide">
        <div className="min-w-max">
          <TabsList className="grid grid-cols-4 w-full min-w-[600px] md:min-w-full">
            <TabsTrigger
              value="all"
              className="flex items-center justify-center gap-1 md:gap-2 px-3 md:px-4 py-2 whitespace-nowrap text-xs md:text-sm"
            >
              <Calendar size={14} className="md:w-4 md:h-4 flex-shrink-0" />
              <span className="hidden sm:inline">Todas</span>
              <span className="sm:hidden">Todas</span>
              <Badge variant="secondary" className="ml-1 text-xs px-1.5 py-0.5 flex-shrink-0">
                {quotas.length}
              </Badge>
            </TabsTrigger>

            <TabsTrigger
              value="current"
              className="flex items-center justify-center gap-1 md:gap-2 px-3 md:px-4 py-2 whitespace-nowrap text-xs md:text-sm"
            >
              <Clock size={14} className="md:w-4 md:h-4 flex-shrink-0" />
              <span className="hidden sm:inline">Mês Atual</span>
              <span className="sm:hidden">Atual</span>
              <Badge variant="secondary" className="ml-1 text-xs px-1.5 py-0.5 flex-shrink-0">
                {currentMonthQuotas.length}
              </Badge>
            </TabsTrigger>

            <TabsTrigger
              value="overdue"
              className="flex items-center justify-center gap-1 md:gap-2 px-3 md:px-4 py-2 whitespace-nowrap text-xs md:text-sm"
            >
              <AlertTriangle size={14} className="md:w-4 md:h-4 flex-shrink-0" />
              <span className="hidden sm:inline">Em Atraso</span>
              <span className="sm:hidden">Atraso</span>
              <Badge variant="destructive" className="ml-1 text-xs px-1.5 py-0.5 flex-shrink-0">
                {overdueQuotas.length}
              </Badge>
            </TabsTrigger>

            <TabsTrigger
              value="fines"
              className="flex items-center justify-center gap-1 md:gap-2 px-3 md:px-4 py-2 whitespace-nowrap text-xs md:text-sm"
            >
              <DollarSign size={14} className="md:w-4 md:h-4 flex-shrink-0" />
              <span className="hidden sm:inline">Com Multa</span>
              <span className="sm:hidden">Multa</span>
              <Badge variant="outline" className="ml-1 text-xs px-1.5 py-0.5 flex-shrink-0 border-orange-500 text-orange-600">
                {quotasWithFines.length}
              </Badge>
            </TabsTrigger>
          </TabsList>
        </div>
      </div>

      <TabsContent value={activeTab} className="mt-0">
        {children}
      </TabsContent>
    </Tabs>
  );
};
