import React, { useState, useEffect } from 'react';
import Sidebar from '@/components/layout/Sidebar';
import Header from '@/components/layout/Header';
import DataTable from '@/components/tables/DataTable';
import EditUserModal from '@/components/modals/EditUserModal';
import { useSidebar } from '@/contexts/SidebarContext';
import { cn } from '@/lib/utils';
import {
  UserPlus,
  Mail,
  Shield,
  User,
  Trash2,
  AlertCircle
} from 'lucide-react';
import { toast } from 'sonner';
import { User as UserType } from '@/types';
import { getUsers, createUser, updateUser, deleteUser, checkUserPermissions, type UserPermissions } from '@/utils/user';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogHeader,
  DialogFooter
} from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";

const newUserSchema = z.object({
  name: z.string().min(1, "Nome é obrigatório"),
  email: z.string().email("Email inválido"),
  password: z.string().min(6, "Senha deve ter pelo menos 6 caracteres"),
  role: z.enum(["Administrador", "Convidado"]),
});

type NewUserFormValues = z.infer<typeof newUserSchema>;

const Users = () => {
  const { collapsed, isMobile } = useSidebar();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<UserType | null>(null);
  const [userToDelete, setUserToDelete] = useState<UserType | null>(null);
  const queryClient = useQueryClient();

  const newUserForm = useForm<NewUserFormValues>({
    resolver: zodResolver(newUserSchema),
    defaultValues: {
      name: '',
      email: '',
      password: '',
      role: 'Convidado',
    }
  });

  // Check user permissions
  const { data: permissions, isLoading: permissionsLoading } = useQuery({
    queryKey: ['userPermissions'],
    queryFn: checkUserPermissions,
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
  });

  useEffect(() => {
    if (!isModalOpen) {
      newUserForm.reset({
        name: '',
        email: '',
        password: '',
        role: 'Convidado',
      });
    }
  }, [isModalOpen, newUserForm]);

  const { data: users = [], isLoading, refetch } = useQuery({
    queryKey: ['users'],
    queryFn: async () => {
      console.log('🔄 Users.tsx: Executando query getUsers...');
      const result = await getUsers();
      console.log('📊 Users.tsx: Query result:', result);
      console.log('📊 Users.tsx: Number of users returned:', result.length);
      return result;
    },
    refetchOnWindowFocus: false,
    staleTime: 0,
    gcTime: 0,
    retry: 2,
    enabled: !permissionsLoading, // Only fetch users after permissions are loaded
  });

  useEffect(() => {
    console.log("📋 Users.tsx: Fetched users data:", users);
    console.log("📋 Users.tsx: Users count:", users.length);
    if (users.length > 0) {
      console.log("📋 Users.tsx: First user:", users[0]);
    }
  }, [users]);

  // Force cache invalidation and refetch on component mount
  useEffect(() => {
    if (permissions?.canViewAllUsers) {
      console.log("🔄 Users.tsx: Component mounted with admin permissions, invalidating cache and refetching...");
      queryClient.invalidateQueries({ queryKey: ['users'] });
      setTimeout(() => {
        console.log("🔄 Users.tsx: Delayed refetch triggered");
        refetch();
      }, 100);
    }
  }, [permissions, queryClient, refetch]);

  const createUserMutation = useMutation({
    mutationFn: (data: NewUserFormValues) => {
      console.log('➕ Users.tsx: Creating user with data:', data);
      return createUser(data.email, data.password, {
        name: data.name,
        role: data.role,
        status: 'Ativo',
      });
    },
    onSuccess: () => {
      console.log('✅ Users.tsx: User created successfully, invalidating cache...');
      queryClient.invalidateQueries({ queryKey: ['users'] });
      toast.success('Usuário adicionado com sucesso!');
      setIsModalOpen(false);
      newUserForm.reset();
      setTimeout(() => {
        console.log("Post-creation refetch triggered");
        refetch();
      }, 500);
    },
    onError: (error) => {
      console.error('❌ Users.tsx: Error creating user:', error);
      toast.error(`Erro ao adicionar usuário: ${error instanceof Error ? error.message : 'Verifique se o email já está em uso.'}`);
    }
  });

  const updateUserMutation = useMutation({
    mutationFn: (data: { id: string, userData: Partial<UserType> & { password?: string } }) => {
      return updateUser(data.id, data.userData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      toast.success('Usuário atualizado com sucesso!');
      setIsEditModalOpen(false);
      setTimeout(() => {
        console.log("Post-update refetch triggered");
        refetch();
      }, 500);
    },
    onError: (error) => {
      console.error('Error updating user:', error);
      toast.error(`Erro ao atualizar usuário: ${error instanceof Error ? error.message : 'Tente novamente mais tarde.'}`);
    }
  });

  const deleteUserMutation = useMutation({
    mutationFn: (id: string) => {
      return deleteUser(id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      toast.success('Usuário removido com sucesso!');
      setUserToDelete(null);
      setTimeout(() => {
        console.log("Post-deletion refetch triggered");
        refetch();
      }, 500);
    },
    onError: (error) => {
      console.error('Error deleting user:', error);
      toast.error(`Erro ao remover usuário: ${error instanceof Error ? error.message : 'Tente novamente mais tarde.'}`);
    }
  });

  const handleCreateUser = (data: NewUserFormValues) => {
    console.log("Creating user with data:", data);
    createUserMutation.mutate(data);
  };

  const handleEdit = (user: UserType) => {
    console.log("Editing user:", user);
    setSelectedUser(user);
    setIsEditModalOpen(true);
  };

  const handleDelete = (user: UserType) => {
    console.log("Preparing to delete user:", user);
    setUserToDelete(user);
  };

  const confirmDelete = () => {
    if (userToDelete?.id) {
      console.log("Confirming delete for user ID:", userToDelete.id);
      deleteUserMutation.mutate(userToDelete.id);
    }
  };

  const handleUpdateUser = (userData: Partial<UserType> & { password?: string }) => {
    if (selectedUser?.id) {
      console.log("Updating user ID:", selectedUser.id);
      updateUserMutation.mutate({
        id: selectedUser.id,
        userData
      });
    }
  };

  const columns = [
    {
      header: 'Nome',
      accessor: 'name',
      isSortable: true,
      cell: (value: string, row: any) => (
        <div className="flex items-center">
          <div className="h-8 w-8 bg-gray-200 rounded-full flex items-center justify-center mr-3">
            {row.role === 'Administrador' ? (
              <Shield size={16} className="text-primary-600" />
            ) : (
              <User size={16} className="text-gray-600" />
            )}
          </div>
          <span className="font-medium">{value}</span>
        </div>
      )
    },
    {
      header: 'E-mail',
      accessor: 'email',
      cell: (value: string) => (
        <div className="flex items-center">
          <Mail size={16} className="mr-2 text-gray-500" />
          <span>{value}</span>
        </div>
      )
    },
    {
      header: 'Função',
      accessor: 'role',
      cell: (value: string) => (
        <div className="flex items-center justify-center">
          <span className={`
            inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
            ${value === 'Administrador' ? 'bg-primary-100 text-primary-800' : 'bg-gray-100 text-gray-800'}
          `}>
            {value === 'Administrador' ? <Shield size={12} className="mr-1" /> : <User size={12} className="mr-1" />}
            {value}
          </span>
        </div>
      ),
      align: 'center' as const,
    },
    {
      header: 'Status',
      accessor: 'status',
      cell: (value: string) => (
        <div className="flex items-center justify-center">
          <span className={`
            inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
            ${value === 'Ativo' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}
          `}>
            <span className={`h-1.5 w-1.5 rounded-full mr-1 ${value === 'Ativo' ? 'bg-green-600' : 'bg-red-600'}`}></span>
            {value}
          </span>
        </div>
      ),
      align: 'center' as const,
    },
    {
      header: 'Último Login',
      accessor: 'last_login',
      align: 'center' as const,
    },
  ];

  const filters = [
    {
      name: 'Função',
      options: [
        { value: 'Administrador', label: 'Administrador' },
        { value: 'Convidado', label: 'Convidado' },
      ],
      onFilter: (value: string) => {
        console.log('Filtering by role:', value);
      },
    },
    {
      name: 'Status',
      options: [
        { value: 'Ativo', label: 'Ativo' },
        { value: 'Inativo', label: 'Inativo' },
      ],
      onFilter: (value: string) => {
        console.log('Filtering by status:', value);
      },
    },
  ];

  const usersWithActions = users.map(user => ({
    ...user,
    onEdit: permissions?.canEditUsers ? () => handleEdit(user) : undefined,
    onDelete: permissions?.canDeleteUsers ? () => handleDelete(user) : undefined
  }));

  console.log("🎯 Users.tsx: Final users with actions:", usersWithActions.length);
  console.log("🔑 Users.tsx: User permissions:", permissions);

  if (permissionsLoading) {
    return (
      <div className="flex h-screen bg-gray-50">
        <Sidebar />
        <div className={cn(
          "flex-1 flex flex-col transition-all duration-300 overflow-hidden",
          isMobile ? "ml-0" : (collapsed ? "ml-12" : "ml-64")
        )}>
          <div className="flex-1 flex items-center justify-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          </div>
        </div>
      </div>
    );
  }

  // Show access denied message for non-admin users
  if (!permissions?.canViewAllUsers) {
    return (
      <div className="flex h-screen bg-gray-50">
        <Sidebar />
        <div className={cn(
          "flex-1 flex flex-col transition-all duration-300 overflow-hidden",
          isMobile ? "ml-0" : (collapsed ? "ml-12" : "ml-64")
        )}>
          <div className="flex-1 overflow-y-auto pb-10">
            <div className="page-container">
              <Header
                title="Gestão de Usuários"
                subtitle="Gerencie os usuários do sistema"
              />
              <div className="mt-6">
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    Você não tem permissão para acessar a gestão de usuários. Esta funcionalidade está disponível apenas para administradores.
                  </AlertDescription>
                </Alert>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar />

      <div className={cn(
        "flex-1 flex flex-col transition-all duration-300 overflow-hidden",
        isMobile ? "ml-0" : (collapsed ? "ml-12" : "ml-64")
      )}>
        <div className="flex-1 overflow-y-auto pb-10">
          <div className="page-container">
            <Header
              title="Gestão de Usuários"
              subtitle="Gerencie os usuários do sistema"
            />

            <div className="mt-6 animate-enter">
              <DataTable
                columns={columns}
                data={usersWithActions}
                enableSearch={true}
                searchPlaceholder="Pesquisar usuários..."
                enablePagination={true}
                filters={filters}
                actionLabel="Ações"
                isLoading={isLoading}
                customControls={
                  permissions?.canCreateUsers ? (
                    <div className="flex items-center gap-3">
                      <button
                        onClick={() => setIsModalOpen(true)}
                        className="flex items-center gap-2 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-600 transition-colors"
                      >
                        <UserPlus size={18} />
                        <span>Adicionar Usuário</span>
                      </button>
                    </div>
                  ) : undefined
                }
              />
            </div>

            {permissions?.canCreateUsers && (
              <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
                <DialogContent className="sm:max-w-md">
                  <DialogHeader>
                    <DialogTitle>Adicionar Novo Usuário</DialogTitle>
                  </DialogHeader>

                  <Form {...newUserForm}>
                    <form onSubmit={newUserForm.handleSubmit(handleCreateUser)} className="space-y-4">
                      
                      <FormField
                        control={newUserForm.control}
                        name="name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Nome <span className="text-red-500">*</span></FormLabel>
                            <FormControl>
                              <Input {...field} placeholder="Nome completo" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={newUserForm.control}
                        name="email"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>E-mail <span className="text-red-500">*</span></FormLabel>
                            <FormControl>
                              <Input {...field} type="email" placeholder="<EMAIL>" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={newUserForm.control}
                        name="password"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Senha <span className="text-red-500">*</span></FormLabel>
                            <FormControl>
                              <Input {...field} type="password" placeholder="Mínimo 6 caracteres" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={newUserForm.control}
                        name="role"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Função <span className="text-red-500">*</span></FormLabel>
                            <select
                              {...field}
                              className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-200 focus:border-primary-300"
                            >
                              <option value="Convidado">Convidado</option>
                              <option value="Administrador">Administrador</option>
                            </select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <DialogFooter className="pt-3">
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => setIsModalOpen(false)}
                        >
                          Cancelar
                        </Button>
                        <Button
                          type="submit"
                          disabled={createUserMutation.isPending}
                        >
                          {createUserMutation.isPending ? 'Salvando...' : 'Salvar'}
                        </Button>
                      </DialogFooter>
                    </form>
                  </Form>
                </DialogContent>
              </Dialog>
            )}

            {permissions?.canEditUsers && (
              <EditUserModal
                isOpen={isEditModalOpen}
                onClose={() => setIsEditModalOpen(false)}
                user={selectedUser}
                onSave={handleUpdateUser}
              />
            )}

            {permissions?.canDeleteUsers && (
              <AlertDialog open={userToDelete !== null} onOpenChange={(open) => !open && setUserToDelete(null)}>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Excluir Usuário</AlertDialogTitle>
                    <AlertDialogDescription>
                      Tem certeza que deseja excluir o usuário "{userToDelete?.name}"?
                      Esta ação não pode ser desfeita.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancelar</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={confirmDelete}
                      className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
                    >
                      {deleteUserMutation.isPending ? 'Excluindo...' : 'Excluir'}
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Users;
