
import React from 'react';
import { 
  FileIcon, 
  FileTextIcon, 
  ImageIcon, 
  FolderIcon, 
  Trash2Icon, 
  DownloadIcon, 
  ExternalLinkIcon 
} from 'lucide-react';
import { 
  Card, 
  CardContent, 
  CardFooter 
} from '@/components/ui/card';
import { 
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger 
} from '@/components/ui/tooltip';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { formatDistanceToNow } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { cn } from '@/lib/utils';
import { DocumentFile } from '@/types';

interface FileCardProps {
  file: DocumentFile;
  onOpen: () => void;
  onDelete: () => void;
  getFileUrl: (path: string) => string;
}

const FileCard: React.FC<FileCardProps> = ({ file, onOpen, onDelete, getFileUrl }) => {
  const getIcon = () => {
    if (file.isFolder) {
      return <FolderIcon className="w-12 h-12 text-blue-500" />;
    }
    
    const mimeType = file.metadata.mimetype;
    
    if (mimeType.includes('pdf')) {
      return <FileTextIcon className="w-12 h-12 text-red-500" />;
    } else if (mimeType.includes('image')) {
      return <ImageIcon className="w-12 h-12 text-green-500" />;
    } else {
      return <FileIcon className="w-12 h-12 text-gray-500" />;
    }
  };

  const fileSize = () => {
    if (file.isFolder) return '';
    
    const size = file.metadata.size;
    if (size < 1024) return `${size} B`;
    if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`;
    return `${(size / (1024 * 1024)).toFixed(1)} MB`;
  };

  const createdAt = new Date(file.created_at);
  const formattedDate = formatDistanceToNow(createdAt, { 
    addSuffix: true,
    locale: ptBR
  });

  return (
    <Card className={cn(
      "w-full overflow-hidden transition-all duration-200 cursor-pointer hover:shadow-md",
      file.isFolder ? "border-blue-200" : ""
    )}>
      <CardContent className="p-4" onClick={onOpen}>
        <div className="flex flex-col items-center justify-center">
          {getIcon()}
          <h3 className="mt-2 text-sm font-medium text-center text-ellipsis overflow-hidden w-full">
            {file.name}
          </h3>
          {!file.isFolder && (
            <p className="text-xs text-gray-500 mt-1">{fileSize()}</p>
          )}
        </div>
      </CardContent>
      <CardFooter className="bg-gray-50 flex items-center justify-between p-2">
        <span className="text-xs text-gray-500">{formattedDate}</span>
        
        <TooltipProvider>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <span className="text-gray-500">•••</span>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Opções</p>
                  </TooltipContent>
                </Tooltip>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={onOpen}>
                {file.isFolder ? (
                  <>
                    <FolderIcon className="mr-2 h-4 w-4" />
                    <span>Abrir</span>
                  </>
                ) : (
                  <>
                    <ExternalLinkIcon className="mr-2 h-4 w-4" />
                    <span>Visualizar</span>
                  </>
                )}
              </DropdownMenuItem>
              
              {!file.isFolder && (
                <DropdownMenuItem 
                  onClick={(e) => {
                    e.stopPropagation();
                    window.open(getFileUrl(file.fullPath), '_blank');
                  }}
                >
                  <DownloadIcon className="mr-2 h-4 w-4" />
                  <span>Baixar</span>
                </DropdownMenuItem>
              )}
              
              <DropdownMenuSeparator />
              
              <DropdownMenuItem 
                onClick={(e) => {
                  e.stopPropagation();
                  onDelete();
                }}
                className="text-red-600"
              >
                <Trash2Icon className="mr-2 h-4 w-4" />
                <span>Excluir</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </TooltipProvider>
      </CardFooter>
    </Card>
  );
};

export default FileCard;
