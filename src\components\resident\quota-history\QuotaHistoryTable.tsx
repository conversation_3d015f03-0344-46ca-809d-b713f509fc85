
import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Download,
  CheckCircle,
  Clock,
  AlertTriangle
} from 'lucide-react';
import { QuotaHistory } from '@/types';

interface QuotaHistoryTableProps {
  quotas: QuotaHistory[];
  onDownloadReceipt?: (receipt: string) => void;
}

const QuotaHistoryTable: React.FC<QuotaHistoryTableProps> = ({ 
  quotas, 
  onDownloadReceipt 
}) => {
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'paid':
        return <Badge variant="default" className="bg-green-100 text-green-800">Pago</Badge>;
      case 'pending':
        return <Badge variant="default" className="bg-yellow-100 text-yellow-800">Pendente</Badge>;
      case 'overdue':
        return <Badge variant="destructive">Em Atraso</Badge>;
      default:
        return <Badge variant="secondary">Desconhecido</Badge>;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'paid':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'overdue':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      default:
        return null;
    }
  };

  const getMonthName = (month: number) => {
    const months = [
      'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
      'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
    ];
    return months[month - 1];
  };

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Período</TableHead>
            <TableHead>Valor</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Vencimento</TableHead>
            <TableHead>Pagamento</TableHead>
            <TableHead>Multa</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {quotas.map((quota) => (
            <TableRow key={quota.id}>
              <TableCell className="font-medium">
                <div className="flex items-center">
                  {getStatusIcon(quota.status)}
                  <span className="ml-2">
                    {getMonthName(quota.month)}/{quota.year}
                  </span>
                </div>
              </TableCell>
              <TableCell>{quota.amount.toLocaleString()} Kz</TableCell>
              <TableCell>{getStatusBadge(quota.status)}</TableCell>
              <TableCell>{quota.dueDate.toLocaleDateString('pt-BR')}</TableCell>
              <TableCell>
                {quota.paidDate ? quota.paidDate.toLocaleDateString('pt-BR') : '-'}
              </TableCell>
              <TableCell>
                {quota.fine ? (
                  <span className={`font-medium ${quota.fineStatus === 'Regularizada' ? 'text-green-600 line-through' : 'text-red-600'}`}>
                    {quota.fineStatus === 'Regularizada' && <CheckCircle className="inline w-3 h-3 mr-1" />}
                    {quota.fine.toLocaleString()} Kz
                  </span>
                ) : (
                  '-'
                )}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

export default QuotaHistoryTable;
