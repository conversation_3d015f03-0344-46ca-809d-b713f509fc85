
import React from 'react';

type PasswordStrengthProps = {
  password: string;
};

export const PasswordStrengthIndicator: React.FC<PasswordStrengthProps> = ({ password }) => {
  if (!password) return null;

  const getPasswordStrength = (password: string): { strength: 'weak' | 'medium' | 'strong', text: string, color: string } => {
    const hasLowerCase = /[a-z]/.test(password);
    const hasUpperCase = /[A-Z]/.test(password);
    const hasNumber = /[0-9]/.test(password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);
    const isLongEnough = password.length >= 8;

    const criteria = [hasLowerCase, hasUpperCase, hasNumber, hasSpecialChar, isLongEnough];
    const metCriteria = criteria.filter(Boolean).length;

    if (metCriteria <= 2) {
      return { strength: 'weak', text: 'Fraca', color: 'bg-red-500' };
    } else if (metCriteria <= 4) {
      return { strength: 'medium', text: '<PERSON><PERSON><PERSON>', color: 'bg-yellow-500' };
    } else {
      return { strength: 'strong', text: 'Forte', color: 'bg-green-500' };
    }
  };

  const { strength, text, color } = getPasswordStrength(password);

  return (
    <div className="mt-1">
      <div className="flex items-center space-x-2">
        <div className="h-1 flex-1 bg-gray-200 rounded-full overflow-hidden">
          <div 
            className={`h-full ${color}`} 
            style={{ 
              width: strength === 'weak' ? '33%' : strength === 'medium' ? '66%' : '100%' 
            }}
          ></div>
        </div>
        <span className="text-xs text-gray-500">{text}</span>
      </div>
    </div>
  );
};
