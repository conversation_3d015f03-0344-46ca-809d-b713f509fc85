
import React, { useState } from 'react';
import { format, parse, isValid } from 'date-fns';
import { CalendarIcon } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';

interface PaymentDateModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (date: string) => void;
  isLoading?: boolean;
  quotaInfo?: {
    morador: string;
    mes: number;
    ano: number;
    valor: number;
  };
}

const PaymentDateModal: React.FC<PaymentDateModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  isLoading = false,
  quotaInfo
}) => {
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [dateInput, setDateInput] = useState<string>(format(new Date(), 'dd/MM/yyyy'));

  const handleConfirm = () => {
    const formattedDate = format(selectedDate, 'yyyy-MM-dd');
    onConfirm(formattedDate);
  };

  const handleDateInputChange = (value: string) => {
    setDateInput(value);

    // Try to parse the input date in dd/MM/yyyy format
    const parsedDate = parse(value, 'dd/MM/yyyy', new Date());

    // If the parsed date is valid and not in the future, update selectedDate
    if (isValid(parsedDate) && parsedDate <= new Date()) {
      setSelectedDate(parsedDate);
    }
  };

  const handleDateSelect = (date: Date | undefined) => {
    if (date) {
      setSelectedDate(date);
      setDateInput(format(date, 'dd/MM/yyyy'));
    }
  };

  const getMonthName = (month: number) => {
    const months = [
      'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
      'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
    ];
    return months[month - 1];
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Liquidar Quota</DialogTitle>
          <DialogDescription>
            {quotaInfo && (
              <>
                Selecione a data de pagamento para a quota de{' '}
                <strong>{quotaInfo.morador}</strong> referente a{' '}
                <strong>{getMonthName(quotaInfo.mes)}/{quotaInfo.ano}</strong>
                {' '}no valor de <strong>{quotaInfo.valor.toLocaleString()} Kz</strong>.
              </>
            )}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="flex flex-col space-y-2">
            <label className="text-sm font-medium">Data de Pagamento</label>

            {/* Editable Date Input */}
            <Input
              type="text"
              placeholder="dd/mm/aaaa"
              value={dateInput}
              onChange={(e) => handleDateInputChange(e.target.value)}
              className="w-full"
            />

            {/* Calendar Picker */}
            <div className="flex items-center space-x-2">
              <span className="text-xs text-muted-foreground">ou selecione no calendário:</span>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-8 w-8 p-0"
                  >
                    <CalendarIcon className="h-4 w-4" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent
                  className="w-auto p-0"
                  align="center"
                  side="bottom"
                  sideOffset={8}
                  avoidCollisions={true}
                  collisionPadding={16}
                >
                  <Calendar
                    mode="single"
                    selected={selectedDate}
                    onSelect={handleDateSelect}
                    disabled={(date) => date > new Date()}
                    initialFocus
                    className="pointer-events-auto"
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isLoading}>
            Cancelar
          </Button>
          <Button onClick={handleConfirm} disabled={isLoading}>
            {isLoading ? 'Processando...' : 'Confirmar Pagamento'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default PaymentDateModal;
