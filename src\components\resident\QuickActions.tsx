import React from 'react';
import { Link } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  CreditCard,
  FileText,
  MessageSquare,
  User,
  Download,
  Bell,
  Calendar,
  HelpCircle
} from 'lucide-react';

interface QuickAction {
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  href: string;
  color: string;
  bgColor: string;
}

const quickActions: QuickAction[] = [
  {
    title: 'Histórico de Quotas',
    description: 'Ver todas as quotas e comprovantes',
    icon: CreditCard,
    href: '/resident/quotas',
    color: 'text-blue-500',
    bgColor: 'bg-blue-100'
  },
  {
    title: 'Documentos',
    description: 'Comprovantes e regulamentos',
    icon: FileText,
    href: '/resident/documents',
    color: 'text-green-500',
    bgColor: 'bg-green-100'
  },
  {
    title: 'Comunicados',
    description: 'Avisos da administração',
    icon: Message<PERSON><PERSON>re,
    href: '/resident/communications',
    color: 'text-purple-500',
    bgColor: 'bg-purple-100'
  },
  {
    title: 'Meu Perfil',
    description: '<PERSON><PERSON> pessoais e preferências',
    icon: User,
    href: '/resident/profile',
    color: 'text-orange-500',
    bgColor: 'bg-orange-100'
  }
];

const QuickActions: React.FC = () => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-semibold">Acesso Rápido</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          {quickActions.map((action) => {
            const IconComponent = action.icon;
            
            return (
              <Link
                key={action.title}
                to={action.href}
                className="flex items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors group"
              >
                <div className={`h-10 w-10 rounded-lg ${action.bgColor} flex items-center justify-center mr-3 group-hover:scale-105 transition-transform`}>
                  <IconComponent className={`h-5 w-5 ${action.color}`} />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-800 group-hover:text-gray-900">
                    {action.title}
                  </p>
                  <p className="text-xs text-gray-500 truncate">
                    {action.description}
                  </p>
                </div>
              </Link>
            );
          })}
        </div>

        {/* Ações Adicionais */}
        <div className="mt-6 pt-4 border-t border-gray-200">
          <h4 className="text-sm font-medium text-gray-700 mb-3">Outras Ações</h4>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            <button className="flex items-center p-3 text-left bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
              <Download className="h-4 w-4 text-gray-500 mr-3" />
              <div>
                <p className="text-sm font-medium text-gray-800">Baixar Comprovantes</p>
                <p className="text-xs text-gray-500">Últimas quotas pagas</p>
              </div>
            </button>

            <button className="flex items-center p-3 text-left bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
              <Bell className="h-4 w-4 text-gray-500 mr-3" />
              <div>
                <p className="text-sm font-medium text-gray-800">Configurar Notificações</p>
                <p className="text-xs text-gray-500">Lembretes e avisos</p>
              </div>
            </button>

            <button className="flex items-center p-3 text-left bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
              <Calendar className="h-4 w-4 text-gray-500 mr-3" />
              <div>
                <p className="text-sm font-medium text-gray-800">Próximas Reuniões</p>
                <p className="text-xs text-gray-500">Agenda do condomínio</p>
              </div>
            </button>

            <button className="flex items-center p-3 text-left bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
              <HelpCircle className="h-4 w-4 text-gray-500 mr-3" />
              <div>
                <p className="text-sm font-medium text-gray-800">Suporte</p>
                <p className="text-xs text-gray-500">Dúvidas e contato</p>
              </div>
            </button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default QuickActions;
