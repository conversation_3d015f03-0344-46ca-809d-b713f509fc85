
import React, { useState } from 'react';
import Sidebar from '@/components/layout/Sidebar';
import Header from '@/components/layout/Header';
import DataTable from '@/components/tables/DataTable';
import EditCommitteeMemberModal from '@/components/modals/EditCommitteeMemberModal';
import { useSidebar } from '@/contexts/SidebarContext';
import { cn } from '@/lib/utils';
import { UserPlus, Mail, Phone, Calendar } from 'lucide-react';
import { toast } from 'sonner';
import { MembroComissao } from '@/types';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getMembrosComissao, createMembroComissao, updateMembroComissao, deleteMembroComissao } from '@/utils/supabase-helpers';
import { useCommitteeRoles } from '@/hooks/useCommitteeRoles';
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

const newMemberSchema = z.object({
  nome: z.string().min(1, "Nome é obrigatório"),
  cargo: z.string().min(1, "Cargo é obrigatório"),
  email: z.string().email("Email inválido").optional().or(z.literal('')),
  telefone: z.string().optional().or(z.literal('')),
  data_inicio: z.string().min(1, "Data de início é obrigatória"),
  data_fim: z.string().optional().or(z.literal('')),
});

type NewMemberFormValues = z.infer<typeof newMemberSchema>;

const CommitteeMembers = () => {
  const { collapsed, isMobile } = useSidebar();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedMember, setSelectedMember] = useState<MembroComissao | null>(null);
  const [memberToDelete, setMemberToDelete] = useState<MembroComissao | null>(null);
  const queryClient = useQueryClient();
  const { activeRoles, isLoading: isLoadingRoles } = useCommitteeRoles();

  const newMemberForm = useForm<NewMemberFormValues>({
    resolver: zodResolver(newMemberSchema),
    defaultValues: {
      nome: '',
      cargo: '',
      email: '',
      telefone: '',
      data_inicio: new Date().toISOString().split('T')[0],
      data_fim: '',
    }
  });

  const { data: membrosComissao = [], isLoading } = useQuery({
    queryKey: ['membros_comissao'],
    queryFn: getMembrosComissao
  });

  const createMemberMutation = useMutation({
    mutationFn: (data: NewMemberFormValues) => {
      return createMembroComissao({
        nome: data.nome,
        cargo: data.cargo,
        email: data.email || undefined,
        telefone: data.telefone || undefined,
        data_inicio: data.data_inicio,
        data_fim: data.data_fim || undefined,
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['membros_comissao'] });
      toast.success('Membro da comissão adicionado com sucesso!');
      setIsModalOpen(false);
      newMemberForm.reset();
    },
    onError: (error) => {
      console.error('Error creating committee member:', error);
      toast.error('Erro ao adicionar membro da comissão.');
    }
  });

  const updateMemberMutation = useMutation({
    mutationFn: (data: { id: string, membro: Partial<MembroComissao> }) => {
      return updateMembroComissao(data.id, data.membro);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['membros_comissao'] });
      toast.success('Membro da comissão atualizado com sucesso!');
      setIsEditModalOpen(false);
    },
    onError: (error) => {
      console.error('Error updating committee member:', error);
      toast.error('Erro ao atualizar membro da comissão.');
    }
  });

  const deleteMemberMutation = useMutation({
    mutationFn: (id: string) => {
      return deleteMembroComissao(id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['membros_comissao'] });
      toast.success('Membro da comissão removido com sucesso!');
      setMemberToDelete(null);
    },
    onError: (error) => {
      console.error('Error deleting committee member:', error);
      toast.error('Erro ao remover membro da comissão.');
    }
  });

  const handleCreateMember = (data: NewMemberFormValues) => {
    createMemberMutation.mutate(data);
  };

  const handleEdit = (member: MembroComissao) => {
    setSelectedMember(member);
    setIsEditModalOpen(true);
  };

  const handleDelete = (member: MembroComissao) => {
    setMemberToDelete(member);
  };

  const confirmDelete = () => {
    if (memberToDelete?.id) {
      deleteMemberMutation.mutate(memberToDelete.id);
    }
  };

  const handleUpdateMember = (memberData: Partial<MembroComissao>) => {
    if (selectedMember?.id) {
      updateMemberMutation.mutate({
        id: selectedMember.id,
        membro: memberData
      });
    }
  };

  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return '-';
    try {
      return format(new Date(dateString), 'dd/MM/yyyy', { locale: ptBR });
    } catch (error) {
      console.error('Error formatting date:', error);
      return dateString;
    }
  };

  const columns = [
    {
      header: 'Nome',
      accessor: 'nome',
      isSortable: true
    },
    {
      header: 'Função',
      accessor: 'cargo',
      isSortable: true,
      align: 'center' as const,
    },
    {
      header: 'E-mail',
      accessor: 'email',
      cell: (value: string) => (
        <div className="flex items-center">
          <Mail size={16} className="mr-2 text-gray-500" />
          <span>{value || '-'}</span>
        </div>
      )
    },
    {
      header: 'Telefone',
      accessor: 'telefone',
      cell: (value: string) => (
        <div className="flex items-center">
          <Phone size={16} className="mr-2 text-gray-500" />
          <span>{value || '-'}</span>
        </div>
      )
    },
    {
      header: 'Data de Início',
      accessor: 'data_inicio',
      cell: (value: string) => (
        <div className="flex items-center justify-center">
          <Calendar size={16} className="mr-2 text-gray-500" />
          <span>{formatDate(value)}</span>
        </div>
      ),
      align: 'center' as const,
    },
    {
      header: 'Data de Término',
      accessor: 'data_fim',
      cell: (value: string) => (
        <div className="flex items-center justify-center">
          <Calendar size={16} className="mr-2 text-gray-500" />
          <span>{formatDate(value)}</span>
        </div>
      ),
      align: 'center' as const,
    },
  ];

  const filters = [
    {
      name: 'Função',
      options: activeRoles.map(role => ({
        value: role.name,
        label: role.name
      })),
      onFilter: (value: string) => {
        console.log('Filtering by role:', value);
      },
    },
  ];



  // Add an onEdit and onDelete method to each row for the DataTable to use
  const membrosWithActions = membrosComissao.map(membro => ({
    ...membro,
    onEdit: () => handleEdit(membro),
    onDelete: () => handleDelete(membro)
  }));

  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar />

      <div className={cn(
        "flex-1 flex flex-col transition-all duration-300 overflow-hidden",
        isMobile ? "ml-0" : (collapsed ? "ml-12" : "ml-64")
      )}>
        <div className="flex-1 overflow-y-auto pb-10">
          <div className="page-container">
            <Header
              title="Membros da Comissão"
              subtitle="Gerencie os membros da comissão do condomínio"
            />

            <div className="mt-6 animate-enter">
              <DataTable
                columns={columns}
                data={membrosWithActions}
                enableSearch={true}
                searchPlaceholder="Pesquisar membros..."
                enablePagination={true}
                filters={filters}
                actionLabel="Ações"
                isLoading={isLoading}
                customControls={
                  <div className="flex items-center gap-3">
                    <button
                      onClick={() => setIsModalOpen(true)}
                      className="flex items-center gap-2 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-600 transition-colors"
                    >
                      <UserPlus size={18} />
                      <span>Adicionar Membro</span>
                    </button>
                  </div>
                }
              />
            </div>

            {/* Add Committee Member Modal */}
            <Dialog open={isModalOpen} onOpenChange={(open) => {
              setIsModalOpen(open);
              if (!open) {
                newMemberForm.reset({
                  nome: '',
                  cargo: '',
                  email: '',
                  telefone: '',
                  data_inicio: new Date().toISOString().split('T')[0],
                  data_fim: '',
                });
              }
            }}>
              <DialogContent className="sm:max-w-md">
                <DialogHeader>
                  <DialogTitle>Adicionar Novo Membro da Comissão</DialogTitle>
                </DialogHeader>

                <Form {...newMemberForm}>
                  <form onSubmit={newMemberForm.handleSubmit(handleCreateMember)} className="space-y-4">
                    <FormField
                      control={newMemberForm.control}
                      name="nome"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Nome <span className="text-red-500">*</span></FormLabel>
                          <FormControl>
                            <Input {...field} placeholder="Nome completo" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={newMemberForm.control}
                      name="cargo"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Função <span className="text-red-500">*</span></FormLabel>
                          <FormControl>
                            <Select value={field.value} onValueChange={field.onChange} disabled={isLoadingRoles}>
                              <SelectTrigger>
                                <SelectValue placeholder="Selecione uma função" />
                              </SelectTrigger>
                              <SelectContent>
                                {activeRoles.map((role) => (
                                  <SelectItem key={role.id} value={role.name}>
                                    {role.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={newMemberForm.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>E-mail</FormLabel>
                          <FormControl>
                            <Input {...field} type="email" placeholder="<EMAIL>" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={newMemberForm.control}
                      name="telefone"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Telefone</FormLabel>
                          <FormControl>
                            <Input {...field} type="tel" placeholder="+244 123 456 789" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={newMemberForm.control}
                        name="data_inicio"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Data de Início <span className="text-red-500">*</span></FormLabel>
                            <FormControl>
                              <Input {...field} type="date" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={newMemberForm.control}
                        name="data_fim"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Data de Término</FormLabel>
                            <FormControl>
                              <Input {...field} type="date" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <DialogFooter className="pt-3">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => setIsModalOpen(false)}
                      >
                        Cancelar
                      </Button>
                      <Button
                        type="submit"
                        disabled={createMemberMutation.isPending}
                      >
                        {createMemberMutation.isPending ? 'Salvando...' : 'Salvar'}
                      </Button>
                    </DialogFooter>
                  </form>
                </Form>
              </DialogContent>
            </Dialog>

            {/* Edit Committee Member Modal */}
            <EditCommitteeMemberModal
              isOpen={isEditModalOpen}
              onClose={() => setIsEditModalOpen(false)}
              member={selectedMember}
              onSave={handleUpdateMember}
            />

            {/* Delete Committee Member Confirmation */}
            <AlertDialog open={memberToDelete !== null} onOpenChange={(open) => !open && setMemberToDelete(null)}>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Excluir Membro da Comissão</AlertDialogTitle>
                  <AlertDialogDescription>
                    Tem certeza que deseja excluir o membro "{memberToDelete?.nome}"?
                    Esta ação não pode ser desfeita.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancelar</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={confirmDelete}
                    className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
                  >
                    {deleteMemberMutation.isPending ? 'Excluindo...' : 'Excluir'}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CommitteeMembers;
