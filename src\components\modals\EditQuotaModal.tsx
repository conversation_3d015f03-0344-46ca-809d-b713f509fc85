import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>T<PERSON>le, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

import { AlertCircle, FileText, Upload, X } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from 'sonner';
import { Quota, Morador } from '@/types';
import { getMoradores, uploadComprovativo } from '@/utils/supabase-helpers';
import { useQuery } from '@tanstack/react-query';
import { useSettings } from '@/hooks/useSettings';
import { <PERSON><PERSON>, AlertT<PERSON>le, AlertDescription } from "@/components/ui/alert";
import { useQuotas } from '@/hooks/useQuotas';
import { calculateFineAmount, calculateSituacaoMulta, MultaConfig } from '@/utils/quota-calculations';

interface EditQuotaModalProps {
  isOpen: boolean;
  onClose: () => void;
  quota: Partial<Quota> | null;
  onSave: (quotaData: Partial<Quota>) => void;
  isLoading?: boolean;
}

const EditQuotaModal: React.FC<EditQuotaModalProps> = ({
  isOpen,
  onClose,
  quota,
  onSave,
  isLoading = false
}) => {
  const { data: moradores = [] } = useQuery({
    queryKey: ['moradores'],
    queryFn: getMoradores
  });

  const { quotas, checkQuotaExists } = useQuotas();
  const { getConfigValue } = useSettings();
  const defaultQuotaValue = parseFloat(getConfigValue('valor_quota')) || 2000;
  const defaultMultaValue = parseFloat(getConfigValue('valor_multa_atraso')) || 1000;
  const diasAtrasoMulta = parseInt(getConfigValue('dias_atraso_multa') || '0');

  // Get the configured day for the deadline
  const limiteDia = parseInt(getConfigValue('data_limite_pagamento') || '10', 10);

  // Calculate the deadline date based on the quota month/year and configured day
  const calculateDataLimite = (mes: number, ano: number) => {
    const data = new Date(Date.UTC(ano, mes - 1, limiteDia, 12, 0, 0));
    return data.toISOString().split('T')[0];
  };

  // Calculate multa based on data vencimento - FIXED VERSION
  const calculateMultaForQuota = (dataVencimento: string, dataPagamento: string | null = null) => {
    const config: MultaConfig = {
      valorMulta: defaultMultaValue,
      diasAtrasoMulta: diasAtrasoMulta
    };
    
    console.log('🧮 Calculando multa para quota:', { dataVencimento, dataPagamento, config });
    
    return calculateFineAmount(dataPagamento, dataVencimento, config);
  };

  const [selectedMorador, setSelectedMorador] = useState<Morador | null>(null);
  const [multasAnteriores, setMultasAnteriores] = useState<number>(0);
  const [duplicateWarning, setDuplicateWarning] = useState<string | null>(null);
  const [comprovativoFile, setComprovativoFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);

  // Define the months array that was missing
  const months = [
    { value: 1, label: 'Janeiro' },
    { value: 2, label: 'Fevereiro' },
    { value: 3, label: 'Março' },
    { value: 4, label: 'Abril' },
    { value: 5, label: 'Maio' },
    { value: 6, label: 'Junho' },
    { value: 7, label: 'Julho' },
    { value: 8, label: 'Agosto' },
    { value: 9, label: 'Setembro' },
    { value: 10, label: 'Outubro' },
    { value: 11, label: 'Novembro' },
    { value: 12, label: 'Dezembro' }
  ];

  // Determine if this is an edit operation (existing quota) or a new quota
  const isEditMode = !!quota?.id;

  // Get current year and create a list of years (current year - 1, current year, current year + 1)
  const currentYear = new Date().getFullYear();
  const years = [currentYear - 1, currentYear, currentYear + 1];

  const form = useForm<Partial<Quota>>({
    defaultValues: {
      morador_id: quota?.morador_id || '',
      mes: quota?.mes || new Date().getMonth() + 1,
      ano: quota?.ano || new Date().getFullYear(),
      valor: quota?.valor || defaultQuotaValue,
      status: quota?.status || 'Não Pago',
      data_vencimento: quota?.data_vencimento || '',
      data_pagamento: quota?.data_pagamento || '',
      multa: quota?.multa || 0,
      numero_multas: quota?.numero_multas || 0,
      situacao: quota?.situacao || 'Não Regularizada',
      comprovativo: quota?.comprovativo || ''
    }
  });

  // Check for duplicate quota when form values change
  useEffect(() => {
    const moradorId = form.watch('morador_id');
    const mes = form.watch('mes');
    const ano = form.watch('ano');

    if (moradorId && mes && ano) {
      // Clear previous warning
      setDuplicateWarning(null);

      // Only check for duplicates if this is a new quota or if we're changing morador/mes/ano
      if (!quota?.id ||
          moradorId !== quota.morador_id ||
          mes !== quota.mes ||
          ano !== quota.ano) {

        // Check if this combination already exists
        const exists = checkQuotaExists(moradorId, mes, ano);
        if (exists) {
          setDuplicateWarning("Este morador já possui uma quota registrada para o mês selecionado.");
        }
      }
    }
  }, [form.watch('morador_id'), form.watch('mes'), form.watch('ano'), quota?.id, quota?.morador_id, quota?.mes, quota?.ano, checkQuotaExists]);

  // Update form when quota changes or when creating a new quota
  useEffect(() => {
    const currentMes = quota?.mes || new Date().getMonth() + 1;
    const currentAno = quota?.ano || new Date().getFullYear();
    const dataLimite = calculateDataLimite(currentMes, currentAno);

    console.log(`📅 Data limite calculada: ${dataLimite} para mês ${currentMes}, ano ${currentAno}, dia ${limiteDia}`);

    if (quota) {
      // Para quotas existentes, manter os valores atuais
      form.reset({
        id: quota.id,
        morador_id: quota.morador_id,
        mes: currentMes,
        ano: currentAno,
        valor: quota.valor,
        status: quota.status,
        data_vencimento: quota.data_vencimento || dataLimite,
        data_pagamento: quota.data_pagamento,
        multa: quota.multa || 0,
        numero_multas: quota.numero_multas || 0,
        situacao: quota.situacao || 'Não Regularizada',
        comprovativo: quota.comprovativo || ''
      });

      if (quota.comprovativo) {
        setPreviewUrl(quota.comprovativo);
      } else {
        setPreviewUrl(null);
      }

      const morador = moradores.find(m => m.id === quota.morador_id);
      setSelectedMorador(morador || null);

      if (morador) {
        setMultasAnteriores(morador.id === 'some-id-with-multas' ? 4000 : 0);
      }
    } else {
      // Para novas quotas, calcular multa e situação automaticamente
      const today = new Date().toISOString().split('T')[0];
      const multaCalculada = calculateMultaForQuota(dataLimite, null);
      const situacaoCalculada = calculateSituacaoMulta(multaCalculada, null, dataLimite);
      
      console.log(`🆕 Nova quota - Data limite: ${dataLimite}, Hoje: ${today}`);
      console.log(`🆕 Nova quota - Multa calculada: ${multaCalculada}, Situação: ${situacaoCalculada}`);

      form.reset({
        morador_id: '',
        mes: currentMes,
        ano: currentAno,
        valor: defaultQuotaValue,
        status: 'Não Pago',
        data_vencimento: dataLimite,
        data_pagamento: '',
        multa: multaCalculada,
        numero_multas: 0,
        situacao: situacaoCalculada,
        comprovativo: ''
      });
      setSelectedMorador(null);
      setMultasAnteriores(0);
      setPreviewUrl(null);
      setComprovativoFile(null);
    }
  }, [quota, form, defaultQuotaValue, moradores, limiteDia, defaultMultaValue, diasAtrasoMulta]);

  // Update selected morador when morador_id changes
  useEffect(() => {
    const moradorId = form.watch('morador_id');
    if (moradorId) {
      const morador = moradores.find(m => m.id === moradorId);
      setSelectedMorador(morador || null);

      if (morador) {
        setMultasAnteriores(morador.id === 'some-id-with-multas' ? 4000 : 0);
      } else {
        setMultasAnteriores(0);
      }
    }
  }, [form.watch('morador_id'), moradores, form]);

  // Update data_vencimento and recalculate multa when mes or ano changes
  useEffect(() => {
    const mes = form.watch('mes');
    const ano = form.watch('ano');

    if (mes && ano) {
      const dataLimite = calculateDataLimite(mes, ano);
      console.log(`🔄 Atualizando data limite para ${dataLimite} (mês ${mes}, ano ${ano})`);
      
      form.setValue('data_vencimento', dataLimite);
      
      // Recalcular multa para novas quotas baseado na data atual vs data limite
      if (!isEditMode) {
        const today = new Date().toISOString().split('T')[0];
        const multaCalculada = calculateMultaForQuota(dataLimite, form.watch('data_pagamento'));
        const situacaoCalculada = calculateSituacaoMulta(multaCalculada, form.watch('data_pagamento'), dataLimite);
        
        console.log(`🔄 Recalculando - Data limite: ${dataLimite}, Hoje: ${today}`);
        console.log(`🔄 Recalculando multa: ${multaCalculada}, Situação: ${situacaoCalculada}`);
        
        form.setValue('multa', multaCalculada);
        form.setValue('situacao', situacaoCalculada);
      }
    }
  }, [form.watch('mes'), form.watch('ano'), form, limiteDia, isEditMode]);

  // Handle file selection for comprovativo
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const validTypes = ['image/jpeg', 'image/png', 'application/pdf'];
    if (!validTypes.includes(file.type)) {
      toast.error("Tipo de arquivo inválido. Por favor, envie apenas arquivos PDF, PNG ou JPG.");
      return;
    }

    const maxSize = 2 * 1024 * 1024;
    if (file.size > maxSize) {
      toast.error("Arquivo muito grande. O tamanho máximo permitido é 2MB.");
      return;
    }

    setComprovativoFile(file);

    if (file.type.startsWith('image/')) {
      const objectUrl = URL.createObjectURL(file);
      setPreviewUrl(objectUrl);
      return () => URL.revokeObjectURL(objectUrl);
    } else {
      setPreviewUrl(null);
    }
  };

  // Clear selected file
  const clearFile = () => {
    setComprovativoFile(null);
    setPreviewUrl(null);
    if (form.getValues('comprovativo')) {
      form.setValue('comprovativo', '');
    }
  };

  const handleSubmit = async (data: Partial<Quota>) => {
    if (!data.morador_id) {
      toast.error("Selecione um morador");
      return;
    }

    if (!data.valor || data.valor <= 0) {
      toast.error("O valor da quota deve ser maior que zero");
      return;
    }

    // Validação de data de pagamento removida - status é controlado via botão "Liquidar Quota"

    if (duplicateWarning) {
      toast.error(duplicateWarning);
      return;
    }

    try {
      setIsUploading(true);

      if (comprovativoFile) {
        const quotaId = quota?.id || 'new';
        const fileUrl = await uploadComprovativo(quotaId, comprovativoFile);

        if (fileUrl) {
          data.comprovativo = fileUrl;
        } else {
          toast.error("Erro ao fazer upload do comprovativo");
          setIsUploading(false);
          return;
        }
      }

      // Recalcular situacao baseada nos valores finais
      const finalSituacao = calculateSituacaoMulta(
        data.multa || 0,
        data.data_pagamento || null,
        data.data_vencimento || ''
      );
      
      data.situacao = finalSituacao;

      const quotaData = {
        ...(quota?.id ? { id: quota.id } : {}),
        morador_id: data.morador_id,
        mes: data.mes || new Date().getMonth() + 1,
        ano: data.ano || new Date().getFullYear(),
        valor: data.valor,
        // Status não é incluído - deve ser alterado via botão "Liquidar Quota"
        data_vencimento: data.data_vencimento,
        data_pagamento: data.data_pagamento || '',
        multa: data.multa || 0,
        numero_multas: data.numero_multas || 0,
        situacao: data.situacao,
        comprovativo: data.comprovativo
      };

      console.log('💾 Salvando quota:', quotaData);
      onSave(quotaData);
      setIsUploading(false);
    } catch (error) {
      console.error('❌ Erro durante submissão:', error);
      toast.error("Ocorreu um erro ao salvar. Por favor, tente novamente.");
      setIsUploading(false);
    }
  };

  // Calculate total to pay
  const totalAPagar = () => {
    const valor = form.watch('valor') || 0;
    const multa = form.watch('multa') || 0;
    return valor + multa;
  };

  // Required field indicator
  const RequiredIndicator = () => (
    <span className="text-red-500 ml-1">*</span>
  );

  // Get file name from URL
  const getFileNameFromUrl = (url: string) => {
    if (!url) return '';
    try {
      const fileName = url.split('/').pop() || 'comprovativo';
      return decodeURIComponent(fileName.replace(/^quota_.*?_\d+\./, ''));
    } catch (e) {
      return 'comprovativo';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle>{quota?.id ? 'Editar' : 'Nova'} Quota</DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto px-1">
          <div className="space-y-4">
            {duplicateWarning && (
              <Alert variant="destructive" className="mb-4">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Erro de validação</AlertTitle>
                <AlertDescription>{duplicateWarning}</AlertDescription>
              </Alert>
            )}

            <Form {...form}>
              <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="morador_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Morador
                        <RequiredIndicator />
                      </FormLabel>
                      <FormControl>
                        <Select
                          value={field.value}
                          onValueChange={(value) => {
                            field.onChange(value);
                          }}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione um morador" />
                          </SelectTrigger>
                          <SelectContent>
                            {moradores.map((morador) => (
                              <SelectItem key={morador.id} value={morador.id}>
                                {morador.nome} ({morador.apartamento})
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="mes"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Mês
                          <RequiredIndicator />
                        </FormLabel>
                        <FormControl>
                          <Select
                            value={field.value?.toString()}
                            onValueChange={(value) => field.onChange(parseInt(value, 10))}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Selecione o mês" />
                            </SelectTrigger>
                            <SelectContent>
                              {months.map((month) => (
                                <SelectItem key={month.value} value={month.value.toString()}>
                                  {month.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="ano"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Ano
                          <RequiredIndicator />
                        </FormLabel>
                        <FormControl>
                          <Select
                            value={field.value?.toString()}
                            onValueChange={(value) => field.onChange(parseInt(value, 10))}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Selecione o ano" />
                            </SelectTrigger>
                            <SelectContent>
                              {years.map((year) => (
                                <SelectItem key={year} value={year.toString()}>
                                  {year}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="valor"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Valor da Quota
                          <RequiredIndicator />
                        </FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Input
                              {...field}
                              type="number"
                              className="pl-10"
                              placeholder="Ex: 30000"
                              onChange={(e) => field.onChange(parseFloat(e.target.value))}
                              disabled={true}
                            />
                            <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">Kz</span>
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="multa"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex items-center">
                          Multa
                          {!isEditMode && form.watch('multa') > 0 && (
                            <span className="ml-2 text-xs bg-red-100 text-red-600 px-2 py-0.5 rounded-full">
                              Aplicada automaticamente
                            </span>
                          )}
                          {isEditMode && (
                            <span className="ml-2 text-xs bg-blue-100 text-blue-600 px-2 py-0.5 rounded-full">
                              Editável
                            </span>
                          )}
                        </FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Input
                              {...field}
                              type="number"
                              className="pl-10"
                              placeholder="Ex: 0"
                              onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                              disabled={!isEditMode}
                            />
                            <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">Kz</span>
                          </div>
                        </FormControl>
                        {multasAnteriores > 0 && (
                          <p className="text-xs text-gray-600 mt-1">
                            Multas anteriores: {multasAnteriores.toLocaleString('pt-AO')} Kz
                          </p>
                        )}
                        {!isEditMode && form.watch('multa') > 0 && (
                          <p className="text-xs text-red-600 mt-1">
                            Multa aplicada automaticamente por quota cadastrada após data limite.
                          </p>
                        )}
                        {isEditMode && (
                          <p className="text-xs text-blue-600 mt-1">
                            Você pode ajustar o valor da multa manualmente.
                          </p>
                        )}
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="status"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Status de Pagamento</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Input
                              value={field.value || 'Não Pago'}
                              disabled={true}
                              className="bg-gray-50"
                            />
                          </div>
                        </FormControl>
                        <p className="text-xs text-blue-600 mt-1">
                          Para alterar o status, use o botão "Liquidar Quota" na tabela
                        </p>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="data_vencimento"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Data Limite</FormLabel>
                        <FormControl>
                          <Input {...field} type="date" disabled={true} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {form.watch('status') === 'Pago' && (
                  <FormField
                    control={form.control}
                    name="data_pagamento"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Data de Pagamento
                          <RequiredIndicator />
                        </FormLabel>
                        <FormControl>
                          <Input {...field} type="date" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}

                {/* Novo campo para upload de comprovativo */}
                <FormField
                  control={form.control}
                  name="comprovativo"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center">
                        Comprovativo
                        {form.watch('status') === 'Pago' && <RequiredIndicator />}
                      </FormLabel>
                      <FormControl>
                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            <Button
                              type="button"
                              variant="outline"
                              className="flex-1"
                              onClick={() => document.getElementById('comprovativo-upload')?.click()}
                            >
                              <Upload className="h-4 w-4 mr-2" />
                              {comprovativoFile ? 'Trocar arquivo' : 'Carregar comprovativo'}
                            </Button>
                            <input
                              id="comprovativo-upload"
                              type="file"
                              className="hidden"
                              accept=".pdf,.jpg,.jpeg,.png"
                              onChange={handleFileChange}
                            />
                          </div>

                          {/* File preview or existing file display */}
                          {(comprovativoFile || previewUrl) && (
                            <div className="p-3 bg-gray-50 rounded-md border flex items-center justify-between">
                              <div className="flex items-center">
                                <FileText className="h-4 w-4 mr-2 text-blue-600" />
                                <span className="text-sm truncate max-w-[200px]">
                                  {comprovativoFile
                                    ? comprovativoFile.name
                                    : getFileNameFromUrl(previewUrl || '')}
                                </span>
                              </div>
                              <Button
                                type="button"
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8"
                                onClick={clearFile}
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            </div>
                          )}

                          {/* Preview for image files */}
                          {previewUrl && previewUrl.match(/\.(jpeg|jpg|png)(\?.*)?$/i) && (
                            <div className="mt-2">
                              <img
                                src={previewUrl}
                                alt="Comprovativo"
                                className="max-h-40 rounded-md object-contain mx-auto"
                              />
                            </div>
                          )}

                          <input
                            type="hidden"
                            {...field}
                          />
                        </div>
                      </FormControl>
                      <p className="text-xs text-gray-500">
                        Formatos permitidos: PDF, JPG, PNG. Tamanho máximo: 2MB
                      </p>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="bg-gray-50 p-3 rounded-lg border border-gray-100">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Total a Pagar:</span>
                    <span className="font-semibold">
                      {totalAPagar().toLocaleString('pt-AO')} Kz
                    </span>
                  </div>
                  <div className="flex justify-between items-center mt-2">
                    <span className="text-sm font-medium">Situação:</span>
                    <span className={`
                      inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                      ${form.watch('situacao') === 'Regularizada' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}
                    `}>
                      {form.watch('situacao')}
                    </span>
                  </div>
                </div>
              </form>
            </Form>
          </div>
        </div>

        <DialogFooter className="border-t pt-4 mt-4">
          <Button type="button" variant="outline" onClick={onClose}>
            Cancelar
          </Button>
          <Button
            onClick={form.handleSubmit(handleSubmit)}
            disabled={isLoading || isUploading || !!duplicateWarning}
          >
            {isLoading || isUploading ? 'Salvando...' : 'Salvar'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default EditQuotaModal;
