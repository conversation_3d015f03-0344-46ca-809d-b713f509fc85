
import React from 'react';
import DashboardCard from '@/components/dashboard/DashboardCard';
import {
  AlertTriangle,
  CreditCard,
  CheckCircle,
  Clock
} from 'lucide-react';
import { ResidentDashboardData } from '@/hooks/resident/useResidentDashboard';

interface DashboardCardsProps {
  currentQuota: ResidentDashboardData['currentQuota'];
  summary: ResidentDashboardData['summary'];
}

const DashboardCards: React.FC<DashboardCardsProps> = ({ currentQuota, summary }) => {
  const getStatusColor = (status: 'pending' | 'paid' | 'overdue') => {
    switch (status) {
      case 'paid':
        return 'text-green-600';
      case 'pending':
        return 'text-yellow-600';
      case 'overdue':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const getStatusText = (status: 'pending' | 'paid' | 'overdue') => {
    switch (status) {
      case 'paid':
        return 'Pago';
      case 'pending':
        return 'Pendente';
      case 'overdue':
        return 'Em Atraso';
      default:
        return 'Desconhecido';
    }
  };

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mt-6 animate-enter">
      <DashboardCard
        title="Quota Atual"
        value={currentQuota ? `${currentQuota.amount.toLocaleString()} Kz` : 'N/A'}
        description={currentQuota ? `${currentQuota.monthYear} - ${getStatusText(currentQuota.status)}` : 'Nenhuma quota encontrada'}
        icon={currentQuota?.status === 'paid' ? CheckCircle : currentQuota?.status === 'pending' ? Clock : AlertTriangle}
        iconBgColor={currentQuota?.status === 'paid' ? 'bg-green-100' : currentQuota?.status === 'pending' ? 'bg-yellow-100' : 'bg-red-100'}
        iconColor={currentQuota?.status === 'paid' ? 'text-green-500' : currentQuota?.status === 'pending' ? 'text-yellow-500' : 'text-red-500'}
      />

      <DashboardCard
        title="Total Pendente"
        value={`${summary.totalPending.toLocaleString()} Kz`}
        description="Valor em aberto"
        icon={CreditCard}
        iconBgColor="bg-blue-100"
        iconColor="text-blue-500"
      />

      <DashboardCard
        title="Multas"
        value={`${summary.totalFines.toLocaleString()} Kz`}
        description="Total de penalizações"
        icon={AlertTriangle}
        iconBgColor="bg-red-100"
        iconColor="text-red-500"
      />

      <DashboardCard
        title="Taxa de Pagamento"
        value={`${summary.paymentRate}%`}
        description="Histórico de pontualidade"
        icon={CheckCircle}
        iconBgColor="bg-green-100"
        iconColor="text-green-500"
      />
    </div>
  );
};

export default DashboardCards;
