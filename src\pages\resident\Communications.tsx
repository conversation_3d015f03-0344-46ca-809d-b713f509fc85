
import React from 'react';
import ResidentLayout from '@/components/layout/resident/ResidentLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  MessageSquare,
  Pin,
  Calendar,
  User,
  CheckCircle,
  AlertCircle,
  Info
} from 'lucide-react';
import { useResidentCommunications } from '@/hooks/resident/useResidentCommunications';

const ResidentCommunications = () => {
  const { communications, isLoading, error, markAsRead } = useResidentCommunications();

  const getPriorityColor = (prioridade: string) => {
    switch (prioridade.toLowerCase()) {
      case 'alta':
        return 'bg-red-100 text-red-800';
      case 'media':
        return 'bg-yellow-100 text-yellow-800';
      case 'baixa':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeIcon = (tipo: string) => {
    switch (tipo.toLowerCase()) {
      case 'urgente':
        return <AlertCircle className="h-4 w-4" />;
      case 'informativo':
        return <Info className="h-4 w-4" />;
      default:
        return <MessageSquare className="h-4 w-4" />;
    }
  };

  const handleMarkAsRead = async (comunicadoId: string) => {
    try {
      await markAsRead.mutateAsync(comunicadoId);
    } catch (error) {
      console.error('Erro ao marcar como lido:', error);
    }
  };

  if (isLoading) {
    return (
      <ResidentLayout
        title="Comunicados"
        subtitle="Acompanhe os avisos e informações da administração"
      >
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      </ResidentLayout>
    );
  }

  if (error) {
    return (
      <ResidentLayout
        title="Comunicados"
        subtitle="Acompanhe os avisos e informações da administração"
      >
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <p className="text-gray-600">Erro ao carregar comunicados</p>
          </div>
        </div>
      </ResidentLayout>
    );
  }

  const fixedCommunications = communications.filter(com => com.fixado);
  const regularCommunications = communications.filter(com => !com.fixado);

  return (
    <ResidentLayout
      title="Comunicados"
      subtitle="Acompanhe os avisos e informações da administração"
    >
      <div className="space-y-6 mt-6">
        {/* Comunicados Fixados */}
        {fixedCommunications.length > 0 && (
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Pin className="h-5 w-5 mr-2 text-primary" />
              Comunicados Fixados
            </h2>
            <div className="grid gap-4">
              {fixedCommunications.map((comunicado) => (
                <Card key={comunicado.id} className="border-primary/20 bg-primary/5">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <CardTitle className="text-lg flex items-center">
                        {getTypeIcon(comunicado.tipo)}
                        <span className="ml-2">{comunicado.titulo}</span>
                        {!comunicado.lida && (
                          <Badge variant="secondary" className="ml-2 bg-blue-100 text-blue-800">
                            Novo
                          </Badge>
                        )}
                      </CardTitle>
                      <div className="flex items-center space-x-2">
                        <Badge className={getPriorityColor(comunicado.prioridade)}>
                          {comunicado.prioridade}
                        </Badge>
                        <Pin className="h-4 w-4 text-primary" />
                      </div>
                    </div>
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <div className="flex items-center">
                        <User className="h-4 w-4 mr-1" />
                        {comunicado.autor}
                      </div>
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-1" />
                        {new Date(comunicado.data_publicacao).toLocaleDateString('pt-BR')}
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="prose prose-sm max-w-none">
                      <p className="text-gray-700 whitespace-pre-wrap">{comunicado.conteudo}</p>
                    </div>
                    {!comunicado.lida && (
                      <div className="mt-4 pt-3 border-t border-gray-200">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleMarkAsRead(comunicado.id)}
                          className="flex items-center"
                          disabled={markAsRead.isPending}
                        >
                          <CheckCircle className="h-4 w-4 mr-2" />
                          Marcar como Lido
                        </Button>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}

        {/* Comunicados Regulares */}
        <div>
          <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <MessageSquare className="h-5 w-5 mr-2 text-gray-600" />
            Todos os Comunicados
          </h2>
          {regularCommunications.length > 0 ? (
            <div className="grid gap-4">
              {regularCommunications.map((comunicado) => (
                <Card key={comunicado.id} className={comunicado.lida ? 'opacity-75' : ''}>
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <CardTitle className="text-lg flex items-center">
                        {getTypeIcon(comunicado.tipo)}
                        <span className="ml-2">{comunicado.titulo}</span>
                        {!comunicado.lida && (
                          <Badge variant="secondary" className="ml-2 bg-blue-100 text-blue-800">
                            Novo
                          </Badge>
                        )}
                      </CardTitle>
                      <Badge className={getPriorityColor(comunicado.prioridade)}>
                        {comunicado.prioridade}
                      </Badge>
                    </div>
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <div className="flex items-center">
                        <User className="h-4 w-4 mr-1" />
                        {comunicado.autor}
                      </div>
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-1" />
                        {new Date(comunicado.data_publicacao).toLocaleDateString('pt-BR')}
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="prose prose-sm max-w-none">
                      <p className="text-gray-700 whitespace-pre-wrap">{comunicado.conteudo}</p>
                    </div>
                    {!comunicado.lida && (
                      <div className="mt-4 pt-3 border-t border-gray-200">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleMarkAsRead(comunicado.id)}
                          className="flex items-center"
                          disabled={markAsRead.isPending}
                        >
                          <CheckCircle className="h-4 w-4 mr-2" />
                          Marcar como Lido
                        </Button>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Nenhum comunicado disponível
                </h3>
                <p className="text-gray-500">
                  Quando houver novos comunicados da administração, eles aparecerão aqui.
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </ResidentLayout>
  );
};

export default ResidentCommunications;
