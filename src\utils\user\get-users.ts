
import { User } from '@/types';
import { supabase } from '@/integrations/supabase/client';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

export const getUsers = async (): Promise<User[]> => {
  try {
    console.log('🔍 getUsers: Iniciando busca de usuários...');
    
    // Get current user info
    const { data: { user: currentUser } } = await supabase.auth.getUser();
    console.log('👤 getUsers: Usuário atual logado:', currentUser?.id, currentUser?.email);

    if (!currentUser) {
      console.warn('⚠️ getUsers: Usuário não autenticado');
      return [];
    }

    // Check if current user is admin
    const { data: currentProfile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', currentUser.id)
      .single();

    const isAdmin = currentProfile?.role === 'admin';
    console.log('🔑 getUsers: <PERSON>u<PERSON><PERSON> é admin:', isAdmin);

    let profilesData;
    let profilesError;

    if (isAdmin) {
      // Admin users: Try to use the secure function first, fallback to direct query
      console.log('👑 getUsers: Usuário admin - tentando função segura...');
      
      try {
        const { data: functionData, error: functionError } = await supabase
          .rpc('get_all_users');

        if (functionError) {
          console.warn('⚠️ getUsers: Erro na função segura, tentando query direta...', functionError);
          
          // Fallback to direct query for admins
          const { data: directData, error: directError } = await supabase
            .from('profiles')
            .select('*')
            .order('created_at', { ascending: false });

          profilesData = directData;
          profilesError = directError;
        } else {
          profilesData = functionData;
          profilesError = null;
        }
      } catch (error) {
        console.warn('⚠️ getUsers: Função segura falhou, usando query direta...', error);
        
        // Fallback to direct query
        const { data: directData, error: directError } = await supabase
          .from('profiles')
          .select('*')
          .order('created_at', { ascending: false });

        profilesData = directData;
        profilesError = directError;
      }
    } else {
      // Non-admin users: Can only see their own profile
      console.log('👤 getUsers: Usuário não-admin - buscando apenas perfil próprio...');
      
      const { data: ownProfile, error: ownError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', currentUser.id);

      profilesData = ownProfile;
      profilesError = ownError;
    }

    console.log('📊 getUsers: Query executada - Error:', profilesError);
    console.log('📊 getUsers: Query executada - Data length:', profilesData?.length || 0);

    if (profilesError) {
      console.error('❌ getUsers: Error fetching profiles:', profilesError);
      throw profilesError;
    }

    if (!profilesData || profilesData.length === 0) {
      console.warn('⚠️ getUsers: Nenhum perfil encontrado');
      return [];
    }

    console.log("✅ getUsers: Profiles data retrieved:", profilesData.length, "records");
    
    // Map profile data to User type
    const combinedUsers = profilesData.map(profile => {
      console.log('🔄 getUsers: Processando perfil:', profile.id, profile.email, profile.name);
      
      // Format the last login date if it exists
      let formattedLastLogin = '-';
      if (profile.last_sign_in) {
        try {
          const date = new Date(profile.last_sign_in);
          formattedLastLogin = format(date, 'dd/MM/yyyy HH:mm', { locale: ptBR });
        } catch (e) {
          console.error('❌ getUsers: Error formatting date:', e);
        }
      }

      const mappedUser = {
        id: profile.id,
        name: profile.name || 'Usuário sem nome',
        email: profile.email || '<EMAIL>',
        role: (profile.role === 'admin' ? 'Administrador' : 'Convidado') as 'Administrador' | 'Convidado',
        status: profile.banned ? 'Inativo' : 'Ativo' as 'Ativo' | 'Inativo',
        last_login: formattedLastLogin,
        created_at: profile.created_at,
        updated_at: profile.updated_at
      };
      
      console.log('✅ getUsers: Usuário mapeado:', mappedUser.id, mappedUser.email, mappedUser.role);
      return mappedUser;
    });

    console.log("🎯 getUsers: Transformed users:", combinedUsers.length);
    console.log("🎯 getUsers: Final users list:", combinedUsers.map(u => ({ id: u.id, email: u.email, role: u.role })));
    
    return combinedUsers;
  } catch (error) {
    console.error('💥 getUsers: Critical error in getUsers:', error);
    console.error('💥 getUsers: Error stack:', error instanceof Error ? error.stack : 'No stack trace');
    
    // Return empty array instead of throwing to prevent UI from breaking
    return [];
  }
};
