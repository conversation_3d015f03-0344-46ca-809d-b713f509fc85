Sistema Otimizado e Responsivo para Gestão de Quotas do Condomínio
Objetivo
Desenvolver o layout de um sistema responsivo e otimizado para gerenciamento de quotas do condomínio, priorizando uma experiência intuitiva para administradores e moradores. O backend será implementado posteriormente, após a conclusão do design.

Paleta de Cores
Utilizar a paleta de cores do logotipo do condomínio para manter a identidade visual.

📌 Estrutura do Sistema
📊 Dashboard (Painel Principal)
✅ Menu lateral esquerdo com as seguintes seções:

Painel

Condomínios → Submenus: Moradores, Membros da Comissão

Quotas

Fluxo de Caixa

Usuários

Relatórios

Configurações

📌 Cards informativos no Painel:

Número de moradores com quotas em atraso

Arrecadação do mês atual (zerado a cada novo mês e atualizado conforme os pagamentos são registrados)

Total de moradores

Gráficos de quotas pagas, multas aplicadas, valores pendentes, entre outras informações relevantes

👤 Moradores
📋 Tabela de dados (DataTable) contendo:

Nome | Apartamento | E-mail | Telefone | Ações (Editar, Deletar)

Botão para baixar PDF com os dados filtrados

🏢 Membros da Comissão
📋 Tabela de dados (DataTable) contendo:

Nome | Função | E-mail | Telefone | Data de Início | Data de Término | Ações (Editar, Deletar)

Botão para baixar PDF com os dados filtrados

💰 Gestão de Quotas
📋 Tabela de dados (DataTable) contendo:

Nome (automaticamente preenchido com os moradores cadastrados)

Apartamento (automaticamente preenchido com os moradores cadastrados)

Status (padrão: "Não Pago" até o administrador marcar como "Pago")

Data de Pagamento (ao inserir, preenche automaticamente o mês e ano)

Mês e Ano

Valor da Quota (definido apenas pelos administradores)

Multa (exibirá os valores se houver penalizações)

Quotas em Atraso (quantidade total de quotas pendentes do morador)

Situação (Regularizada / Não Regularizada)

Total a Pagar (Quota + Multa, se houver)

Ações: Editar / Deletar

Filtros Dropdown: Status, Mês e Ano

Botão para baixar PDF

📌 Regras de Negócio:

A cada novo mês, são geradas entradas vazias (exceto Nome e Apartamento)

O status padrão é "Não Pago" e só muda quando o administrador altera manualmente

O total de quotas e multas é atualizado automaticamente

📈 Fluxo de Caixa
📋 Dashboard contendo:
✅ Cards:

Saldo Total

Total de Entrada e Saída

Comparação de entradas com o mês anterior

Gráficos de fluxo de caixa

📋 Tabela de dados (DataTable) contendo:

Descrição | Tipo (Entrada/Saída) | Categoria | Data (preenchendo, preenche mês e ano) | Valor | Ações

Filtros Dropdown: Tipo, Categoria e Data

Botão para baixar PDF

👥 Gestão de Usuários
✅ Administrador pode:

Cadastrar usuários com Nome, E-mail e Senha

Remover, redefinir senha e bloquear usuários

Visualizar todos os usuários (Administradores e Convidados)

📑 Relatórios
📋 Painel com:

Resumo geral do sistema (cards, gráficos, ícones informativos)

Lista de moradores com quotas atrasadas (filtrável por mês)

Lista de moradores multados

Outras informações relevantes

⚙️ Configurações
✅ Parâmetros fixos definidos pelo Administrador:

Valor da Quota

Categorias

Tipos de Entrada e Saída

Status de Situação (Regularizada / Não Regularizada)

👤 Acesso dos Usuários (Moradores)
✅ Cada usuário pode acessar apenas seus próprios dados:

Ver se pagou ou não a quota do mês atual

Consultar quotas pendentes dos meses anteriores

Ver se recebeu multas e o valor total devido

Notificações:

Se o administrador atribuir uma multa, o usuário recebe uma notificação

Se o administrador marcar a quota como paga, o usuário recebe uma notificação de regularização

📌 Privacidade

Os usuários não podem visualizar as quotas dos outros moradores

Somente o administrador pode gerar relatórios e compartilhar via WhatsApp

💬 Sistema de Chat
✅ Os usuários podem enviar mensagens privadas aos administradores
✅ Cada mensagem pode ser direcionada para um administrador específico

📑 Funcionalidades Extras
✅ Menu lateral dinâmico

Clicar no botão: esconder e exibir apenas ícones

Passar o mouse: expandir temporariamente

Clicar novamente: fixar o menu

✅ Suporte a Modo Escuro (Dark Mode)

📌 Informações Gerais
Linguagem: Português (pt-PT)

Moeda: Kwanza (Kz) → Exemplo: 2.000 Kz

Código do país: +244 (Angola)