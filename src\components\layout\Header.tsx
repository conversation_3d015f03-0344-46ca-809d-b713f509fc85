
/**
 * Componente Header - Cabeçalho Principal do Sistema
 *
 * Cabeçalho fixo que contém logo, navegação, notificações e perfil do usuário.
 * Inclui funcionalidades de logout, perfil e controle da sidebar.
 *
 * <AUTHOR> Prédio Azul
 * @version 1.0
 */

import React, { useState } from 'react';
import { Bell, User, Clock, Grid3X3, Menu, LogOut } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useAuth } from '@/contexts/AuthContext';
import { useSidebar } from '@/contexts/SidebarContext';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import UserProfileModal from '@/components/modals/UserProfileModal';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

/**
 * Interface para as propriedades do componente Header
 */
interface HeaderProps {
  title: string;                    // Título da página atual
  subtitle?: string;                // Subtítulo opcional da página
  className?: string;               // Classes CSS adicionais
}

/**
 * Componente funcional Header
 *
 * Renderiza o cabeçalho principal com navegação, perfil e controles.
 * Gerencia estado do perfil do usuário e funcionalidades de logout.
 *
 * @param props - Propriedades do componente
 * @returns {JSX.Element} Cabeçalho renderizado
 */
const Header: React.FC<HeaderProps> = ({ title, subtitle, className }) => {
  // Hooks de contexto para autenticação e sidebar
  const { user, session, signOut } = useAuth();
  const { collapsed, toggleCollapsed } = useSidebar();

  // Estados locais para modal e dados do usuário
  const [isProfileModalOpen, setIsProfileModalOpen] = useState(false);
  const [userName, setUserName] = useState<string | null>(null);
  const [userPhoto, setUserPhoto] = useState<string | null>(null);
  const { toast } = useToast();

  // Efeito para buscar perfil do usuário quando logado
  React.useEffect(() => {
    if (user?.id) {
      fetchUserProfile();
    }
  }, [user?.id]);

  /**
   * Busca dados do perfil do usuário no Supabase
   * Atualiza nome e foto do usuário no estado local
   */
  const fetchUserProfile = async () => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('name, photo')
        .eq('id', user?.id)
        .single();

      if (error) {
        console.error('Error fetching user profile:', error);
        return;
      }

      if (data) {
        setUserName(data.name);
        setUserPhoto(data.photo);
      }
    } catch (error) {
      console.error('Error fetching user profile:', error);
    }
  };

  /**
   * Callback para atualizar perfil após edição
   * Recarrega dados do perfil do usuário
   */
  const handleUpdateProfile = () => {
    fetchUserProfile();
  };

  /**
   * Função para realizar logout do usuário
   * Utiliza o método signOut do contexto de autenticação
   */
  const handleLogout = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error("Error signing out:", error);
    }
  };

  return (
    <>
      {/* Main Header - Fixed at top */}
      <header className={cn("fixed top-0 left-0 right-0 z-50 bg-white border-b border-gray-200 shadow-sm", className)}>
        <div className="flex items-center justify-between h-14 px-4">
          {/* Left Section - Logo, System Name and Menu */}
          <div className="flex items-center space-x-3">
            {/* Logo */}
            <div className="flex items-center justify-center w-8 h-8 bg-primary rounded-lg">
              <Grid3X3 size={16} className="text-white" />
            </div>

            {/* System Name */}
            <span className="text-lg font-bold text-primary">Prédio Azul</span>

            {/* Menu Button */}
            <button
              onClick={toggleCollapsed}
              className="p-1.5 rounded-md hover:bg-gray-100 transition-colors"
            >
              <Menu size={16} className="text-gray-600" />
            </button>
          </div>

          {/* Right Section - Actions and Profile */}
          <div className="flex items-center space-x-2">
            {/* History Icon */}
            <button className="p-1.5 rounded-md hover:bg-gray-100 transition-colors">
              <Clock size={16} className="text-gray-600" />
            </button>

            {/* Notifications */}
            <button className="relative p-1.5 rounded-md hover:bg-gray-100 transition-colors">
              <Bell size={16} className="text-gray-600" />
              <span className="absolute top-0.5 right-0.5 h-2 w-2 bg-red-500 rounded-full"></span>
            </button>

            {/* User Profile */}
            <div
              className="flex items-center space-x-2 cursor-pointer p-1.5 rounded-md hover:bg-gray-50 transition-colors"
              onClick={() => setIsProfileModalOpen(true)}
            >
              <Avatar className="h-7 w-7 border border-gray-200">
                <AvatarImage src={userPhoto || undefined} alt={userName || 'User'} />
                <AvatarFallback>
                  <User className="h-4 w-4 text-gray-400" />
                </AvatarFallback>
              </Avatar>
              <div className="hidden md:block">
                <p className="text-sm font-medium text-gray-700">{userName || 'Usuário'}</p>
              </div>
            </div>

            {/* Logout Button */}
            <button
              onClick={handleLogout}
              className="p-1.5 rounded-md hover:bg-gray-100 hover:text-red-600 transition-colors"
              title="Sair"
            >
              <LogOut size={16} className="text-gray-600" />
            </button>
          </div>
        </div>
      </header>

      {/* Page Title Section - Below fixed header */}
      <div className="mt-14 bg-gray-50 py-4 border-b border-gray-200">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">{title}</h1>
          {subtitle && <p className="text-sm text-gray-500 mt-1">{subtitle}</p>}
        </div>
      </div>

      {/* User Profile Modal */}
      <UserProfileModal
        isOpen={isProfileModalOpen}
        onClose={() => setIsProfileModalOpen(false)}
        onProfileUpdate={handleUpdateProfile}
      />
    </>
  );
};

export default Header;
