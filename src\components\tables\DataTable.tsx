
import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import {
  ChevronDown,
  ChevronUp,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  Download,
  Filter,
  Search,
  Edit,
  Trash2
} from 'lucide-react';

interface Column {
  header: string;
  accessor: string;
  cell?: (value: any, row: any) => React.ReactNode;
  isSortable?: boolean;
  align?: 'left' | 'center' | 'right';
}

interface TableAction {
  label: string;
  onClick: (row: any) => void;
  icon: string;
}

interface DataTableProps {
  columns: Column[];
  data: any[];
  enableSearch?: boolean;
  searchPlaceholder?: string;
  enablePagination?: boolean;
  pageSize?: number;
  enableRowSelection?: boolean;
  onRowSelect?: (selectedRows: any[]) => void;
  isLoading?: boolean;
  noDataText?: string;
  className?: string;
  actionLabel?: string;
  filters?: {
    name: string;
    options: { value: string; label: string }[];
    onFilter: (value: string) => void;
  }[];
  enableDownload?: boolean;
  onDownload?: () => void;
  tableActions?: TableAction[];
  onEdit?: (row: any) => void;
  onDelete?: (id: string) => void;
  customControls?: React.ReactNode;
}

const DataTable: React.FC<DataTableProps> = ({
  columns,
  data,
  enableSearch = true,
  searchPlaceholder = 'Pesquisar...',
  enablePagination = true,
  pageSize = 10,
  enableRowSelection = false,
  onRowSelect,
  isLoading = false,
  noDataText = 'Nenhum dado encontrado',
  className,
  actionLabel = 'Ações',
  filters,
  enableDownload = false,
  onDownload,
  tableActions,
  onEdit,
  onDelete,
  customControls,
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [currentPageSize, setCurrentPageSize] = useState(pageSize);
  const [sortConfig, setSortConfig] = useState<{ key: string; direction: 'asc' | 'desc' } | null>(null);
  const [selectedRows, setSelectedRows] = useState<any[]>([]);
  const [visibleFilters, setVisibleFilters] = useState(false);

  const handleSort = (key: string) => {
    let direction: 'asc' | 'desc' = 'asc';
    
    if (sortConfig && sortConfig.key === key) {
      direction = sortConfig.direction === 'asc' ? 'desc' : 'asc';
    }
    
    setSortConfig({ key, direction });
  };

  const handleRowSelect = (row: any) => {
    const isSelected = selectedRows.some(r => r.id === row.id);
    let newSelectedRows: any[];
    
    if (isSelected) {
      newSelectedRows = selectedRows.filter(r => r.id !== row.id);
    } else {
      newSelectedRows = [...selectedRows, row];
    }
    
    setSelectedRows(newSelectedRows);
    if (onRowSelect) {
      onRowSelect(newSelectedRows);
    }
  };

  const handleSelectAll = () => {
    if (selectedRows.length === filteredData.length) {
      setSelectedRows([]);
      if (onRowSelect) {
        onRowSelect([]);
      }
    } else {
      setSelectedRows([...filteredData]);
      if (onRowSelect) {
        onRowSelect([...filteredData]);
      }
    }
  };

  const filteredData = data.filter(row => 
    Object.keys(row).some(key => 
      String(row[key])
        .toLowerCase()
        .includes(searchQuery.toLowerCase())
    )
  );

  const sortedData = React.useMemo(() => {
    if (!sortConfig) return filteredData;

    return [...filteredData].sort((a, b) => {
      // Special handling for resident name column - sort by apartment number
      if (sortConfig.key === 'moradores' && a.moradores && b.moradores) {
        const aptA = a.moradores.apartamento || 'ZZZ'; // 'ZZZ' puts N/A at the end
        const aptB = b.moradores.apartamento || 'ZZZ';

        // Use proven apartment sorting logic from the codebase (ReportFiltersModal.tsx)
        // First try numeric sorting for apartments like "1", "2", "10"
        const numA = parseInt(aptA);
        const numB = parseInt(aptB);
        if (!isNaN(numA) && !isNaN(numB)) {
          return sortConfig.direction === 'asc' ? numA - numB : numB - numA;
        }

        // Fallback to alphabetic sorting for apartments like "1A", "1B", "2A"
        const aptComparison = aptA.localeCompare(aptB);
        return sortConfig.direction === 'asc' ? aptComparison : -aptComparison;
      }

      // Special handling for period column with dual-column sorting
      // Primary sort by period (mes/ano), secondary sort by resident apartment
      if (sortConfig.key === 'periodo' && a.mes !== undefined && a.ano !== undefined) {
        // Primary sort: Period (year first, then month)
        const yearA = a.ano || 0;
        const yearB = b.ano || 0;
        if (yearA !== yearB) {
          return sortConfig.direction === 'asc' ? yearA - yearB : yearB - yearA;
        }

        const monthA = a.mes || 0;
        const monthB = b.mes || 0;
        if (monthA !== monthB) {
          return sortConfig.direction === 'asc' ? monthA - monthB : monthB - monthA;
        }

        // Secondary sort: Apartment number (always ascending for logical order)
        const aptA = a.moradores?.apartamento || 'ZZZ';
        const aptB = b.moradores?.apartamento || 'ZZZ';

        // Use proven apartment sorting logic from the codebase
        const numA = parseInt(aptA);
        const numB = parseInt(aptB);
        if (!isNaN(numA) && !isNaN(numB)) {
          return numA - numB; // Always ascending for apartments
        }
        return aptA.localeCompare(aptB); // Always ascending for apartments
      }

      // Default sorting for other columns
      const valueA = a[sortConfig.key];
      const valueB = b[sortConfig.key];

      if (valueA < valueB) {
        return sortConfig.direction === 'asc' ? -1 : 1;
      }
      if (valueA > valueB) {
        return sortConfig.direction === 'asc' ? 1 : -1;
      }
      return 0;
    });
  }, [filteredData, sortConfig]);

  const pageCount = Math.ceil(sortedData.length / currentPageSize);
  const paginatedData = React.useMemo(() => {
    const start = (currentPage - 1) * currentPageSize;
    const end = start + currentPageSize;
    return sortedData.slice(start, end);
  }, [sortedData, currentPage, currentPageSize]);

  const goToPage = (page: number) => {
    setCurrentPage(page);
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setCurrentPageSize(newPageSize);
    setCurrentPage(1); // Reset to first page when changing page size
  };

  // Check if we should render the actions column
  const shouldRenderActionsColumn = onEdit || onDelete || (data.length > 0 && (data[0].onEdit || data[0].onDelete));

  return (
    <div className={cn("flex flex-col", className)}>
      <div className="flex flex-col mb-4 gap-3 sm:flex-row sm:items-end sm:justify-between">
        {enableSearch && (
          <div className="relative">
            <input
              type="text"
              placeholder={searchPlaceholder}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 pr-4 py-2 w-full sm:w-64 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary-200 focus:border-primary-300 transition-all"
            />
            <Search size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          </div>
        )}

        {customControls && (
          <div className="flex-1 flex justify-end">
            {customControls}
          </div>
        )}

        {!customControls && (
          <div className="flex items-center gap-3">
            {filters && filters.length > 0 && (
              <div className="relative">
                <button
                  onClick={() => setVisibleFilters(!visibleFilters)}
                  className="flex items-center gap-2 px-3 py-2 text-sm font-medium rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
                >
                  <Filter size={16} />
                  Filtros
                </button>
                {visibleFilters && (
                  <div className="absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-lg border border-gray-200 z-10">
                    <div className="p-3">
                      {filters.map((filter, index) => (
                        <div key={index} className="mb-3 last:mb-0">
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            {filter.name}
                          </label>
                          <select
                            onChange={(e) => filter.onFilter(e.target.value)}
                            className="w-full p-2 text-sm border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-200 focus:border-primary-300"
                          >
                            <option value="">Todos</option>
                            {filter.options.map((option) => (
                              <option key={option.value} value={option.value}>
                                {option.label}
                              </option>
                            ))}
                          </select>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {enableDownload && (
              <button
                onClick={onDownload}
                className="flex items-center gap-2 px-3 py-2 text-sm font-medium rounded-lg text-white bg-primary hover:bg-primary-600 transition-colors"
              >
                <Download size={16} />
                Baixar PDF
              </button>
            )}
          </div>
        )}
      </div>

      <div className="overflow-x-auto border border-gray-100 rounded-lg">
        <div className="min-w-full inline-block align-middle">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-100">
              <thead>
                <tr className="bg-gray-50 border-b border-gray-200">
                  {enableRowSelection && (
                    <th className="py-3 px-4 text-left">
                      <input
                        type="checkbox"
                        checked={paginatedData.length > 0 && selectedRows.length === paginatedData.length}
                        onChange={handleSelectAll}
                        className="rounded border-gray-300 text-primary focus:ring-primary"
                      />
                    </th>
                  )}
                  {columns.map((column, index) => (
                    <th
                      key={index}
                      className={cn(
                        "py-3 px-4 text-sm font-medium text-gray-600 whitespace-nowrap",
                        column.align === 'center' && "text-center",
                        column.align === 'right' && "text-right",
                        column.isSortable && "cursor-pointer hover:bg-gray-100"
                      )}
                      onClick={() => column.isSortable && handleSort(column.accessor)}
                    >
                      <div className={cn(
                        "flex items-center gap-1",
                        column.align === 'center' && "justify-center",
                        column.align === 'right' && "justify-end",
                      )}>
                        {column.header}
                        {column.isSortable && (
                          <span className="inline-flex flex-col">
                            <ChevronUp
                              size={12}
                              className={cn(
                                "text-gray-400",
                                sortConfig?.key === column.accessor && 
                                sortConfig?.direction === 'asc' && 
                                "text-primary"
                              )}
                            />
                            <ChevronDown
                              size={12}
                              className={cn(
                                "text-gray-400 -mt-1",
                                sortConfig?.key === column.accessor && 
                                sortConfig?.direction === 'desc' && 
                                "text-primary"
                              )}
                            />
                          </span>
                        )}
                      </div>
                    </th>
                  ))}
                  {shouldRenderActionsColumn && (
                    <th className="py-3 px-4 text-sm font-medium text-gray-600 text-center whitespace-nowrap">
                      {actionLabel}
                    </th>
                  )}
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-100">
                {isLoading ? (
                  <tr>
                    <td
                      colSpan={columns.length + (shouldRenderActionsColumn ? 1 : 0) + (enableRowSelection ? 1 : 0)}
                      className="py-10 text-center text-gray-500"
                    >
                      <div className="flex flex-col items-center">
                        <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin mb-2"></div>
                        <p>Carregando...</p>
                      </div>
                    </td>
                  </tr>
                ) : paginatedData.length === 0 ? (
                  <tr>
                    <td
                      colSpan={columns.length + (shouldRenderActionsColumn ? 1 : 0) + (enableRowSelection ? 1 : 0)}
                      className="py-10 text-center text-gray-500"
                    >
                      {noDataText}
                    </td>
                  </tr>
                ) : (
                  paginatedData.map((row, rowIndex) => (
                    <tr
                      key={rowIndex}
                      className={cn(
                        "border-b border-gray-100 hover:bg-gray-50 transition-colors",
                        rowIndex % 2 === 1 && "bg-gray-50/50"
                      )}
                    >
                      {enableRowSelection && (
                        <td className="py-3 px-4">
                          <input
                            type="checkbox"
                            checked={selectedRows.some(r => r.id === row.id)}
                            onChange={() => handleRowSelect(row)}
                            className="rounded border-gray-300 text-primary focus:ring-primary"
                          />
                        </td>
                      )}
                      {columns.map((column, colIndex) => (
                        <td
                          key={colIndex}
                          className={cn(
                            "py-3 px-4 text-sm text-gray-700",
                            column.align === 'center' && "text-center",
                            column.align === 'right' && "text-right"
                          )}
                        >
                          {column.cell
                            ? column.cell(row[column.accessor], row)
                            : row[column.accessor]}
                        </td>
                      ))}
                      {shouldRenderActionsColumn && (
                        <td className="py-3 px-4 text-center">
                          <div className="flex items-center justify-center gap-2">
                            {(onEdit || row.onEdit) && (
                              <button
                                className="p-1 text-blue-600 hover:text-blue-800 transition-colors"
                                title="Editar"
                                onClick={() => onEdit ? onEdit(row) : row.onEdit()}
                              >
                                <Edit size={18} />
                              </button>
                            )}
                            {(onDelete || row.onDelete) && (
                              <button
                                className="p-1 text-red-600 hover:text-red-800 transition-colors"
                                title="Excluir"
                                onClick={() => onDelete ? onDelete(row.id) : row.onDelete()}
                              >
                                <Trash2 size={18} />
                              </button>
                            )}
                          </div>
                        </td>
                      )}
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {enablePagination && sortedData.length > 0 && (
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mt-4 text-sm">
          {/* Records counter and page size selector */}
          <div className="flex flex-col sm:flex-row sm:items-center gap-4">
            <p className="text-gray-500">
              Mostrando <span className="font-medium">{((currentPage - 1) * currentPageSize) + 1}</span>-<span className="font-medium">
                {Math.min(currentPage * currentPageSize, sortedData.length)}
              </span>{' '}
              de <span className="font-medium">{sortedData.length}</span>
            </p>

            <div className="flex items-center gap-2">
              <span className="text-gray-500">Registros por página:</span>
              <select
                value={currentPageSize}
                onChange={(e) => handlePageSizeChange(Number(e.target.value))}
                className="px-2 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                <option value={10}>10</option>
                <option value={25}>25</option>
                <option value={50}>50</option>
                <option value={100}>100</option>
              </select>
            </div>
          </div>

          {/* Pagination controls */}
          <div className="flex items-center gap-1">
            {/* First page button */}
            <button
              onClick={() => goToPage(1)}
              disabled={currentPage === 1}
              className={cn(
                "p-1 rounded-md",
                currentPage === 1
                  ? "text-gray-400 cursor-not-allowed"
                  : "text-gray-700 hover:bg-gray-100"
              )}
              title="Primeira página"
            >
              <ChevronsLeft size={16} />
            </button>

            {/* Previous page button */}
            <button
              onClick={() => goToPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
              className={cn(
                "p-1 rounded-md",
                currentPage === 1
                  ? "text-gray-400 cursor-not-allowed"
                  : "text-gray-700 hover:bg-gray-100"
              )}
              title="Página anterior"
            >
              <ChevronLeft size={16} />
            </button>

            {/* Page numbers */}
            <div className="flex items-center space-x-1">
              {Array.from({ length: Math.min(5, pageCount) }, (_, i) => {
                let pageNum;
                if (pageCount <= 5) {
                  pageNum = i + 1;
                } else if (currentPage <= 3) {
                  pageNum = i + 1;
                } else if (currentPage >= pageCount - 2) {
                  pageNum = pageCount - 4 + i;
                } else {
                  pageNum = currentPage - 2 + i;
                }

                return (
                  <button
                    key={i}
                    onClick={() => goToPage(pageNum)}
                    className={cn(
                      "flex items-center justify-center w-8 h-8 rounded-md text-sm",
                      currentPage === pageNum
                        ? "bg-primary text-white"
                        : "text-gray-700 hover:bg-gray-100"
                    )}
                  >
                    {pageNum}
                  </button>
                );
              })}
            </div>

            {/* Next page button */}
            <button
              onClick={() => goToPage(Math.min(pageCount, currentPage + 1))}
              disabled={currentPage === pageCount}
              className={cn(
                "p-1 rounded-md",
                currentPage === pageCount
                  ? "text-gray-400 cursor-not-allowed"
                  : "text-gray-700 hover:bg-gray-100"
              )}
              title="Próxima página"
            >
              <ChevronRight size={16} />
            </button>

            {/* Last page button */}
            <button
              onClick={() => goToPage(pageCount)}
              disabled={currentPage === pageCount}
              className={cn(
                "p-1 rounded-md",
                currentPage === pageCount
                  ? "text-gray-400 cursor-not-allowed"
                  : "text-gray-700 hover:bg-gray-100"
              )}
              title="Última página"
            >
              <ChevronsRight size={16} />
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default DataTable;
