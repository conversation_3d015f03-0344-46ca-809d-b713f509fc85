export interface User {
  id: string;
  name: string | null;
  email: string;
  role: 'Administrador' | 'Convidado';
  status: 'Ativo' | 'Inativo';
  last_login?: string;
  created_at: string;
  updated_at?: string;
}

export interface Profile {
  id: string;
  name: string | null;
  photo: string | null;
  role: 'admin' | 'convidado' | 'resident' | null;
  created_at: string;
  updated_at: string;
  apartment_id?: string;
  first_login?: boolean;
  last_login?: string;
  notification_preferences?: any;
}

export interface Morador {
  id: string;
  nome: string;
  apartamento: string;
  email: string;
  telefone?: string;
  status?: string;
  user_id?: string;
  created_at?: string;
  updated_at?: string;
  isento_quotas?: boolean;
  motivo_isencao?: string;
}

export interface MembroComissao {
  id: string;
  nome: string;
  cargo: string;
  email?: string;
  telefone?: string;
  foto?: string;
  biografia?: string;
  data_inicio: string;
  data_fim?: string;
  status?: string;
  created_at?: string;
  updated_at?: string;
}

export interface Quota {
  id: string;
  morador_id: string;
  mes: number;
  ano: number;
  valor: number;
  data_vencimento: string;
  data_pagamento?: string | null;
  status: string;
  multa: number;
  numero_multas: number;
  situacao: string;
  comprovativo?: string | null;
  fluxo_caixa_id?: string | null; // Novo campo para referência ao lançamento
  created_at: string;
  updated_at: string;
  moradores?: Morador;
}

export interface FluxoCaixa {
  id: string;
  tipo: 'entrada' | 'saida';
  categoria: string;
  descricao: string;
  valor: number;
  data: string;
  anexo?: string;
  responsavel_id?: string;
  created_at?: string;
  updated_at?: string;
}

export interface Notificacao {
  id: string;
  usuario_id: string;
  titulo: string;
  mensagem: string;
  tipo: string;
  lida: boolean;
  link?: string;
  created_at: string;
}

export interface Relatorio {
  id: string;
  titulo: string;
  tipo: string;
  parametros?: any;
  resultado?: any;
  gerado_por: string;
  created_at: string;
}

export interface Configuracao {
  id: string;
  nome: string;
  valor: string;
  descricao?: string;
  created_at?: string;
  updated_at?: string;
}

export interface QuotaWithResident extends Quota {
  resident?: {
    name: string;
    apartment: string;
  };
}

// New interfaces for Documents feature
export interface DocumentFolder {
  id: string;
  name: string;
  parent_id: string | null;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface DocumentFile {
  name: string;
  id: string;
  metadata: {
    mimetype: string;
    size: number;
  };
  created_at: string;
  updated_at?: string;
  fullPath: string;
  isFolder?: boolean;
}

// Interfaces específicas para área do morador
export interface ResidentQuotaView {
  currentMonth: {
    status: 'paid' | 'pending' | 'overdue';
    amount: number;
    dueDate: Date;
    paidDate?: Date;
  };
  history: QuotaHistory[];
  summary: {
    totalPending: number;
    totalFines: number;
    monthsOverdue: number;
  };
}

export interface QuotaHistory {
  id: string;
  month: number;
  year: number;
  amount: number;
  status: 'paid' | 'pending' | 'overdue';
  dueDate: Date;
  paidDate?: Date;
  fine?: number;
  fineStatus?: string; // Status da multa (Regularizada, Não Regularizada, etc.)
  receipt?: string;
}

export interface ResidentNotification {
  id: string;
  type: 'quota_paid' | 'fine_applied' | 'reminder' | 'announcement';
  title: string;
  message: string;
  read: boolean;
  createdAt: Date;
  priority: 'low' | 'medium' | 'high';
}
