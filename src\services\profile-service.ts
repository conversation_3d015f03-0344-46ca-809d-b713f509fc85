
import { supabase } from '@/integrations/supabase/client';
import { Profile } from '@/types/auth';

/**
 * Fetch user profile from database
 */
export const fetchProfile = async (userId: string): Promise<Profile | null> => {
  try {
    console.log('🔍 Fetching profile for user:', userId);
    
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();

    if (error) {
      console.error('❌ Error fetching profile:', error);
      return null;
    }

    console.log('✅ Profile fetched successfully:', data);
    
    // Cast role to proper type
    const profileData: Profile = {
      ...data,
      role: data.role as 'admin' | 'resident' | 'convidado'
    };
    
    return profileData;
  } catch (error) {
    console.error('💥 Error in fetchProfile:', error);
    return null;
  }
};

/**
 * Update user profile in database
 */
export const updateProfileData = async (userId: string, updates: Partial<Profile>): Promise<Profile> => {
  try {
    console.log('🔄 Updating profile with:', updates);
    
    const { data, error } = await supabase
      .from('profiles')
      .update(updates)
      .eq('id', userId)
      .select()
      .single();

    if (error) {
      console.error('❌ Error updating profile:', error);
      throw error;
    }

    console.log('✅ Profile updated successfully:', data);
    
    // Cast role to proper type
    const profileData: Profile = {
      ...data,
      role: data.role as 'admin' | 'resident' | 'convidado'
    };
    
    return profileData;
  } catch (error) {
    console.error('💥 Error in updateProfile:', error);
    throw error;
  }
};
