
import React from 'react';
import { Shield } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Morador } from '@/types';

interface QuotaGeneratorFormProps {
  generationType: 'current' | 'custom';
  setGenerationType: (type: 'current' | 'custom') => void;
  startMonth: number;
  setStartMonth: (month: number) => void;
  startYear: number;
  setStartYear: (year: number) => void;
  includeExempt: boolean;
  setIncludeExempt: (include: boolean) => void;
  moradores: Morador[];
  months: { value: number; label: string }[];
  years: number[];
}

export const QuotaGeneratorForm: React.FC<QuotaGeneratorFormProps> = ({
  generationType,
  setGenerationType,
  startMonth,
  setStartMonth,
  startYear,
  setStartYear,
  includeExempt,
  setIncludeExempt,
  moradores,
  months,
  years
}) => {
  const exemptCount = moradores.filter(m => m.isento_quotas).length;

  const handleIncludeExemptChange = (checked: boolean | 'indeterminate') => {
    setIncludeExempt(checked === true);
  };

  return (
    <div className="space-y-6">
      {/* Tipo de Geração */}
      <div className="space-y-3">
        <Label className="text-sm font-medium">Período de Geração</Label>
        <Select value={generationType} onValueChange={(value: 'current' | 'custom') => setGenerationType(value)}>
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="current">
              Mês Atual - Todos os Moradores
            </SelectItem>
            <SelectItem value="custom">
              Personalizada
            </SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Opções Personalizadas */}
      {generationType === 'custom' && (
        <div className="space-y-3">
          <Label className="text-sm font-medium">Período Inicial</Label>
          <div className="grid grid-cols-2 gap-3">
            <div>
              <Label className="text-xs text-gray-600">Mês</Label>
              <Select value={startMonth.toString()} onValueChange={(value) => setStartMonth(parseInt(value))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {months.map((month) => (
                    <SelectItem key={month.value} value={month.value.toString()}>
                      {month.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label className="text-xs text-gray-600">Ano</Label>
              <Select value={startYear.toString()} onValueChange={(value) => setStartYear(parseInt(value))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {years.map((year) => (
                    <SelectItem key={year} value={year.toString()}>
                      {year}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      )}

      {/* Opção para Incluir Isentos */}
      {exemptCount > 0 && (
        <div className="space-y-3 border-t pt-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="includeExempt"
              checked={includeExempt}
              onCheckedChange={handleIncludeExemptChange}
            />
            <Label htmlFor="includeExempt" className="text-sm font-medium flex items-center gap-2">
              <Shield className="w-4 h-4 text-yellow-600" />
              Incluir moradores isentos
            </Label>
          </div>
          <p className="text-xs text-gray-600 ml-6">
            Gerar quotas também para os {exemptCount} moradores marcados como isentos
          </p>
        </div>
      )}
    </div>
  );
};
