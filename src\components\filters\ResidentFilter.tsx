import React, { useState, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { User, ChevronDown, X } from 'lucide-react';
import { Morador } from '@/types';

interface ResidentFilterProps {
  moradores: Morador[];
  selectedMorador: Morador | null;
  onMoradorChange: (morador: Morador | null) => void;
  placeholder?: string;
  className?: string;
}

const ResidentFilter: React.FC<ResidentFilterProps> = ({
  moradores,
  selectedMorador,
  onMoradorChange,
  placeholder = "Filtrar por morador...",
  className
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Filter moradores based on search term
  const filteredMoradores = moradores.filter(morador =>
    morador.nome.toLowerCase().includes(searchTerm.toLowerCase()) ||
    morador.apartamento.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSearchTerm('');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleMoradorSelect = (morador: Morador) => {
    onMoradorChange(morador);
    setIsOpen(false);
    setSearchTerm('');
  };

  const handleClear = () => {
    onMoradorChange(null);
    setSearchTerm('');
  };

  const handleInputClick = () => {
    setIsOpen(true);
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  return (
    <div className={cn("relative", className)} ref={dropdownRef}>
      <div className="relative">
          <div
            className={cn(
              "flex items-center w-full px-3 py-2 border border-gray-300 rounded-md bg-white cursor-pointer",
              "focus-within:outline-none focus-within:ring-2 focus-within:ring-primary-500 focus-within:border-primary-500",
              "hover:border-gray-400 transition-colors"
            )}
            onClick={handleInputClick}
          >
            <User size={16} className="text-gray-400 mr-2 flex-shrink-0" />
            
            {selectedMorador && !isOpen ? (
              <div className="flex items-center justify-between w-full">
                <span className="text-sm text-gray-900 truncate">
                  {selectedMorador.nome} ({selectedMorador.apartamento})
                </span>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleClear();
                  }}
                  className="ml-2 p-1 hover:bg-gray-100 rounded-full transition-colors"
                  title="Limpar filtro"
                >
                  <X size={14} className="text-gray-400" />
                </button>
              </div>
            ) : (
              <>
                <input
                  ref={inputRef}
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder={selectedMorador ? `${selectedMorador.nome} (${selectedMorador.apartamento})` : placeholder}
                  className="flex-1 outline-none bg-transparent text-sm"
                  onFocus={() => setIsOpen(true)}
                />
                <ChevronDown 
                  size={16} 
                  className={cn(
                    "text-gray-400 transition-transform ml-2",
                    isOpen && "transform rotate-180"
                  )} 
                />
              </>
            )}
          </div>

          {/* Dropdown */}
          {isOpen && (
            <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
              {/* Clear option */}
              <div
                className="px-3 py-2 hover:bg-gray-50 cursor-pointer border-b border-gray-100"
                onClick={handleClear}
              >
                <span className="text-sm text-gray-500">Todos os moradores</span>
              </div>

              {/* Filtered moradores */}
              {filteredMoradores.length > 0 ? (
                filteredMoradores.map((morador) => (
                  <div
                    key={morador.id}
                    className={cn(
                      "px-3 py-2 hover:bg-gray-50 cursor-pointer",
                      selectedMorador?.id === morador.id && "bg-primary-50 text-primary-700"
                    )}
                    onClick={() => handleMoradorSelect(morador)}
                  >
                    <div className="flex flex-col">
                      <span className="text-sm font-medium">{morador.nome}</span>
                      <span className="text-xs text-gray-500">Apartamento {morador.apartamento}</span>
                    </div>
                  </div>
                ))
              ) : (
                <div className="px-3 py-2 text-sm text-gray-500">
                  Nenhum morador encontrado
                </div>
              )}
            </div>
          )}
        </div>
    </div>
  );
};

export default ResidentFilter;
