
import React from 'react';
import ResidentLayout from '@/components/layout/resident/ResidentLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  FolderOpen,
  FileText,
  Download,
  Calendar,
  User,
  AlertCircle
} from 'lucide-react';
import { useResidentDocuments } from '@/hooks/resident/useResidentDocuments';

const ResidentDocuments = () => {
  const { data: documents, isLoading, error } = useResidentDocuments();

  const getFileIcon = (tipo: string) => {
    switch (tipo.toLowerCase()) {
      case 'pdf':
        return <FileText className="h-5 w-5 text-red-500" />;
      case 'doc':
      case 'docx':
        return <FileText className="h-5 w-5 text-blue-500" />;
      case 'jpg':
      case 'jpeg':
      case 'png':
        return <FileText className="h-5 w-5 text-green-500" />;
      default:
        return <FileText className="h-5 w-5 text-gray-500" />;
    }
  };

  const getCategoryColor = (categoria: string) => {
    switch (categoria.toLowerCase()) {
      case 'comprovantes':
        return 'bg-green-100 text-green-800';
      case 'contratos':
        return 'bg-blue-100 text-blue-800';
      case 'regulamentos':
        return 'bg-purple-100 text-purple-800';
      case 'outros':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleDownload = (url: string, filename: string) => {
    // Criar um link temporário para download
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const formatFileSize = (sizeStr: string) => {
    // Se já está formatado, retorna como está
    if (sizeStr.includes('KB') || sizeStr.includes('MB') || sizeStr.includes('GB')) {
      return sizeStr;
    }
    
    // Caso contrário, converte de bytes
    const size = parseInt(sizeStr);
    if (size < 1024) return `${size} B`;
    if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`;
    if (size < 1024 * 1024 * 1024) return `${(size / (1024 * 1024)).toFixed(1)} MB`;
    return `${(size / (1024 * 1024 * 1024)).toFixed(1)} GB`;
  };

  if (isLoading) {
    return (
      <ResidentLayout
        title="Documentos"
        subtitle="Acesse seus documentos e comprovantes"
      >
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      </ResidentLayout>
    );
  }

  if (error) {
    return (
      <ResidentLayout
        title="Documentos"
        subtitle="Acesse seus documentos e comprovantes"
      >
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <p className="text-gray-600">Erro ao carregar documentos</p>
          </div>
        </div>
      </ResidentLayout>
    );
  }

  // Agrupar documentos por categoria
  const groupedDocuments = documents?.reduce((acc, doc) => {
    const categoria = doc.categoria || 'Outros';
    if (!acc[categoria]) {
      acc[categoria] = [];
    }
    acc[categoria].push(doc);
    return acc;
  }, {} as Record<string, typeof documents>) || {};

  return (
    <ResidentLayout
      title="Documentos"
      subtitle="Acesse seus documentos e comprovantes"
    >
      <div className="space-y-6 mt-6">
        {Object.keys(groupedDocuments).length > 0 ? (
          Object.entries(groupedDocuments).map(([categoria, docs]) => (
            <div key={categoria}>
              <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <FolderOpen className="h-5 w-5 mr-2 text-primary" />
                {categoria}
                <Badge variant="secondary" className="ml-2">
                  {docs.length} documento{docs.length !== 1 ? 's' : ''}
                </Badge>
              </h2>
              
              <div className="grid gap-4">
                {docs.map((document) => (
                  <Card key={document.id} className="hover:shadow-md transition-shadow">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3 flex-1 min-w-0">
                          {getFileIcon(document.tipo)}
                          <div className="flex-1 min-w-0">
                            <h3 className="font-medium text-gray-900 truncate">
                              {document.nome}
                            </h3>
                            <div className="flex items-center space-x-4 text-sm text-gray-500 mt-1">
                              <div className="flex items-center">
                                <Calendar className="h-4 w-4 mr-1" />
                                {new Date(document.data_upload).toLocaleDateString('pt-BR')}
                              </div>
                              <span>•</span>
                              <span>{formatFileSize(document.tamanho)}</span>
                              <span>•</span>
                              <span className="uppercase">{document.tipo}</span>
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex items-center space-x-3">
                          <Badge className={getCategoryColor(document.categoria)}>
                            {document.categoria}
                          </Badge>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDownload(document.url_arquivo, document.nome)}
                            className="flex items-center"
                          >
                            <Download className="h-4 w-4 mr-2" />
                            Baixar
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          ))
        ) : (
          <Card>
            <CardContent className="p-8 text-center">
              <FolderOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Nenhum documento disponível
              </h3>
              <p className="text-gray-500">
                Quando a administração disponibilizar documentos para seu apartamento, eles aparecerão aqui.
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </ResidentLayout>
  );
};

export default ResidentDocuments;
