
import { supabase } from '@/integrations/supabase/client';
import { FluxoCaixa, Quota } from '@/types';
import { createLancamento, updateLancamento } from './supabase-helpers';

export interface QuotaCashFlowIntegration {
  generateMonthlyLancamento: (mes: number, ano: number, quotas: Omit<Quota, 'id' | 'created_at' | 'updated_at'>[]) => Promise<string | null>;
  updateLancamentoOnPayment: (quotaId: string, quotaValue: number) => Promise<boolean>;
  createFineEntry: (quota: Quota, fineValue: number) => Promise<boolean>;
  findExistingLancamento: (mes: number, ano: number) => Promise<FluxoCaixa | null>;
  findOrCreateLancamento: (mes: number, ano: number, quotaValue: number) => Promise<string | null>;
}

/**
 * Busca um lançamento existente para um mês específico
 */
export const findExistingLancamento = async (mes: number, ano: number): Promise<FluxoCaixa | null> => {
  try {
    const monthNames = [
      'Janeiro', 'Fevereiro', 'Mar<PERSON><PERSON>', 'Abril', '<PERSON><PERSON>', 'Jun<PERSON>',
      'Jul<PERSON>', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
    ];
    
    const expectedDescription = `Quotas ${monthNames[mes - 1]}/${ano}`;
    
    console.log('🔍 Buscando lançamento existente:', expectedDescription);
    
    const { data, error } = await supabase
      .from('fluxo_caixa')
      .select('*')
      .eq('descricao', expectedDescription)
      .eq('categoria', 'Quotas')
      .eq('tipo', 'entrada')
      .maybeSingle();
    
    if (error) {
      console.error('❌ Erro ao buscar lançamento existente:', error);
      return null;
    }
    
    // Type assertion to ensure proper typing
    const typedData = data as FluxoCaixa | null;
    
    console.log('📊 Lançamento encontrado:', typedData);
    return typedData;
  } catch (error) {
    console.error('💥 Erro crítico ao buscar lançamento:', error);
    return null;
  }
};

/**
 * Busca um lançamento existente ou cria um novo para o mês/ano especificado
 * Esta função é chamada apenas quando uma quota é paga (abordagem correta)
 * CORRIGIDO: Agora incrementa o valor quando lançamento já existe
 */
export const findOrCreateLancamento = async (
  mes: number,
  ano: number,
  quotaValue: number
): Promise<string | null> => {
  try {
    console.log(`🔍 Buscando ou criando lançamento para ${mes}/${ano} com valor ${quotaValue}`);

    // Primeiro, verificar se já existe um lançamento para este mês
    const existingLancamento = await findExistingLancamento(mes, ano);
    if (existingLancamento) {
      console.log('📋 Lançamento já existe para este mês:', existingLancamento.id);
      console.log(`💰 Valor atual do lançamento: ${existingLancamento.valor}`);

      // CORREÇÃO: Incrementar o valor do lançamento existente
      const novoValor = existingLancamento.valor + quotaValue;
      console.log(`📈 Incrementando valor do lançamento de ${existingLancamento.valor} para ${novoValor}`);

      const updateResult = await updateLancamento(existingLancamento.id, {
        valor: novoValor
      });

      if (updateResult) {
        console.log('✅ Valor do lançamento incrementado com sucesso');
        return existingLancamento.id;
      } else {
        console.error('❌ Falha ao incrementar valor do lançamento');
        return null;
      }
    }

    // Se não existe, criar um novo lançamento com o valor real da primeira quota paga
    const monthNames = [
      'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
      'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
    ];

    const lancamentoData = {
      tipo: 'entrada' as const,
      categoria: 'Quotas',
      descricao: `Quotas ${monthNames[mes - 1]}/${ano}`,
      valor: quotaValue, // Usar o valor real da primeira quota paga
      data: `${ano}-${mes.toString().padStart(2, '0')}-01`,
      responsavel_id: null,
      anexo: null
    };

    console.log('📝 Criando novo lançamento com valor real:', lancamentoData);

    const result = await createLancamento(lancamentoData);

    if (result) {
      console.log('✅ Novo lançamento criado com sucesso:', result.id);
      return result.id;
    } else {
      console.error('❌ Falha ao criar novo lançamento');
      return null;
    }
  } catch (error) {
    console.error('💥 Erro crítico ao buscar/criar lançamento:', error);
    return null;
  }
};

/**
 * Gera um lançamento mensal no fluxo de caixa com valor mínimo inicial
 * DEPRECATED: Esta função não deve mais ser usada na geração de quotas
 */
export const generateMonthlyLancamento = async (
  mes: number, 
  ano: number, 
  quotas: Omit<Quota, 'id' | 'created_at' | 'updated_at'>[]
): Promise<string | null> => {
  try {
    console.log(`💰 Gerando lançamento mensal para ${mes}/${ano} com ${quotas.length} quotas`);
    
    // Verificar se já existe um lançamento para este mês
    const existingLancamento = await findExistingLancamento(mes, ano);
    if (existingLancamento) {
      console.log('⚠️ Lançamento já existe para este mês:', existingLancamento.id);
      return existingLancamento.id;
    }
    
    const monthNames = [
      'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
      'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
    ];
    
    // Criar o lançamento com valor mínimo inicial (1 Kz) para contornar a validação
    const lancamentoData = {
      tipo: 'entrada' as const,
      categoria: 'Quotas',
      descricao: `Quotas ${monthNames[mes - 1]}/${ano}`,
      valor: 1, // Valor mínimo inicial - será incrementado com os pagamentos
      data: `${ano}-${mes.toString().padStart(2, '0')}-01`,
      responsavel_id: null,
      anexo: null
    };
    
    console.log('📝 Criando lançamento com valor mínimo inicial:', lancamentoData);
    
    const result = await createLancamento(lancamentoData);
    
    if (result) {
      console.log('✅ Lançamento criado com sucesso:', result.id);
      return result.id;
    } else {
      console.error('❌ Falha ao criar lançamento');
      return null;
    }
  } catch (error) {
    console.error('💥 Erro crítico ao gerar lançamento mensal:', error);
    return null;
  }
};

/**
 * Atualiza o valor do lançamento quando uma quota é paga
 */
export const updateLancamentoOnPayment = async (quotaId: string, quotaValue: number): Promise<boolean> => {
  try {
    console.log(`💳 Processando pagamento da quota ${quotaId} com valor ${quotaValue}`);
    
    // Buscar a quota para obter o ID do lançamento associado
    const { data: quota, error: quotaError } = await supabase
      .from('quotas')
      .select('fluxo_caixa_id, mes, ano')
      .eq('id', quotaId)
      .single();
    
    if (quotaError || !quota) {
      console.error('❌ Erro ao buscar quota:', quotaError);
      return false;
    }
    
    if (!quota.fluxo_caixa_id) {
      console.log('⚠️ Quota não possui lançamento associado');
      return true; // Não é erro crítico
    }
    
    // Buscar o lançamento atual
    const { data: lancamento, error: lancamentoError } = await supabase
      .from('fluxo_caixa')
      .select('valor')
      .eq('id', quota.fluxo_caixa_id)
      .single();
    
    if (lancamentoError || !lancamento) {
      console.error('❌ Erro ao buscar lançamento:', lancamentoError);
      return false;
    }
    
    // Incrementar o valor do lançamento
    const novoValor = lancamento.valor + quotaValue;
    
    console.log(`📈 Incrementando valor do lançamento de ${lancamento.valor} para ${novoValor}`);
    
    // Atualizar o lançamento
    const updateResult = await updateLancamento(quota.fluxo_caixa_id, {
      valor: novoValor
    });
    
    if (updateResult) {
      console.log('✅ Valor do lançamento atualizado com sucesso');
      return true;
    } else {
      console.error('❌ Falha ao atualizar valor do lançamento');
      return false;
    }
  } catch (error) {
    console.error('💥 Erro crítico ao processar pagamento:', error);
    return false;
  }
};

/**
 * Cria um lançamento específico para multa regularizada
 */
export const createFineEntry = async (quota: Quota, fineValue: number): Promise<boolean> => {
  try {
    console.log(`🎯 Criando lançamento de multa para quota ${quota.id} com valor ${fineValue}`);
    
    const monthNames = [
      'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
      'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
    ];
    
    // Buscar nome do morador
    const moradorNome = quota.moradores?.nome || 'Morador Desconhecido';
    
    const fineEntryData = {
      tipo: 'entrada' as const,
      categoria: 'Multas',
      descricao: `Multa - ${moradorNome} - ${monthNames[quota.mes - 1]}/${quota.ano}`,
      valor: fineValue,
      data: new Date().toISOString().split('T')[0], // Data atual
      responsavel_id: null,
      anexo: null
    };
    
    console.log('📝 Criando lançamento de multa:', fineEntryData);
    
    const result = await createLancamento(fineEntryData);
    
    if (result) {
      console.log('✅ Lançamento de multa criado com sucesso:', result.id);
      return true;
    } else {
      console.error('❌ Falha ao criar lançamento de multa');
      return false;
    }
  } catch (error) {
    console.error('💥 Erro crítico ao criar lançamento de multa:', error);
    return false;
  }
};

/**
 * Associa um lançamento a todas as quotas de um mês
 */
export const associateLancamentoToQuotas = async (
  quotas: Omit<Quota, 'id' | 'created_at' | 'updated_at'>[],
  lancamentoId: string
): Promise<boolean> => {
  try {
    console.log(`🔗 Associando lançamento ${lancamentoId} a ${quotas.length} quotas`);
    
    // Para cada quota gerada, associar o ID do lançamento
    for (const quota of quotas) {
      const { error } = await supabase
        .from('quotas')
        .update({ fluxo_caixa_id: lancamentoId })
        .eq('morador_id', quota.morador_id)
        .eq('mes', quota.mes)
        .eq('ano', quota.ano);
      
      if (error) {
        console.error(`❌ Erro ao associar lançamento à quota ${quota.morador_id}:`, error);
        return false;
      }
    }
    
    console.log('✅ Todas as quotas foram associadas ao lançamento');
    return true;
  } catch (error) {
    console.error('💥 Erro crítico ao associar lançamento às quotas:', error);
    return false;
  }
};
