
import React, { useState } from 'react';
import { Settings } from 'lucide-react';
import { useMoradores } from '@/hooks/useMoradores';
import { useSettings } from '@/hooks/useSettings';
import { generateCustomPeriodQuotas } from '@/utils/quota-generation-helpers';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { QuotaGeneratorForm } from './QuotaGeneratorForm';
import { QuotaInfoSection } from './QuotaInfoSection';

interface QuotaGeneratorDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onGenerateQuotas: (quotas: any[]) => void;
  isGenerating?: boolean;
}

export const QuotaGeneratorDialog: React.FC<QuotaGeneratorDialogProps> = ({
  isOpen,
  onClose,
  onGenerateQuotas,
  isGenerating = false
}) => {
  const [generationType, setGenerationType] = useState<'current' | 'custom'>('current');
  const [startMonth, setStartMonth] = useState<number>(1);
  const [startYear, setStartYear] = useState<number>(new Date().getFullYear());
  const [includeExempt, setIncludeExempt] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  
  const { moradores } = useMoradores();
  const { getConfigValue } = useSettings();

  const exemptCount = moradores?.filter(m => m.isento_quotas).length || 0;
  const activeCount = (moradores?.length || 0) - exemptCount;
  const valorQuota = getConfigValue('valor_quota') ? parseInt(getConfigValue('valor_quota')) : 2000;

  const months = [
    { value: 1, label: 'Janeiro' },
    { value: 2, label: 'Fevereiro' },
    { value: 3, label: 'Março' },
    { value: 4, label: 'Abril' },
    { value: 5, label: 'Maio' },
    { value: 6, label: 'Junho' },
    { value: 7, label: 'Julho' },
    { value: 8, label: 'Agosto' },
    { value: 9, label: 'Setembro' },
    { value: 10, label: 'Outubro' },
    { value: 11, label: 'Novembro' },
    { value: 12, label: 'Dezembro' }
  ];

  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 5 }, (_, i) => currentYear - i);

  const handleGenerate = () => {
    if (!moradores || moradores.length === 0) {
      alert('Nenhum morador encontrado para gerar quotas.');
      return;
    }

    const targetCount = includeExempt ? moradores.length : activeCount;
    
    if (targetCount === 0) {
      alert(includeExempt 
        ? 'Nenhum morador encontrado.' 
        : 'Apenas moradores isentos encontrados. Marque a opção "Incluir Isentos" se desejar gerar quotas para eles.'
      );
      return;
    }

    setShowConfirmation(true);
  };

  const confirmGeneration = async () => {
    try {
      let quotas;

      if (generationType === 'current') {
        // Gerar quotas para o mês atual
        const currentDate = new Date();
        const currentMonth = currentDate.getMonth() + 1;
        const currentYear = currentDate.getFullYear();
        quotas = await generateCustomPeriodQuotas(moradores, currentMonth, currentYear, includeExempt);
      } else {
        quotas = await generateCustomPeriodQuotas(moradores, startMonth, startYear, includeExempt);
      }

      // Filtrar quotas se não incluir isentos
      if (!includeExempt) {
        const exemptIds = moradores.filter(m => m.isento_quotas).map(m => m.id);
        quotas = quotas.filter(q => !exemptIds.includes(q.morador_id));
      }

      onGenerateQuotas(quotas);
      onClose();
      setShowConfirmation(false);
    } catch (error) {
      console.error('Erro ao gerar quotas:', error);
      setShowConfirmation(false);
    }
  };

  const targetCount = includeExempt ? moradores?.length || 0 : activeCount;
  const typeLabel = generationType === 'current'
    ? 'para o mês atual'
    : `personalizadas desde ${months.find(m => m.value === startMonth)?.label}/${startYear}`;
  const exemptLabel = includeExempt ? ' (incluindo moradores isentos)' : ' (apenas moradores ativos)';

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-md max-h-[90vh] flex flex-col">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Gerar Quotas em Massa
            </DialogTitle>
            <DialogDescription>
              Configure o período e opções para geração automática de quotas.
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-y-auto py-4 space-y-6">
            <QuotaInfoSection
              moradores={moradores || []}
              valorQuota={valorQuota}
              includeExempt={includeExempt}
              generationType={generationType}
              startMonth={startMonth}
              startYear={startYear}
              months={months}
            />

            <QuotaGeneratorForm
              generationType={generationType}
              setGenerationType={setGenerationType}
              startMonth={startMonth}
              setStartMonth={setStartMonth}
              startYear={startYear}
              setStartYear={setStartYear}
              includeExempt={includeExempt}
              setIncludeExempt={setIncludeExempt}
              moradores={moradores || []}
              months={months}
              years={years}
            />
          </div>

          <div className="flex justify-end gap-3 pt-4 border-t flex-shrink-0">
            <Button variant="outline" onClick={onClose}>
              Cancelar
            </Button>
            <Button onClick={handleGenerate} disabled={isGenerating}>
              {isGenerating ? 'Gerando...' : 'Gerar Quotas'}
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Confirmation Dialog */}
      <Dialog open={showConfirmation} onOpenChange={setShowConfirmation}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Confirmar Geração de Quotas</DialogTitle>
            <DialogDescription>
              Deseja gerar quotas {typeLabel} para {targetCount} moradores{exemptLabel}?
            </DialogDescription>
          </DialogHeader>

          <div className="flex justify-end gap-3 pt-4">
            <Button variant="outline" onClick={() => setShowConfirmation(false)}>
              Cancelar
            </Button>
            <Button onClick={confirmGeneration}>
              Gerar Quotas
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};
