
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.45.0'
import { corsHeaders } from '../_shared/cors.ts'

const supabaseUrl = Deno.env.get('SUPABASE_URL')!
const supabaseServiceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders })
  }

  try {
    // Debug: Log all request headers
    console.log('🔍 [DEBUG] All request headers:', Object.fromEntries(req.headers.entries()))
    console.log('🔍 [DEBUG] Request method:', req.method)
    console.log('🔍 [DEBUG] Request URL:', req.url)

    // Create Supabase admin client
    const supabaseAdmin = createClient(supabaseUrl, supabaseServiceRoleKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    })

    // Verify current user token
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Authorization header required' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: userError } = await supabaseAdmin.auth.getUser(token)

    if (userError || !user) {
      return new Response(
        JSON.stringify({ error: 'Invalid token' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Check admin privileges
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (profileError || !profile || profile.role !== 'admin') {
      return new Response(
        JSON.stringify({ error: 'Admin privileges required' }),
        { status: 403, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Parse request body
    let requestData: any
    try {
      const bodyText = await req.text()

      // Debug temporário
      console.log('🔍 [DEBUG] Raw body text received:', bodyText)
      console.log('🔍 [DEBUG] Body text length:', bodyText?.length)
      console.log('🔍 [DEBUG] Content-Type header:', req.headers.get('content-type'))

      if (!bodyText || bodyText.trim() === '') {
        console.error('❌ [DEBUG] Empty or whitespace-only request body')
        return new Response(
          JSON.stringify({ error: 'Missing required fields: email, password, name, apartment_id' }),
          { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        )
      }

      try {
        requestData = JSON.parse(bodyText)
      } catch (parseError) {
        return new Response(
          JSON.stringify({ error: 'Invalid JSON in request body' }),
          { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        )
      }
    } catch (bodyError) {
      return new Response(
        JSON.stringify({ error: 'Failed to read request body' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Extract and validate data
    const { email, password, name, apartment_id } = requestData

    // Enhanced validation
    if (!email || typeof email !== 'string' || !email.trim()) {
      return new Response(
        JSON.stringify({ error: 'Email is required and must be a valid string' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    if (!password || typeof password !== 'string' || password.length < 6) {
      return new Response(
        JSON.stringify({ error: 'Password is required and must be at least 6 characters long' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    if (!name || typeof name !== 'string' || !name.trim()) {
      return new Response(
        JSON.stringify({ error: 'Name is required and must be a valid string' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    if (!apartment_id || typeof apartment_id !== 'string' || !apartment_id.trim()) {
      return new Response(
        JSON.stringify({ error: 'Apartment ID is required and must be a valid string' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Clean and normalize data
    const cleanEmail = email.trim().toLowerCase()
    const cleanName = name.trim()
    const cleanApartmentId = apartment_id.trim().toUpperCase()

    // Check if email already exists in profiles
    const { data: existingProfile } = await supabaseAdmin
      .from('profiles')
      .select('id, email')
      .eq('email', cleanEmail)
      .maybeSingle()

    if (existingProfile) {
      return new Response(
        JSON.stringify({ error: 'Este email já está em uso' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Create user with Admin API
    const { data: newUser, error: createUserError } = await supabaseAdmin.auth.admin.createUser({
      email: cleanEmail,
      password: password,
      email_confirm: true
    })

    if (createUserError) {
      if (createUserError.message?.includes('already registered')) {
        return new Response(
          JSON.stringify({ error: 'Este email já está registado' }),
          { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        )
      }
      return new Response(
        JSON.stringify({ error: `Erro ao criar utilizador: ${createUserError.message}` }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    if (!newUser?.user?.id) {
      return new Response(
        JSON.stringify({ error: 'Falha ao obter ID do utilizador criado' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Create profile entry
    const { error: profileInsertError } = await supabaseAdmin
      .from('profiles')
      .insert({
        id: newUser.user.id,
        email: cleanEmail,
        name: cleanName,
        role: 'resident',
        apartment_id: cleanApartmentId,
        first_login: true
      })

    if (profileInsertError) {
      // Clean up created user if profile creation fails
      try {
        await supabaseAdmin.auth.admin.deleteUser(newUser.user.id)
      } catch (cleanupError) {
        // Cleanup failed, but we still need to return the original error
      }

      return new Response(
        JSON.stringify({ error: `Erro ao criar perfil: ${profileInsertError.message}` }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Create apartment access entry
    const { error: accessError } = await supabaseAdmin
      .from('apartment_access')
      .insert({
        user_id: newUser.user.id,
        apartment_id: cleanApartmentId,
        granted_by: user.id,
        is_active: true
      })

    if (accessError) {
      // Clean up created user and profile if access creation fails
      try {
        await supabaseAdmin.from('profiles').delete().eq('id', newUser.user.id)
        await supabaseAdmin.auth.admin.deleteUser(newUser.user.id)
      } catch (cleanupError) {
        // Cleanup failed, but we still need to return the original error
      }

      return new Response(
        JSON.stringify({ error: `Erro ao conceder acesso: ${accessError.message}` }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Return success response with password for credentials display
    const successResponse = {
      success: true,
      user_id: newUser.user.id,
      email: cleanEmail,
      name: cleanName,
      apartment_id: cleanApartmentId,
      password: password // Include password in response for credentials display
    }

    return new Response(
      JSON.stringify(successResponse),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )

  } catch (error) {
    return new Response(
      JSON.stringify({
        error: 'Erro interno do servidor',
        details: error instanceof Error ? error.message : 'Unknown error'
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }
})
