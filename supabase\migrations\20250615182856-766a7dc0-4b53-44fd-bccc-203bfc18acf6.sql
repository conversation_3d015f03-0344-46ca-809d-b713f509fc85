
-- <PERSON><PERSON>r tabela apartment_access para relacionar usuários a apartamentos
CREATE TABLE public.apartment_access (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  apartment_id TEXT NOT NULL,
  granted_by UUID REFERENCES auth.users(id) NOT NULL,
  granted_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  revoked_at TIMESTAMP WITH TIME ZONE,
  is_active BOOLEAN NOT NULL DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Adicionar campo apartment_id na tabela profiles se não existir
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS apartment_id TEXT;

-- Adicionar campo first_login na tabela profiles se não existir
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS first_login BO<PERSON>EAN DEFAULT true;

-- Habilitar RLS na tabela apartment_access
ALTER TABLE public.apartment_access ENABLE ROW LEVEL SECURITY;

-- Política para admins visualizarem todos os acessos
CREATE POLICY "Admins can view all apartment access" 
  ON public.apartment_access 
  FOR SELECT 
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Política para admins gerenciarem acessos
CREATE POLICY "Admins can manage apartment access" 
  ON public.apartment_access 
  FOR ALL 
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Política para moradores visualizarem apenas seu próprio acesso
CREATE POLICY "Residents can view own apartment access" 
  ON public.apartment_access 
  FOR SELECT 
  USING (user_id = auth.uid());

-- Atualizar política de profiles para incluir apartment_id
DROP POLICY IF EXISTS "Residents can view own profile" ON public.profiles;
CREATE POLICY "Residents can view own profile" 
  ON public.profiles 
  FOR SELECT 
  USING (
    id = auth.uid() OR 
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Política para residents atualizarem seu próprio perfil
CREATE POLICY "Residents can update own profile" 
  ON public.profiles 
  FOR UPDATE 
  USING (id = auth.uid())
  WITH CHECK (id = auth.uid());

-- Função para criar usuário morador
CREATE OR REPLACE FUNCTION public.create_resident_user(
  p_email TEXT,
  p_password TEXT,
  p_name TEXT,
  p_apartment_id TEXT,
  p_admin_id UUID
)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  new_user_id UUID;
  result jsonb;
BEGIN
  -- Verificar se o usuário atual é admin
  IF NOT EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() AND role = 'admin'
  ) THEN
    RAISE EXCEPTION 'Acesso negado: privilégios de administrador necessários';
  END IF;

  -- Verificar se email já está em uso
  IF EXISTS (
    SELECT 1 FROM public.profiles WHERE email = p_email
  ) THEN
    RAISE EXCEPTION 'Email já está em uso';
  END IF;

  -- Gerar ID para o novo usuário
  new_user_id := gen_random_uuid();

  -- Inserir na tabela profiles
  INSERT INTO public.profiles (
    id, 
    email, 
    name, 
    role, 
    apartment_id,
    first_login,
    created_at,
    updated_at
  ) VALUES (
    new_user_id,
    p_email,
    p_name,
    'resident',
    p_apartment_id,
    true,
    now(),
    now()
  );

  -- Inserir na tabela apartment_access
  INSERT INTO public.apartment_access (
    user_id,
    apartment_id,
    granted_by,
    is_active
  ) VALUES (
    new_user_id,
    p_apartment_id,
    p_admin_id,
    true
  );

  -- Retornar resultado
  result := jsonb_build_object(
    'user_id', new_user_id,
    'email', p_email,
    'name', p_name,
    'apartment_id', p_apartment_id,
    'success', true
  );

  RETURN result;
END;
$$;

-- Função para listar moradores com status de acesso
CREATE OR REPLACE FUNCTION public.get_residents_with_access_status()
RETURNS TABLE(
  morador_id UUID,
  morador_nome TEXT,
  morador_email TEXT,
  apartamento TEXT,
  has_access BOOLEAN,
  user_id UUID,
  access_granted_at TIMESTAMP WITH TIME ZONE,
  first_login BOOLEAN
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Verificar se o usuário atual é admin
  IF NOT EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() AND role = 'admin'
  ) THEN
    RAISE EXCEPTION 'Acesso negado: privilégios de administrador necessários';
  END IF;

  RETURN QUERY
  SELECT 
    m.id as morador_id,
    m.nome as morador_nome,
    m.email as morador_email,
    m.apartamento,
    CASE 
      WHEN aa.is_active = true THEN true 
      ELSE false 
    END as has_access,
    aa.user_id,
    aa.granted_at as access_granted_at,
    p.first_login
  FROM public.moradores m
  LEFT JOIN public.apartment_access aa ON m.apartamento = aa.apartment_id AND aa.is_active = true
  LEFT JOIN public.profiles p ON aa.user_id = p.id
  ORDER BY m.apartamento, m.nome;
END;
$$;

-- Função para revogar acesso de apartamento
CREATE OR REPLACE FUNCTION public.revoke_apartment_access(
  p_user_id UUID,
  p_apartment_id TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Verificar se o usuário atual é admin
  IF NOT EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() AND role = 'admin'
  ) THEN
    RAISE EXCEPTION 'Acesso negado: privilégios de administrador necessários';
  END IF;

  -- Revogar acesso
  UPDATE public.apartment_access 
  SET 
    is_active = false,
    revoked_at = now(),
    updated_at = now()
  WHERE user_id = p_user_id 
    AND apartment_id = p_apartment_id 
    AND is_active = true;

  RETURN true;
END;
$$;

-- Índices para melhor performance
CREATE INDEX IF NOT EXISTS idx_apartment_access_user_id ON public.apartment_access(user_id);
CREATE INDEX IF NOT EXISTS idx_apartment_access_apartment_id ON public.apartment_access(apartment_id);
CREATE INDEX IF NOT EXISTS idx_apartment_access_active ON public.apartment_access(is_active);
CREATE INDEX IF NOT EXISTS idx_profiles_apartment_id ON public.profiles(apartment_id);
