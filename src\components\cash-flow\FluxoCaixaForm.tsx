
import React from 'react';
import { useForm } from 'react-hook-form';
import { FluxoCaixa } from '@/types';
import { format } from 'date-fns';
import FormField from './FormField';
import TypeSelector from './TypeSelector';
import CategorySelector from './CategorySelector';
import ValueInput from './ValueInput';
import FormActions from './FormActions';
import { formatNumberWithDots } from '@/utils/cash-flow-helpers';

type FluxoCaixaFormProps = {
  initialData?: FluxoCaixa | null;
  onSubmit: (data: any) => Promise<void>;
  onCancel: () => void;
};

const FluxoCaixaForm: React.FC<FluxoCaixaFormProps> = ({ initialData, onSubmit, onCancel }) => {
  const { register, handleSubmit, formState: { errors, isSubmitting }, watch } = useForm({
    defaultValues: initialData ? {
      ...initialData,
      data: initialData.data ? format(new Date(initialData.data), 'yyyy-MM-dd') : '',
      valor: initialData.valor ? formatNumberWithDots(initialData.valor) : '',
    } : {
      tipo: 'entrada',
      categoria: '',
      descricao: '',
      valor: '',
      data: format(new Date(), 'yyyy-MM-dd'),
    }
  });

  const tipoValue = watch('tipo');
  
  React.useEffect(() => {
    console.log('💰 FluxoCaixaForm: Tipo field value changed to:', JSON.stringify(tipoValue));
  }, [tipoValue]);

  const handleFormSubmit = async (data: any) => {
    console.log('📋 FluxoCaixaForm: ========== FORM SUBMISSION START ==========');
    console.log('📋 FluxoCaixaForm: Form submitted with raw data:', JSON.stringify(data, null, 2));
    console.log('📋 FluxoCaixaForm: Raw tipo value:', JSON.stringify(data.tipo));
    console.log('📋 FluxoCaixaForm: Raw valor value:', JSON.stringify(data.valor));
    
    try {
      console.log('📋 FluxoCaixaForm: Calling onSubmit...');
      await onSubmit(data);
      console.log('📋 FluxoCaixaForm: onSubmit completed successfully');
    } catch (error) {
      console.error('❌ FluxoCaixaForm: Error in form submission:', error);
      // O erro já será tratado no hook pai
    }
    
    console.log('📋 FluxoCaixaForm: ========== FORM SUBMISSION END ==========');
  };

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
      <FormField
        label="Descrição"
        required
        error={errors.descricao?.message?.toString()}
      >
        <input
          {...register('descricao', { required: 'Descrição é obrigatória' })}
          type="text"
          className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-200 focus:border-primary-300"
          placeholder="Descrição do registro"
        />
      </FormField>

      <div className="grid grid-cols-2 gap-4">
        <TypeSelector register={register} errors={errors} value={tipoValue} />
        <CategorySelector register={register} errors={errors} />
      </div>

      <FormField
        label="Data"
        required
        error={errors.data?.message?.toString()}
      >
        <input
          {...register('data', { required: 'Data é obrigatória' })}
          type="date"
          className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-200 focus:border-primary-300"
        />
      </FormField>

      <ValueInput register={register} errors={errors} />

      <FormActions onCancel={onCancel} isSubmitting={isSubmitting} />
    </form>
  );
};

export default FluxoCaixaForm;
