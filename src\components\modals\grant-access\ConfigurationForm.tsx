
import React, { useState } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { DialogFooter } from '@/components/ui/dialog';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Eye, EyeOff, RefreshCw } from 'lucide-react';
import { GrantAccessFormValues } from './types';
import { generateSecurePassword } from '@/utils/resident-access-helpers';
import { toast } from 'sonner';

interface ConfigurationFormProps {
  form: UseFormReturn<GrantAccessFormValues>;
  resident: { nome: string; apartamento: string; email: string };
  generatedPassword: string;
  setGeneratedPassword: (password: string) => void;
  onSubmit: (e: React.FormEvent) => void;
  onClose: () => void;
  isLoading: boolean;
  isProcessing: boolean;
}

export const ConfigurationForm: React.FC<ConfigurationFormProps> = ({
  form,
  resident,
  generatedPassword,
  setGeneratedPassword,
  onSubmit,
  onClose,
  isLoading,
  isProcessing
}) => {
  const [showPassword, setShowPassword] = useState(false);
  const useGeneratedPassword = form.watch('useGeneratedPassword');

  const handleGenerateNewPassword = () => {
    const password = generateSecurePassword();
    setGeneratedPassword(password);
    console.log('🔄 [ConfigurationForm] New password generated:', password);
    toast.success('Nova senha gerada!');
  };

  return (
    <div className="space-y-4">
      <div className="p-4 bg-blue-50 rounded-lg">
        <h4 className="font-medium text-blue-900">Apartamento: {resident.apartamento}</h4>
        <p className="text-sm text-blue-700">Nome: {resident.nome}</p>
      </div>

      <Form {...form}>
        <form onSubmit={onSubmit} className="space-y-4">
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email de Acesso</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    type="email"
                    value={resident.email}
                    disabled
                    className="bg-gray-50 text-gray-700"
                  />
                </FormControl>
                <FormMessage />
                <p className="text-xs text-gray-500 mt-1">
                  Email registrado no sistema para este morador
                </p>
              </FormItem>
            )}
          />

          <div className="space-y-4">
            <FormField
              control={form.control}
              name="useGeneratedPassword"
              render={({ field }) => (
                <FormItem className="flex items-center justify-between">
                  <FormLabel>Gerar senha automaticamente</FormLabel>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            {useGeneratedPassword ? (
              <div className="space-y-2">
                <Label>Senha Gerada</Label>
                <div className="flex gap-2">
                  <Input
                    type={showPassword ? 'text' : 'password'}
                    value={generatedPassword}
                    readOnly
                    className="bg-gray-50"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    onClick={handleGenerateNewPassword}
                  >
                    <RefreshCw className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ) : (
              <FormField
                control={form.control}
                name="customPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Senha Personalizada</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type={showPassword ? 'text' : 'password'}
                        placeholder="Digite uma senha"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}
          </div>

          <div className="border-t pt-4">
            <h4 className="font-medium mb-3">Opções de Compartilhamento</h4>

            <div className="space-y-3">
              <FormField
                control={form.control}
                name="sendEmailToResident"
                render={({ field }) => (
                  <FormItem className="flex items-center justify-between">
                    <FormLabel>Enviar por email para o morador</FormLabel>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          </div>

          <DialogFooter className="pt-4">
            <Button 
              type="button" 
              variant="outline" 
              onClick={onClose} 
              disabled={isLoading || isProcessing}
            >
              Cancelar
            </Button>
            <Button 
              type="submit" 
              disabled={isLoading || isProcessing}
            >
              {isLoading || isProcessing ? 'Criando...' : 'Criar Acesso'}
            </Button>
          </DialogFooter>
        </form>
      </Form>
    </div>
  );
};
