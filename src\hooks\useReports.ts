/**
 * Hook useReports - Sistema Prédio Azul
 * 
 * Hook personalizado para gerenciar relatórios, incluindo
 * geração, cache, histórico e integração com React Query.
 */

import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import {
  ReportType,
  ReportFilters,
  ReportData,
  ReportHistory,
  ReportStatus
} from '@/types/reports';
import { getReportData } from '@/services/reports';
import { generatePDF } from '@/components/reports/PDFGenerator';

/**
 * Interface para o estado do hook
 */
interface UseReportsState {
  isGenerating: boolean;
  currentReportType: ReportType | null;
  showFiltersModal: boolean;
  recentReports: ReportHistory[];
}

/**
 * Interface para o retorno do hook
 */
interface UseReportsReturn extends UseReportsState {
  // Funções de controle
  openFiltersModal: (reportType: ReportType) => void;
  closeFiltersModal: () => void;
  generateReport: (reportType: ReportType, filters: ReportFilters) => Promise<void>;
  
  // Dados e queries
  reportData: ReportData | undefined;
  isLoadingData: boolean;
  error: Error | null;
  
  // Funções utilitárias
  clearCache: () => void;
  getReportHistory: () => ReportHistory[];
  formatReportTitle: (reportType: ReportType) => string;
}

/**
 * Hook principal para gerenciamento de relatórios
 */
export const useReports = (): UseReportsReturn => {
  const queryClient = useQueryClient();
  
  // Estados locais
  const [state, setState] = useState<UseReportsState>({
    isGenerating: false,
    currentReportType: null,
    showFiltersModal: false,
    recentReports: []
  });

  // Query para dados do relatório atual
  const {
    data: reportData,
    isLoading: isLoadingData,
    error
  } = useQuery({
    queryKey: ['report-data', state.currentReportType],
    queryFn: () => {
      if (!state.currentReportType) return null;
      // Esta query será executada apenas quando necessário
      return null;
    },
    enabled: false // Desabilitada por padrão, será executada manualmente
  });

  /**
   * Mutation para gerar relatório
   */
  const generateReportMutation = useMutation({
    mutationFn: async ({ reportType, filters }: { reportType: ReportType; filters: ReportFilters }) => {
      console.log('🔄 Iniciando geração de relatório:', { reportType, filters });
      
      // Buscar dados do relatório
      const data = await getReportData(reportType, filters);
      
      // Gerar PDF
      const filename = await generatePDF(data);
      
      return { data, filename };
    },
    onMutate: () => {
      setState(prev => ({ ...prev, isGenerating: true }));
    },
    onSuccess: ({ data, filename }, { reportType }) => {
      console.log('✅ Relatório gerado com sucesso:', filename);
      
      // Adicionar ao histórico
      const newReport: ReportHistory = {
        id: Date.now().toString(),
        type: reportType,
        title: formatReportTitle(reportType),
        generatedAt: new Date().toISOString(),
        generatedBy: 'Sistema', // TODO: Pegar usuário atual
        status: 'completed',
        downloadUrl: filename,
        filters: data.filters
      };

      setState(prev => ({
        ...prev,
        isGenerating: false,
        showFiltersModal: false,
        recentReports: [newReport, ...prev.recentReports.slice(0, 9)] // Manter apenas os 10 mais recentes
      }));

      toast.success(`Relatório ${formatReportTitle(reportType)} gerado com sucesso!`, {
        description: `Arquivo: ${filename}`
      });
    },
    onError: (error, { reportType }) => {
      console.error('❌ Erro ao gerar relatório:', error);
      
      setState(prev => ({ ...prev, isGenerating: false }));
      
      toast.error(`Erro ao gerar relatório ${formatReportTitle(reportType)}`, {
        description: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }
  });

  /**
   * Abre o modal de filtros para um tipo específico de relatório
   */
  const openFiltersModal = (reportType: ReportType) => {
    setState(prev => ({
      ...prev,
      currentReportType: reportType,
      showFiltersModal: true
    }));
  };

  /**
   * Fecha o modal de filtros
   */
  const closeFiltersModal = () => {
    setState(prev => ({
      ...prev,
      showFiltersModal: false,
      currentReportType: null
    }));
  };

  /**
   * Gera um relatório com os filtros especificados
   */
  const generateReport = async (reportType: ReportType, filters: ReportFilters) => {
    try {
      await generateReportMutation.mutateAsync({ reportType, filters });
    } catch (error) {
      // Erro já tratado no onError da mutation
      console.error('Erro na geração do relatório:', error);
    }
  };

  /**
   * Limpa o cache de relatórios
   */
  const clearCache = () => {
    queryClient.invalidateQueries({ queryKey: ['report-data'] });
    toast.success('Cache de relatórios limpo com sucesso!');
  };

  /**
   * Retorna o histórico de relatórios
   */
  const getReportHistory = (): ReportHistory[] => {
    return state.recentReports;
  };

  /**
   * Formata o título do relatório baseado no tipo
   */
  const formatReportTitle = (reportType: ReportType): string => {
    const titles = {
      quotas: 'Relatório de Quotas',
      residents: 'Relatório de Moradores',
      financial: 'Relatório Financeiro',
      committee: 'Relatório da Comissão',
      fines: 'Relatório de Multas'
    };
    return titles[reportType] || 'Relatório';
  };

  /**
   * Função utilitária para obter estatísticas dos relatórios
   */
  const getReportStats = () => {
    const totalReports = state.recentReports.length;
    const completedReports = state.recentReports.filter(r => r.status === 'completed').length;
    const errorReports = state.recentReports.filter(r => r.status === 'error').length;
    
    const reportsByType = state.recentReports.reduce((acc, report) => {
      acc[report.type] = (acc[report.type] || 0) + 1;
      return acc;
    }, {} as Record<ReportType, number>);

    return {
      total: totalReports,
      completed: completedReports,
      errors: errorReports,
      byType: reportsByType
    };
  };

  /**
   * Função para reexecutar um relatório do histórico
   */
  const regenerateReport = async (historyItem: ReportHistory) => {
    await generateReport(historyItem.type, historyItem.filters);
  };

  /**
   * Função para remover um item do histórico
   */
  const removeFromHistory = (reportId: string) => {
    setState(prev => ({
      ...prev,
      recentReports: prev.recentReports.filter(r => r.id !== reportId)
    }));
    toast.success('Relatório removido do histórico');
  };

  /**
   * Função para validar filtros antes da geração
   */
  const validateFilters = (reportType: ReportType, filters: ReportFilters): boolean => {
    // Validações básicas
    if (filters.startDate && filters.endDate) {
      const start = new Date(filters.startDate);
      const end = new Date(filters.endDate);
      
      if (start > end) {
        toast.error('Data inicial não pode ser maior que a data final');
        return false;
      }
    }

    // Validações específicas por tipo
    switch (reportType) {
      case 'quotas':
        // Adicionar validações específicas para quotas se necessário
        break;
      case 'residents':
        // Adicionar validações específicas para moradores se necessário
        break;
      case 'financial':
        // Adicionar validações específicas para financeiro se necessário
        break;
      case 'committee':
        // Adicionar validações específicas para comissão se necessário
        break;
    }

    return true;
  };

  return {
    // Estado
    ...state,
    
    // Dados
    reportData,
    isLoadingData,
    error,
    
    // Funções principais
    openFiltersModal,
    closeFiltersModal,
    generateReport: async (reportType: ReportType, filters: ReportFilters) => {
      if (validateFilters(reportType, filters)) {
        await generateReport(reportType, filters);
      }
    },
    
    // Funções utilitárias
    clearCache,
    getReportHistory,
    formatReportTitle,
    
    // Funções extras (não expostas na interface principal, mas disponíveis)
    getReportStats,
    regenerateReport,
    removeFromHistory
  } as UseReportsReturn & {
    getReportStats: () => any;
    regenerateReport: (historyItem: ReportHistory) => Promise<void>;
    removeFromHistory: (reportId: string) => void;
  };
};
