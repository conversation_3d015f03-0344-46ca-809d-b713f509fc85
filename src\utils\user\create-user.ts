
import { supabase } from '@/integrations/supabase/client';

export const createUser = async (
  email: string,
  password: string,
  userData: { name: string; role: string; status: string }
) => {
  try {
    // Check if the email is already in use by querying the profiles table
    const { data: existingProfiles, error: checkError } = await supabase
      .from('profiles')
      .select('id')
      .eq('email', email);
      
    if (checkError) {
      console.error('Error checking existing profiles:', checkError);
      throw checkError;
    }
    
    if (existingProfiles && existingProfiles.length > 0) {
      throw new Error('Email already in use');
    }
    
    // Use a regular signup method instead of admin.createUser
    const { data: authData, error: signUpError } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          name: userData.name,
          role: userData.role === 'Administrador' ? 'admin' : 'convidado'
        },
        emailRedirectTo: window.location.origin,
      }
    });

    if (signUpError) {
      console.error('Error creating user:', signUpError);
      throw signUpError;
    }

    if (!authData.user) {
      throw new Error('Failed to create user');
    }

    // Manually insert into profiles since the trigger may not work without admin rights
    const { error: profileError } = await supabase
      .from('profiles')
      .insert({
        id: authData.user.id,
        name: userData.name,
        email: email,
        role: userData.role === 'Administrador' ? 'admin' : 'convidado',
        banned: userData.status === 'Inativo',
        updated_at: new Date().toISOString()
      });

    if (profileError) {
      console.error('Error creating profile:', profileError);
      // Don't throw here, the auth user was created so we can continue
    }

    // Important: Sign back in as the original user
    // This workaround avoids the issue of automatically logging in as the new user
    // We need to retrieve the current user's session first
    const { data: { session } } = await supabase.auth.getSession();
    
    if (session) {
      // If we have a session, we can sign back in with the session
      // This is to prevent being logged out when creating a new user
      setTimeout(async () => {
        try {
          // Try to refresh the session to maintain the current user logged in
          await supabase.auth.refreshSession();
        } catch (e) {
          console.error("Error refreshing session after user creation:", e);
        }
      }, 500);
    }

    return authData.user;
  } catch (error) {
    console.error('Error in createUser:', error);
    throw error;
  }
};
