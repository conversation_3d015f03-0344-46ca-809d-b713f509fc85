
-- <PERSON><PERSON><PERSON><PERSON> políticas RLS recursivas criando funções SECURITY DEFINER

-- 1. <PERSON><PERSON>r função para verificar se o usuário atual é admin
CREATE OR REPLACE FUNCTION public.is_current_user_admin()
RETURNS boolean
LANGUAGE plpgsql
STABLE SECURITY DEFINER
AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND role = 'admin'
  );
END;
$$;

-- 2. Remover políticas problemáticas e recriar sem recursão
DROP POLICY IF EXISTS "Residents can view own profile" ON public.profiles;
DROP POLICY IF EXISTS "Residents can update own profile" ON public.profiles;
DROP POLICY IF EXISTS "Admins can view all apartment access" ON public.apartment_access;
DROP POLICY IF EXISTS "Admins can manage apartment access" ON public.apartment_access;

-- 3. Recriar políticas usando a função SECURITY DEFINER
CREATE POLICY "Users can view own profile or admins can view all" 
  ON public.profiles 
  FOR SELECT 
  USING (
    id = auth.uid() OR public.is_current_user_admin()
  );

CREATE POLICY "Users can update own profile" 
  ON public.profiles 
  FOR UPDATE 
  USING (id = auth.uid())
  WITH CHECK (id = auth.uid());

-- 4. Políticas para apartment_access
CREATE POLICY "Admins can view all apartment access" 
  ON public.apartment_access 
  FOR SELECT 
  USING (public.is_current_user_admin());

CREATE POLICY "Admins can manage apartment access" 
  ON public.apartment_access 
  FOR ALL 
  USING (public.is_current_user_admin());

-- 5. Adicionar políticas RLS nas outras tabelas principais se não existirem

-- Moradores
ALTER TABLE public.moradores ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Admins can manage moradores" ON public.moradores;
CREATE POLICY "Admins can manage moradores" 
  ON public.moradores 
  FOR ALL 
  USING (public.is_current_user_admin());

-- Quotas
ALTER TABLE public.quotas ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Admins can manage quotas" ON public.quotas;
DROP POLICY IF EXISTS "Residents can view own quotas" ON public.quotas;

CREATE POLICY "Admins can manage quotas" 
  ON public.quotas 
  FOR ALL 
  USING (public.is_current_user_admin());

CREATE POLICY "Residents can view own quotas" 
  ON public.quotas 
  FOR SELECT 
  USING (
    EXISTS (
      SELECT 1 FROM public.moradores m 
      JOIN public.profiles p ON m.apartamento = p.apartment_id
      WHERE m.id = quotas.morador_id 
      AND p.id = auth.uid()
    )
  );

-- Fluxo Caixa
ALTER TABLE public.fluxo_caixa ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Admins can manage fluxo_caixa" ON public.fluxo_caixa;
CREATE POLICY "Admins can manage fluxo_caixa" 
  ON public.fluxo_caixa 
  FOR ALL 
  USING (public.is_current_user_admin());

-- Membros Comissão
ALTER TABLE public.membros_comissao ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Admins can manage membros_comissao" ON public.membros_comissao;
DROP POLICY IF EXISTS "Anyone can view membros_comissao" ON public.membros_comissao;

CREATE POLICY "Admins can manage membros_comissao" 
  ON public.membros_comissao 
  FOR ALL 
  USING (public.is_current_user_admin());

CREATE POLICY "Anyone can view active membros_comissao" 
  ON public.membros_comissao 
  FOR SELECT 
  USING (status = 'Ativo');

-- Configurações
ALTER TABLE public.configuracoes ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Admins can manage configuracoes" ON public.configuracoes;
CREATE POLICY "Admins can manage configuracoes" 
  ON public.configuracoes 
  FOR ALL 
  USING (public.is_current_user_admin());

-- Notificações
ALTER TABLE public.notificacoes ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Users can view own notifications" ON public.notificacoes;
CREATE POLICY "Users can view own notifications" 
  ON public.notificacoes 
  FOR SELECT 
  USING (usuario_id = auth.uid());

CREATE POLICY "Users can update own notifications" 
  ON public.notificacoes 
  FOR UPDATE 
  USING (usuario_id = auth.uid())
  WITH CHECK (usuario_id = auth.uid());

-- 6. Garantir que seu usuário <EMAIL> seja admin
-- Primeiro vamos verificar se existe um perfil para esse email
DO $$
DECLARE
    user_exists BOOLEAN;
    user_profile_id UUID;
BEGIN
    -- Verificar se existe perfil com esse email
    SELECT EXISTS(SELECT 1 FROM public.profiles WHERE email = '<EMAIL>') INTO user_exists;
    
    IF user_exists THEN
        -- Atualizar role para admin se já existe
        UPDATE public.profiles 
        SET role = 'admin' 
        WHERE email = '<EMAIL>';
        
        RAISE NOTICE 'Usuário <EMAIL> definido como admin';
    ELSE
        -- Se não existe, vamos criá-lo (assumindo que você já se registrou)
        -- Este bloco só funcionará se você já tiver se registrado no sistema
        RAISE NOTICE 'Perfil não <NAME_EMAIL>. Certifique-se de fazer login primeiro.';
    END IF;
END $$;
