import React, { useState, useEffect } from 'react';
import {
  Settings as SettingsIcon,
  Save,
  CreditCard,
  Tag,
  ArrowUpDown,
  CheckCircle,
  Shield,
  LoaderCircle,
  User,
  Calendar,
  AlertCircle
} from 'lucide-react';
import { toast } from 'sonner';
import { ListManager } from '@/components/settings/ListManager';
import CommitteeRolesManager from '@/components/settings/CommitteeRolesManager';
import { useSettings, ConfigurationType } from '@/hooks/useSettings';
import Sidebar from '@/components/layout/Sidebar';
import Header from '@/components/layout/Header';
import { useSidebar } from '@/contexts/SidebarContext';
import { cn } from '@/lib/utils';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

const Settings = () => {
  const { collapsed, isMobile } = useSidebar();
  const {
    configurations,
    getConfigValue,
    getArrayConfig,
    saveSettings,
    loading,
    isLoadingConfigs
  } = useSettings();

  // General settings state with default values
  const [condominioName, setCondominioName] = useState('');
  const [condominioAddress, setCondominioAddress] = useState('');
  const [quotaValue, setQuotaValue] = useState('');
  const [condominioEmail, setCondominioEmail] = useState('');
  const [condominioPhone, setCondominioPhone] = useState('');
  const [dataLimiteDia, setDataLimiteDia] = useState('');
  const [valorMultaAtraso, setValorMultaAtraso] = useState('');
  const [diasAtrasoMulta, setDiasAtrasoMulta] = useState('');

  // List settings state
  const [categories, setCategories] = useState<string[]>([]);
  const [transactionTypes, setTransactionTypes] = useState<string[]>([]);
  const [situationStatuses, setSituationStatuses] = useState<string[]>([]);

  // Track initial values for general tab
  const [initialGeneralValues, setInitialGeneralValues] = useState({
    name: '',
    address: '',
    quota: '',
    email: '',
    phone: '',
    dataLimiteDia: '',
    valorMultaAtraso: '',
    diasAtrasoMulta: ''
  });

  // Generate array of days for the select
  const daysInMonth = Array.from({ length: 31 }, (_, i) => String(i + 1));

  // Update states when configurations are loaded
  useEffect(() => {
    if (configurations) {
      const name = getConfigValue('condominio_nome') || 'Condomínio Residencial';
      const address = getConfigValue('condominio_endereco') || 'Rua Principal, 123 - Luanda, Angola';
      const quota = getConfigValue('valor_quota') || '30.000';
      const email = getConfigValue('condominio_email') || '<EMAIL>';
      const phone = getConfigValue('condominio_telefone') || '+244 923 456 789';
      const multa = getConfigValue('valor_multa_atraso') || '1000';
      const diasAtraso = getConfigValue('dias_atraso_multa') || '0';

      // Get the day from data_limite_pagamento or default to day 10
      let dia = '10'; // Default value
      const dataLimiteConfig = getConfigValue('data_limite_pagamento');
      if (dataLimiteConfig) {
        try {
          // If it's a full date, extract the day
          if (dataLimiteConfig.includes('-')) {
            const dataParts = new Date(dataLimiteConfig).getDate();
            dia = String(dataParts || 10);
          } else {
            // If it's already just a day number
            dia = dataLimiteConfig;
          }
        } catch (e) {
          console.error('Error parsing data_limite_pagamento:', e);
          dia = '10';
        }
      }

      setCondominioName(name);
      setCondominioAddress(address);
      setQuotaValue(quota);
      setCondominioEmail(email);
      setCondominioPhone(phone);
      setDataLimiteDia(dia);
      setValorMultaAtraso(multa);
      setDiasAtrasoMulta(diasAtraso);

      setInitialGeneralValues({
        name,
        address,
        quota,
        email,
        phone,
        dataLimiteDia: dia,
        valorMultaAtraso: multa,
        diasAtrasoMulta: diasAtraso
      });

      setCategories(getArrayConfig('categorias'));
      setTransactionTypes(getArrayConfig('tipos_transacao'));
      setSituationStatuses(getArrayConfig('status_situacao'));
    }
  }, [configurations, getConfigValue, getArrayConfig]);

  const handleSaveGeneralSettings = async () => {
    const settingsToSave = [
      { nome: 'condominio_nome', valor: condominioName },
      { nome: 'condominio_endereco', valor: condominioAddress },
      { nome: 'valor_quota', valor: quotaValue },
      { nome: 'condominio_email', valor: condominioEmail },
      { nome: 'condominio_telefone', valor: condominioPhone },
      { nome: 'data_limite_pagamento', valor: dataLimiteDia },
      { nome: 'valor_multa_atraso', valor: valorMultaAtraso },
      { nome: 'dias_atraso_multa', valor: diasAtrasoMulta }
    ];

    await saveSettings(settingsToSave);

    // Update initial values after saving
    setInitialGeneralValues({
      name: condominioName,
      address: condominioAddress,
      quota: quotaValue,
      email: condominioEmail,
      phone: condominioPhone,
      dataLimiteDia: dataLimiteDia,
      valorMultaAtraso: valorMultaAtraso,
      diasAtrasoMulta: diasAtrasoMulta
    });
  };

  const handleSaveCategories = async (updatedCategories: string[]) => {
    const settingsToSave = [
      { nome: 'categorias', valor: updatedCategories.join(',') }
    ];
    await saveSettings(settingsToSave);
    setCategories(updatedCategories);
  };

  const handleSaveTransactionTypes = async (updatedTypes: string[]) => {
    const settingsToSave = [
      { nome: 'tipos_transacao', valor: updatedTypes.join(',') }
    ];
    await saveSettings(settingsToSave);
    setTransactionTypes(updatedTypes);
  };

  const handleSaveSituationStatuses = async (updatedStatuses: string[]) => {
    const settingsToSave = [
      { nome: 'status_situacao', valor: updatedStatuses.join(',') }
    ];
    await saveSettings(settingsToSave);
    setSituationStatuses(updatedStatuses);
  };

  // Check if any general values have changed
  const hasGeneralChanges = () => {
    return (
      initialGeneralValues.name !== condominioName ||
      initialGeneralValues.address !== condominioAddress ||
      initialGeneralValues.quota !== quotaValue ||
      initialGeneralValues.email !== condominioEmail ||
      initialGeneralValues.phone !== condominioPhone ||
      initialGeneralValues.dataLimiteDia !== dataLimiteDia ||
      initialGeneralValues.valorMultaAtraso !== valorMultaAtraso ||
      initialGeneralValues.diasAtrasoMulta !== diasAtrasoMulta
    );
  };

  if (isLoadingConfigs) {
    return (
      <div className="flex h-screen items-center justify-center">
        <LoaderCircle className="animate-spin" size={48} />
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar />

      <div className={cn(
        "flex-1 flex flex-col transition-all duration-300 overflow-hidden",
        isMobile ? "ml-0" : (collapsed ? "ml-12" : "ml-64")
      )}>
        <div className="flex-1 overflow-y-auto pb-10">
          <div className="page-container">
            <Header
              title="Configurações"
              subtitle="Gerencie as configurações do sistema"
            />

            <div className="mt-6 animate-enter">
              {loading && (
                <div className="flex items-center justify-center p-4 mb-4 bg-blue-50 text-blue-700 rounded-lg">
                  <LoaderCircle className="mr-2 animate-spin" size={18} />
                  <span>Salvando configurações...</span>
                </div>
              )}

              <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
                <Tabs defaultValue="general" className="w-full">
                  <div className="border-b border-gray-200">
                    <TabsList className="h-auto bg-transparent border-b rounded-none p-0">
                      <TabsTrigger
                        value="general"
                        className="data-[state=active]:border-primary data-[state=active]:text-primary border-b-2 border-transparent px-4 py-3 rounded-none"
                      >
                        Geral
                      </TabsTrigger>
                      <TabsTrigger
                        value="categories"
                        className="data-[state=active]:border-primary data-[state=active]:text-primary border-b-2 border-transparent px-4 py-3 rounded-none"
                      >
                        Categorias
                      </TabsTrigger>
                      <TabsTrigger
                        value="transactionTypes"
                        className="data-[state=active]:border-primary data-[state=active]:text-primary border-b-2 border-transparent px-4 py-3 rounded-none"
                      >
                        Tipos de Transação
                      </TabsTrigger>
                      <TabsTrigger
                        value="situationStatus"
                        className="data-[state=active]:border-primary data-[state=active]:text-primary border-b-2 border-transparent px-4 py-3 rounded-none"
                      >
                        Status de Situação
                      </TabsTrigger>
                      <TabsTrigger
                        value="committeeRoles"
                        className="data-[state=active]:border-primary data-[state=active]:text-primary border-b-2 border-transparent px-4 py-3 rounded-none"
                      >
                        Funções da Comissão
                      </TabsTrigger>
                      <TabsTrigger
                        value="security"
                        className="data-[state=active]:border-primary data-[state=active]:text-primary border-b-2 border-transparent px-4 py-3 rounded-none"
                      >
                        Segurança
                      </TabsTrigger>
                    </TabsList>
                  </div>

                  <div className="p-6">
                    <TabsContent value="general" className="mt-0 pt-2">
                      <div className="animate-fade-in">
                        <h3 className="text-lg font-semibold text-gray-800 mb-6">Configurações Gerais</h3>

                        <div className="space-y-6">
                          <div>
                            <Label htmlFor="condominio-name">Nome do Condomínio</Label>
                            <Input
                              id="condominio-name"
                              value={condominioName}
                              onChange={(e) => setCondominioName(e.target.value)}
                              className="mt-1"
                            />
                          </div>

                          <div>
                            <Label htmlFor="condominio-address">Endereço</Label>
                            <Input
                              id="condominio-address"
                              value={condominioAddress}
                              onChange={(e) => setCondominioAddress(e.target.value)}
                              className="mt-1"
                            />
                          </div>

                          <div>
                            <Label htmlFor="quota-value">Valor da Quota</Label>
                            <div className="relative mt-1">
                              <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">Kz</span>
                              <Input
                                id="quota-value"
                                value={quotaValue}
                                onChange={(e) => setQuotaValue(e.target.value)}
                                className="pl-10"
                              />
                            </div>
                          </div>

                          <div>
                            <Label htmlFor="multa-value" className="flex items-center">
                              <AlertCircle size={16} className="mr-2" />
                              Valor da Multa por Atraso
                            </Label>
                            <div className="relative mt-1">
                              <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">Kz</span>
                              <Input
                                id="multa-value"
                                value={valorMultaAtraso}
                                onChange={(e) => setValorMultaAtraso(e.target.value)}
                                className="pl-10"
                              />
                            </div>
                            <p className="text-xs text-gray-500 mt-1">
                              Este valor será aplicado automaticamente às quotas registradas após o dia limite.
                            </p>
                          </div>

                          <div>
                            <Label htmlFor="dias-atraso-multa" className="flex items-center">
                              <Calendar size={16} className="mr-2" />
                              Dias de Tolerância para Multa
                            </Label>
                            <Input
                              id="dias-atraso-multa"
                              type="number"
                              min="0"
                              value={diasAtrasoMulta}
                              onChange={(e) => setDiasAtrasoMulta(e.target.value)}
                              className="mt-1"
                            />
                            <p className="text-xs text-gray-500 mt-1">
                              Número de dias após o vencimento que não aplicará multa (0 = multa aplicada imediatamente).
                            </p>
                          </div>

                          <div>
                            <Label htmlFor="data-limite-pagamento" className="flex items-center">
                              <Calendar size={16} className="mr-2" />
                              Dia Limite de Pagamento da Quota
                            </Label>
                            <Select
                              value={dataLimiteDia}
                              onValueChange={setDataLimiteDia}
                            >
                              <SelectTrigger className="w-full mt-1">
                                <SelectValue placeholder="Selecione o dia" />
                              </SelectTrigger>
                              <SelectContent>
                                {daysInMonth.map((day) => (
                                  <SelectItem key={day} value={day}>
                                    Dia {day}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <p className="text-xs text-gray-500 mt-1">
                              Este dia será usado como padrão para a data limite de todas as quotas.
                            </p>
                          </div>

                          <div>
                            <Label htmlFor="condominio-email">E-mail de Contato</Label>
                            <Input
                              id="condominio-email"
                              type="email"
                              value={condominioEmail}
                              onChange={(e) => setCondominioEmail(e.target.value)}
                              className="mt-1"
                            />
                          </div>

                          <div>
                            <Label htmlFor="condominio-phone">Telefone de Contato</Label>
                            <Input
                              id="condominio-phone"
                              type="tel"
                              value={condominioPhone}
                              onChange={(e) => setCondominioPhone(e.target.value)}
                              className="mt-1"
                            />
                          </div>
                        </div>

                        <div className="mt-6">
                          <Button
                            onClick={handleSaveGeneralSettings}
                            disabled={loading || !hasGeneralChanges()}
                            className="flex items-center gap-2"
                          >
                            {loading ? <LoaderCircle className="animate-spin" size={18} /> : <Save size={18} />}
                            <span>{loading ? 'Salvando...' : 'Salvar Configurações'}</span>
                          </Button>
                        </div>
                      </div>
                    </TabsContent>

                    <TabsContent value="categories" className="mt-0 pt-2">
                      <ListManager
                        title="Categorias"
                        items={categories}
                        icon={<Tag size={16} className="text-gray-500" />}
                        onSave={handleSaveCategories}
                        isLoading={loading}
                      />
                    </TabsContent>

                    <TabsContent value="transactionTypes" className="mt-0 pt-2">
                      <ListManager
                        title="Tipos de Transação"
                        items={transactionTypes}
                        icon={<ArrowUpDown size={16} className="text-gray-500" />}
                        onSave={handleSaveTransactionTypes}
                        isLoading={loading}
                      />
                    </TabsContent>

                    <TabsContent value="situationStatus" className="mt-0 pt-2">
                      <ListManager
                        title="Status de Situação"
                        items={situationStatuses}
                        icon={<CheckCircle size={16} className="text-gray-500" />}
                        onSave={handleSaveSituationStatuses}
                        isLoading={loading}
                      />
                    </TabsContent>

                    <TabsContent value="committeeRoles" className="mt-0 pt-2">
                      <CommitteeRolesManager />
                    </TabsContent>

                    <TabsContent value="security" className="mt-0 pt-2">
                      <div className="animate-fade-in">
                        <h3 className="text-lg font-semibold text-gray-800 mb-6">Configurações de Segurança</h3>

                        <div className="space-y-6">
                          <div>
                            <Label className="flex items-center space-x-2">
                              <input type="checkbox" className="rounded border-gray-300 text-primary focus:ring-primary" defaultChecked />
                              <span>Exigir Senha Forte</span>
                            </Label>
                            <p className="text-xs text-gray-500 mt-1 ml-6">As senhas devem ter pelo menos 8 caracteres, incluindo letras maiúsculas, minúsculas, números e símbolos.</p>
                          </div>

                          <div>
                            <Label htmlFor="inactivity-period">Período de Inatividade</Label>
                            <select
                              id="inactivity-period"
                              className="w-full mt-1 px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-200 focus:border-primary-300"
                              defaultValue="30"
                            >
                              <option value="15">15 minutos</option>
                              <option value="30">30 minutos</option>
                              <option value="60">1 hora</option>
                              <option value="120">2 horas</option>
                            </select>
                            <p className="text-xs text-gray-500 mt-1">Após esse período de inatividade, o usuário será automaticamente desconectado.</p>
                          </div>

                          <div>
                            <Label htmlFor="login-attempts">Tentativas de Login</Label>
                            <select
                              id="login-attempts"
                              className="w-full mt-1 px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-200 focus:border-primary-300"
                              defaultValue="5"
                            >
                              <option value="3">3 tentativas</option>
                              <option value="5">5 tentativas</option>
                              <option value="10">10 tentativas</option>
                            </select>
                            <p className="text-xs text-gray-500 mt-1">Após exceder esse número de tentativas de login malsucedidas, a conta será temporariamente bloqueada.</p>
                          </div>

                          <div>
                            <Label>Permissões de Acesso</Label>
                            <div className="bg-gray-50 rounded-lg p-4 mt-1">
                              <div className="space-y-3">
                                <div className="flex items-center justify-between">
                                  <div className="flex items-center">
                                    <Shield size={16} className="text-primary-600 mr-2" />
                                    <span className="text-gray-800">Administradores</span>
                                  </div>
                                  <span className="text-green-600 text-sm">Acesso total</span>
                                </div>

                                <div className="flex items-center justify-between">
                                  <div className="flex items-center">
                                    <User size={16} className="text-gray-600 mr-2" />
                                    <span className="text-gray-800">Convidados</span>
                                  </div>
                                  <span className="text-orange-600 text-sm">Acesso limitado</span>
                                </div>

                                <div className="mt-3">
                                  <p className="text-xs text-gray-500">Os convidados podem visualizar dados, mas não podem criar, editar ou excluir registros.</p>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>

                        <div className="pt-6">
                          <Button
                            onClick={handleSaveGeneralSettings}
                            disabled={loading}
                            className="flex items-center gap-2"
                          >
                            {loading ? <LoaderCircle className="animate-spin" size={18} /> : <Save size={18} />}
                            <span>{loading ? 'Salvando...' : 'Salvar Configurações'}</span>
                          </Button>
                        </div>
                      </div>
                    </TabsContent>
                  </div>
                </Tabs>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Settings;
