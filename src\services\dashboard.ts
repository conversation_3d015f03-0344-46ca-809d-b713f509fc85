import { supabase } from '@/integrations/supabase/client';
import { Morador, Quota, FluxoCaixa, Notificacao } from '@/types';

// Interface para estatísticas do dashboard
export interface DashboardStats {
  totalMoradores: number;
  moradoresAtivos: number;
  quotasEmAtraso: number;
  percentualAtraso: number;
  arrecadacaoMes: number;
  tendenciaArrecadacao: number;
  moradoresComMultas: number;
  tendenciaMultas: number;
  taxaPagamento: number;
  tendenciaPagamento: number;
}

// Interface para resumo de quotas
export interface QuotasSummary {
  totalQuotas: number;
  quotasPagas: number;
  quotasPendentes: number;
  valorTotal: number;
  valorPago: number;
  valorPendente: number;
  percentualPago: number;
}

// Interface para resumo financeiro
export interface FinancialSummary {
  totalEntradas: number;
  totalSaidas: number;
  saldoAtual: number;
  entradaMes: number;
  saidaMes: number;
  saldoMes: number;
  tendenciaEntrada: number;
  tendenciaSaida: number;
}

// Interface para dados de gráficos
export interface ChartData {
  monthlyQuotas: Array<{
    name: string;
    pagas: number;
    pendentes: number;
  }>;
  cashFlow: Array<{
    name: string;
    entrada: number;
    saida: number;
  }>;
}

// Interface para atividades recentes do dashboard
export interface RecentActivity {
  id: string;
  type: 'quota_paid' | 'fine_applied' | 'new_resident' | 'expense_added' | 'income_added';
  title: string;
  description: string;
  date: string;
  amount?: number;
  resident?: string;
}

/**
 * Busca estatísticas gerais do dashboard
 */
export const getDashboardStats = async (): Promise<DashboardStats> => {
  try {
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth() + 1;
    const currentYear = currentDate.getFullYear();
    const previousMonth = currentMonth === 1 ? 12 : currentMonth - 1;
    const previousYear = currentMonth === 1 ? currentYear - 1 : currentYear;

    // Buscar total de moradores
    const { data: moradores, error: moradoresError } = await supabase
      .from('moradores')
      .select('id, status, isento_quotas');
    
    if (moradoresError) throw moradoresError;

    const totalMoradores = moradores?.length || 0;
    const moradoresAtivos = moradores?.filter(m => m.status !== 'Inativo').length || 0;

    // Buscar quotas do mês atual
    const { data: quotasAtual, error: quotasError } = await supabase
      .from('quotas')
      .select('*, moradores!inner(*)')
      .eq('mes', currentMonth)
      .eq('ano', currentYear);
    
    if (quotasError) throw quotasError;

    // Buscar quotas do mês anterior para comparação
    const { data: quotasAnterior } = await supabase
      .from('quotas')
      .select('*')
      .eq('mes', previousMonth)
      .eq('ano', previousYear);

    // Calcular estatísticas de quotas
    const quotasEmAtraso = quotasAtual?.filter(q => 
      q.status === 'Não Pago' && new Date(q.data_vencimento) < currentDate
    ).length || 0;

    const percentualAtraso = totalMoradores > 0 ? (quotasEmAtraso / totalMoradores) * 100 : 0;

    // Calcular arrecadação
    const arrecadacaoMes = quotasAtual?.filter(q => q.status === 'Pago')
      .reduce((sum, q) => sum + (q.valor || 0), 0) || 0;
    
    const arrecadacaoAnterior = quotasAnterior?.filter(q => q.status === 'Pago')
      .reduce((sum, q) => sum + (q.valor || 0), 0) || 0;

    const tendenciaArrecadacao = arrecadacaoAnterior > 0 
      ? ((arrecadacaoMes - arrecadacaoAnterior) / arrecadacaoAnterior) * 100 
      : 0;

    // Calcular moradores com multas
    const moradoresComMultas = quotasAtual?.filter(q => (q.multa || 0) > 0).length || 0;
    const moradoresComMultasAnterior = quotasAnterior?.filter(q => (q.multa || 0) > 0).length || 0;
    
    const tendenciaMultas = moradoresComMultasAnterior > 0 
      ? ((moradoresComMultas - moradoresComMultasAnterior) / moradoresComMultasAnterior) * 100 
      : 0;

    // Calcular taxa de pagamento
    const quotasPagas = quotasAtual?.filter(q => q.status === 'Pago').length || 0;
    const taxaPagamento = totalMoradores > 0 ? (quotasPagas / totalMoradores) * 100 : 0;
    
    const quotasPagasAnterior = quotasAnterior?.filter(q => q.status === 'Pago').length || 0;
    const taxaPagamentoAnterior = totalMoradores > 0 ? (quotasPagasAnterior / totalMoradores) * 100 : 0;
    
    const tendenciaPagamento = taxaPagamentoAnterior > 0 
      ? ((taxaPagamento - taxaPagamentoAnterior) / taxaPagamentoAnterior) * 100 
      : 0;

    return {
      totalMoradores,
      moradoresAtivos,
      quotasEmAtraso,
      percentualAtraso,
      arrecadacaoMes,
      tendenciaArrecadacao,
      moradoresComMultas,
      tendenciaMultas,
      taxaPagamento,
      tendenciaPagamento
    };

  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    throw error;
  }
};

/**
 * Busca resumo de quotas do mês atual
 */
export const getCurrentMonthQuotasSummary = async (): Promise<QuotasSummary> => {
  try {
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth() + 1;
    const currentYear = currentDate.getFullYear();

    const { data: quotas, error } = await supabase
      .from('quotas')
      .select('*')
      .eq('mes', currentMonth)
      .eq('ano', currentYear);
    
    if (error) throw error;

    const totalQuotas = quotas?.length || 0;
    const quotasPagas = quotas?.filter(q => q.status === 'Pago').length || 0;
    const quotasPendentes = totalQuotas - quotasPagas;

    const valorTotal = quotas?.reduce((sum, q) => sum + (q.valor || 0), 0) || 0;
    const valorPago = quotas?.filter(q => q.status === 'Pago')
      .reduce((sum, q) => sum + (q.valor || 0), 0) || 0;
    const valorPendente = valorTotal - valorPago;

    const percentualPago = valorTotal > 0 ? (valorPago / valorTotal) * 100 : 0;

    return {
      totalQuotas,
      quotasPagas,
      quotasPendentes,
      valorTotal,
      valorPago,
      valorPendente,
      percentualPago
    };

  } catch (error) {
    console.error('Error fetching quotas summary:', error);
    throw error;
  }
};

/**
 * Busca resumo financeiro do mês atual
 */
export const getCurrentMonthFinancialSummary = async (): Promise<FinancialSummary> => {
  try {
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth() + 1;
    const currentYear = currentDate.getFullYear();
    const previousMonth = currentMonth === 1 ? 12 : currentMonth - 1;
    const previousYear = currentMonth === 1 ? currentYear - 1 : currentYear;

    // Buscar fluxo de caixa do mês atual
    const { data: fluxoAtual, error: fluxoError } = await supabase
      .from('fluxo_caixa')
      .select('*')
      .gte('data', `${currentYear}-${currentMonth.toString().padStart(2, '0')}-01`)
      .lt('data', `${currentYear}-${(currentMonth + 1).toString().padStart(2, '0')}-01`);

    if (fluxoError) throw fluxoError;

    // Buscar fluxo de caixa do mês anterior
    const { data: fluxoAnterior } = await supabase
      .from('fluxo_caixa')
      .select('*')
      .gte('data', `${previousYear}-${previousMonth.toString().padStart(2, '0')}-01`)
      .lt('data', `${previousYear}-${previousMonth === 12 ? currentYear : previousYear}-${previousMonth === 12 ? '01' : (previousMonth + 1).toString().padStart(2, '0')}-01`);

    // Calcular totais do mês atual
    const entradaMes = fluxoAtual?.filter(f => f.tipo === 'entrada')
      .reduce((sum, f) => sum + (f.valor || 0), 0) || 0;

    const saidaMes = fluxoAtual?.filter(f => f.tipo === 'saida')
      .reduce((sum, f) => sum + (f.valor || 0), 0) || 0;

    const saldoMes = entradaMes - saidaMes;

    // Calcular totais do mês anterior para tendências
    const entradaAnterior = fluxoAnterior?.filter(f => f.tipo === 'entrada')
      .reduce((sum, f) => sum + (f.valor || 0), 0) || 0;

    const saidaAnterior = fluxoAnterior?.filter(f => f.tipo === 'saida')
      .reduce((sum, f) => sum + (f.valor || 0), 0) || 0;

    // Calcular tendências
    const tendenciaEntrada = entradaAnterior > 0
      ? ((entradaMes - entradaAnterior) / entradaAnterior) * 100
      : 0;

    const tendenciaSaida = saidaAnterior > 0
      ? ((saidaMes - saidaAnterior) / saidaAnterior) * 100
      : 0;

    // Buscar totais gerais (usar função RPC se disponível)
    const { data: saldoTotal } = await supabase.rpc('calcular_saldo');

    // Buscar totais de entradas e saídas gerais
    const { data: fluxoGeral } = await supabase
      .from('fluxo_caixa')
      .select('tipo, valor');

    const totalEntradas = fluxoGeral?.filter(f => f.tipo === 'entrada')
      .reduce((sum, f) => sum + (f.valor || 0), 0) || 0;

    const totalSaidas = fluxoGeral?.filter(f => f.tipo === 'saida')
      .reduce((sum, f) => sum + (f.valor || 0), 0) || 0;

    return {
      totalEntradas,
      totalSaidas,
      saldoAtual: saldoTotal || 0,
      entradaMes,
      saidaMes,
      saldoMes,
      tendenciaEntrada,
      tendenciaSaida
    };

  } catch (error) {
    console.error('Error fetching financial summary:', error);
    throw error;
  }
};

/**
 * Busca dados para gráficos (últimos 6 meses)
 */
export const getChartData = async (): Promise<ChartData> => {
  try {
    const currentDate = new Date();
    const months = [];

    // Gerar últimos 6 meses
    for (let i = 5; i >= 0; i--) {
      const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
      months.push({
        month: date.getMonth() + 1,
        year: date.getFullYear(),
        name: date.toLocaleDateString('pt-BR', { month: 'short' }).replace('.', '')
      });
    }

    // Buscar dados de quotas para os últimos 6 meses
    const quotaPromises = months.map(async ({ month, year, name }) => {
      const { data: quotas } = await supabase
        .from('quotas')
        .select('status, valor')
        .eq('mes', month)
        .eq('ano', year);

      const pagas = quotas?.filter(q => q.status === 'Pago').length || 0;
      const pendentes = (quotas?.length || 0) - pagas;

      return { name, pagas, pendentes };
    });

    // Buscar dados de fluxo de caixa para os últimos 6 meses
    const cashFlowPromises = months.map(async ({ month, year, name }) => {
      const startDate = `${year}-${month.toString().padStart(2, '0')}-01`;
      const endDate = `${year}-${(month + 1).toString().padStart(2, '0')}-01`;

      const { data: fluxo } = await supabase
        .from('fluxo_caixa')
        .select('tipo, valor')
        .gte('data', startDate)
        .lt('data', endDate);

      const entrada = fluxo?.filter(f => f.tipo === 'entrada')
        .reduce((sum, f) => sum + (f.valor || 0), 0) || 0;

      const saida = fluxo?.filter(f => f.tipo === 'saida')
        .reduce((sum, f) => sum + (f.valor || 0), 0) || 0;

      return { name, entrada, saida };
    });

    const [monthlyQuotas, cashFlow] = await Promise.all([
      Promise.all(quotaPromises),
      Promise.all(cashFlowPromises)
    ]);

    return {
      monthlyQuotas,
      cashFlow
    };

  } catch (error) {
    console.error('Error fetching chart data:', error);
    throw error;
  }
};

/**
 * Busca atividades recentes para o dashboard
 */
export const getRecentActivities = async (limit: number = 10): Promise<RecentActivity[]> => {
  try {
    const activities: RecentActivity[] = [];

    // Buscar pagamentos recentes de quotas (últimos 7 dias)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    const { data: recentPayments } = await supabase
      .from('quotas')
      .select(`
        id,
        valor,
        data_pagamento,
        mes,
        ano,
        moradores!inner(nome, apartamento)
      `)
      .eq('status', 'Pago')
      .gte('data_pagamento', sevenDaysAgo.toISOString().split('T')[0])
      .order('data_pagamento', { ascending: false })
      .limit(5);

    // Adicionar pagamentos às atividades
    recentPayments?.forEach(payment => {
      activities.push({
        id: `payment-${payment.id}`,
        type: 'quota_paid',
        title: 'Quota Paga',
        description: `${payment.moradores?.nome} - Apt ${payment.moradores?.apartamento} - ${payment.mes}/${payment.ano}`,
        date: payment.data_pagamento || '',
        amount: payment.valor,
        resident: payment.moradores?.nome
      });
    });

    // Buscar multas aplicadas recentemente
    const { data: recentFines } = await supabase
      .from('quotas')
      .select(`
        id,
        multa,
        updated_at,
        mes,
        ano,
        moradores!inner(nome, apartamento)
      `)
      .gt('multa', 0)
      .gte('updated_at', sevenDaysAgo.toISOString())
      .order('updated_at', { ascending: false })
      .limit(3);

    // Adicionar multas às atividades
    recentFines?.forEach(fine => {
      activities.push({
        id: `fine-${fine.id}`,
        type: 'fine_applied',
        title: 'Multa Aplicada',
        description: `${fine.moradores?.nome} - Apt ${fine.moradores?.apartamento} - ${fine.mes}/${fine.ano}`,
        date: fine.updated_at,
        amount: fine.multa,
        resident: fine.moradores?.nome
      });
    });

    // Buscar novos moradores (últimos 30 dias)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const { data: newResidents } = await supabase
      .from('moradores')
      .select('id, nome, apartamento, created_at')
      .gte('created_at', thirtyDaysAgo.toISOString())
      .order('created_at', { ascending: false })
      .limit(3);

    // Adicionar novos moradores às atividades
    newResidents?.forEach(resident => {
      activities.push({
        id: `resident-${resident.id}`,
        type: 'new_resident',
        title: 'Novo Morador',
        description: `${resident.nome} - Apt ${resident.apartamento}`,
        date: resident.created_at || '',
        resident: resident.nome
      });
    });

    // Buscar despesas recentes (últimos 7 dias)
    const { data: recentExpenses } = await supabase
      .from('fluxo_caixa')
      .select('id, descricao, valor, data, categoria')
      .eq('tipo', 'saida')
      .gte('data', sevenDaysAgo.toISOString().split('T')[0])
      .order('data', { ascending: false })
      .limit(3);

    // Adicionar despesas às atividades
    recentExpenses?.forEach(expense => {
      activities.push({
        id: `expense-${expense.id}`,
        type: 'expense_added',
        title: 'Nova Despesa',
        description: `${expense.categoria}: ${expense.descricao}`,
        date: expense.data,
        amount: expense.valor
      });
    });

    // Buscar entradas recentes (últimos 7 dias)
    const { data: recentIncomes } = await supabase
      .from('fluxo_caixa')
      .select('id, descricao, valor, data, categoria')
      .eq('tipo', 'entrada')
      .gte('data', sevenDaysAgo.toISOString().split('T')[0])
      .order('data', { ascending: false })
      .limit(3);

    // Adicionar entradas às atividades
    recentIncomes?.forEach(income => {
      activities.push({
        id: `income-${income.id}`,
        type: 'income_added',
        title: 'Nova Entrada',
        description: `${income.categoria}: ${income.descricao}`,
        date: income.data,
        amount: income.valor
      });
    });

    // Ordenar por data e limitar resultados
    return activities
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
      .slice(0, limit);

  } catch (error) {
    console.error('Error fetching recent activities:', error);
    throw error;
  }
};

/**
 * Busca contagem de moradores ativos
 */
export const getActiveMoradores = async (): Promise<number> => {
  try {
    const { data, error } = await supabase
      .from('moradores')
      .select('id', { count: 'exact' })
      .neq('status', 'Inativo');

    if (error) throw error;
    return data?.length || 0;

  } catch (error) {
    console.error('Error fetching active moradores count:', error);
    return 0;
  }
};
