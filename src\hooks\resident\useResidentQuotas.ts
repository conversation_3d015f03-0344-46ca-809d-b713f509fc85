import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { QuotaHistory, ResidentQuotaView } from '@/types';

export const useResidentQuotas = () => {
  const { profile } = useAuth();

  const { data: quotas = [], isLoading: loading, error, refetch } = useQuery({
    queryKey: ['resident-quotas', profile?.apartment_id, profile?.id],
    queryFn: async (): Promise<QuotaHistory[]> => {
      console.log('🔍 Buscando quotas para apartamento:', profile?.apartment_id);
      console.log('🔍 User ID do perfil:', profile?.id);
      
      if (!profile?.apartment_id) {
        console.error('❌ Apartamento não encontrado no perfil');
        throw new Error('Apartamento não encontrado no perfil');
      }

      // Estratégia dupla: buscar morador por apartamento E por user_id
      console.log('🔍 Buscando morador para apartamento:', profile.apartment_id);
      
      // Primeira tentativa: buscar pelo apartamento e user_id
      let { data: morador, error: moradorError } = await supabase
        .from('moradores')
        .select('id')
        .eq('apartamento', profile.apartment_id)
        .eq('user_id', profile.id)
        .maybeSingle();

      // Se não encontrou com ambos os critérios, tentar apenas pelo apartamento
      if (!morador) {
        console.log('🔍 Tentando buscar morador apenas pelo apartamento:', profile.apartment_id);
        const { data: moradorByApartment, error: moradorByApartmentError } = await supabase
          .from('moradores')
          .select('id')
          .eq('apartamento', profile.apartment_id)
          .maybeSingle();

        if (moradorByApartmentError) {
          console.error('❌ Erro ao buscar morador por apartamento:', moradorByApartmentError);
        } else if (moradorByApartment) {
          console.log('✅ Morador encontrado apenas pelo apartamento:', moradorByApartment.id);
          morador = moradorByApartment;
        }
      }

      // Se ainda não encontrou, tentar apenas pelo user_id
      if (!morador && profile.id) {
        console.log('🔍 Tentando buscar morador apenas pelo user_id:', profile.id);
        const { data: moradorByUserId, error: moradorByUserIdError } = await supabase
          .from('moradores')
          .select('id, apartamento')
          .eq('user_id', profile.id)
          .maybeSingle();

        if (moradorByUserIdError) {
          console.error('❌ Erro ao buscar morador por user_id:', moradorByUserIdError);
        } else if (moradorByUserId) {
          console.log('✅ Morador encontrado pelo user_id:', moradorByUserId);
          morador = moradorByUserId;
        }
      }

      if (moradorError && !morador) {
        console.error('❌ Erro ao buscar morador:', moradorError);
        throw new Error(`Erro ao buscar morador: ${moradorError.message}`);
      }

      if (!morador) {
        console.warn('⚠️ Morador não encontrado para apartamento:', profile.apartment_id, 'e user_id:', profile.id);
        return [];
      }

      console.log('✅ Morador encontrado:', morador.id);

      // Buscar quotas do morador
      const { data: quotasData, error: quotasError } = await supabase
        .from('quotas')
        .select('*')
        .eq('morador_id', morador.id)
        .order('ano', { ascending: false })
        .order('mes', { ascending: false });

      if (quotasError) {
        console.error('❌ Erro ao buscar quotas:', quotasError);
        throw new Error(`Erro ao buscar quotas: ${quotasError.message}`);
      }

      console.log('✅ Quotas encontradas:', quotasData?.length || 0);

      // Converter dados do Supabase para o formato QuotaHistory
      return (quotasData || []).map(quota => ({
        id: quota.id,
        month: quota.mes,
        year: quota.ano,
        amount: Number(quota.valor),
        status: quota.status === 'Pago' ? 'paid' :
               (new Date(quota.data_vencimento) < new Date() ? 'overdue' : 'pending'),
        dueDate: new Date(quota.data_vencimento),
        paidDate: quota.data_pagamento ? new Date(quota.data_pagamento) : undefined,
        fine: Number(quota.multa) || 0,
        fineStatus: quota.situacao, // Adicionar status da multa
        receipt: quota.comprovativo || undefined
      }));
    },
    enabled: !!profile?.apartment_id && !!profile?.id,
    retry: 1,
    refetchOnWindowFocus: false,
  });

  // Calcular resumo das quotas
  const getQuotaSummary = (): ResidentQuotaView => {
    const currentMonth = new Date().getMonth() + 1;
    const currentYear = new Date().getFullYear();
    
    const currentQuota = quotas.find(q => q.month === currentMonth && q.year === currentYear);
    
    const pendingQuotas = quotas.filter(q => q.status === 'pending' || q.status === 'overdue');
    const totalPending = pendingQuotas.reduce((sum, q) => sum + q.amount, 0);
    // Contar apenas multas não regularizadas
    const totalFines = quotas
      .filter(q => q.fineStatus !== 'Regularizada') // Excluir multas regularizadas
      .reduce((sum, q) => sum + (q.fine || 0), 0);
    const monthsOverdue = quotas.filter(q => q.status === 'overdue').length;

    return {
      currentMonth: currentQuota ? {
        status: currentQuota.status,
        amount: currentQuota.amount,
        dueDate: currentQuota.dueDate,
        paidDate: currentQuota.paidDate
      } : {
        status: 'pending',
        amount: 30000, // Valor padrão
        dueDate: new Date(currentYear, currentMonth - 1, 15) // 15 do mês atual
      },
      history: quotas,
      summary: {
        totalPending,
        totalFines,
        monthsOverdue
      }
    };
  };

  // Filtrar quotas por ano
  const getQuotasByYear = (year: number): QuotaHistory[] => {
    return quotas.filter(quota => quota.year === year);
  };

  // Filtrar quotas por status
  const getQuotasByStatus = (status: 'paid' | 'pending' | 'overdue'): QuotaHistory[] => {
    return quotas.filter(quota => quota.status === status);
  };

  // Obter quota específica
  const getQuotaByMonthYear = (month: number, year: number): QuotaHistory | undefined => {
    return quotas.find(quota => quota.month === month && quota.year === year);
  };

  // Calcular estatísticas
  const getPaymentStats = () => {
    const totalQuotas = quotas.length;
    const paidQuotas = quotas.filter(q => q.status === 'paid').length;
    const paymentRate = totalQuotas > 0 ? Math.round((paidQuotas / totalQuotas) * 100) : 0;
    
    const onTimePayments = quotas.filter(q => 
      q.status === 'paid' && 
      q.paidDate && 
      q.paidDate <= q.dueDate
    ).length;
    
    const onTimeRate = paidQuotas > 0 ? Math.round((onTimePayments / paidQuotas) * 100) : 0;

    return {
      totalQuotas,
      paidQuotas,
      pendingQuotas: quotas.filter(q => q.status === 'pending').length,
      overdueQuotas: quotas.filter(q => q.status === 'overdue').length,
      paymentRate,
      onTimeRate,
      totalPaid: quotas.filter(q => q.status === 'paid').reduce((sum, q) => sum + q.amount, 0),
      // Contar apenas multas não regularizadas
      totalFines: quotas
        .filter(q => q.fineStatus !== 'Regularizada')
        .reduce((sum, q) => sum + (q.fine || 0), 0)
    };
  };

  // Função para baixar comprovante
  const downloadReceipt = async (receipt: string) => {
    try {
      console.log('Downloading receipt:', receipt);
      
      // Em produção, isso faria o download do arquivo real do Supabase Storage
      const link = document.createElement('a');
      link.href = '#'; // URL do arquivo do Supabase Storage
      link.download = receipt;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      return true;
    } catch (error) {
      console.error('Error downloading receipt:', error);
      return false;
    }
  };

  // Função para atualizar quotas
  const refreshQuotas = async () => {
    await refetch();
  };

  return {
    quotas,
    loading,
    error: error ? String(error) : null,
    getQuotaSummary,
    getQuotasByYear,
    getQuotasByStatus,
    getQuotaByMonthYear,
    getPaymentStats,
    downloadReceipt,
    refreshQuotas
  };
};
