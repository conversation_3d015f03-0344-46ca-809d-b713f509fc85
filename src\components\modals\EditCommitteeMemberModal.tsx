
import React, { useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ScrollArea } from "@/components/ui/scroll-area";
import { MembroComissao } from '@/types';
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from 'sonner';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useCommitteeRoles } from '@/hooks/useCommitteeRoles';

const formSchema = z.object({
  nome: z.string().min(1, "Nome é obrigatório"),
  cargo: z.string().min(1, "Função é obrigatória"),
  email: z.string().email("Email inválido").optional().or(z.literal('')),
  telefone: z.string().optional().or(z.literal('')),
  data_inicio: z.string().min(1, "Data de início é obrigatória"),
  data_fim: z.string().optional().or(z.literal('')),
});

type FormValues = z.infer<typeof formSchema>;

interface EditCommitteeMemberModalProps {
  isOpen: boolean;
  onClose: () => void;
  member: MembroComissao | null;
  onSave: (memberData: Partial<MembroComissao>) => void;
}

const EditCommitteeMemberModal: React.FC<EditCommitteeMemberModalProps> = ({
  isOpen,
  onClose,
  member,
  onSave
}) => {
  const queryClient = useQueryClient();
  const { activeRoles, isLoading: isLoadingRoles } = useCommitteeRoles();

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      nome: '',
      cargo: '',
      email: '',
      telefone: '',
      data_inicio: '',
      data_fim: '',
    }
  });

  // Reset form when member changes
  useEffect(() => {
    if (member) {
      form.reset({
        nome: member.nome || '',
        cargo: member.cargo || '',
        email: member.email || '',
        telefone: member.telefone || '',
        data_inicio: member.data_inicio ? new Date(member.data_inicio).toISOString().split('T')[0] : '',
        data_fim: member.data_fim ? new Date(member.data_fim).toISOString().split('T')[0] : '',
      });
    }
  }, [member, form]);

  const updateMemberMutation = useMutation({
    mutationFn: async (data: Partial<MembroComissao>) => {
      if (!data.id) throw new Error('Member ID is missing');

      // Process dates properly for database
      const payload = {
        nome: data.nome,
        cargo: data.cargo,
        email: data.email,
        telefone: data.telefone,
        data_inicio: data.data_inicio,
        data_fim: data.data_fim ? data.data_fim : null, // Explicitly set to null if empty
        updated_at: new Date().toISOString()
      };

      const { error } = await supabase
        .from('membros_comissao')
        .update(payload)
        .eq('id', data.id);

      if (error) throw error;
      return true;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['membros_comissao'] });
      toast.success('Membro da comissão atualizado com sucesso!');
      onClose();
    },
    onError: (error) => {
      console.error('Error updating committee member:', error);
      toast.error('Erro ao atualizar membro da comissão.');
    }
  });

  const handleSubmit = (data: FormValues) => {
    if (!member?.id) {
      toast.error('ID do membro está faltando!');
      return;
    }

    const memberData = {
      ...data,
      id: member.id
    };

    // Call the mutation directly
    updateMemberMutation.mutate(memberData);

    // Also call the parent component's onSave for compatibility
    onSave(memberData);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md max-h-[90vh] flex flex-col p-0">
        <DialogHeader className="px-6 pt-6 flex-shrink-0">
          <DialogTitle>Editar Membro da Comissão</DialogTitle>
        </DialogHeader>

        <ScrollArea className="flex-1 px-6 overflow-y-auto">
          <div className="pb-4">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="nome"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Nome <span className="text-red-500">*</span></FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Nome completo" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="cargo"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Função <span className="text-red-500">*</span></FormLabel>
                      <FormControl>
                        <Select value={field.value} onValueChange={field.onChange} disabled={isLoadingRoles}>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione uma função" />
                          </SelectTrigger>
                          <SelectContent>
                            {activeRoles.map((role) => (
                              <SelectItem key={role.id} value={role.name}>
                                {role.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>E-mail</FormLabel>
                      <FormControl>
                        <Input {...field} type="email" placeholder="<EMAIL>" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="telefone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Telefone</FormLabel>
                      <FormControl>
                        <Input {...field} type="tel" placeholder="+244 123 456 789" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="data_inicio"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Data de Início <span className="text-red-500">*</span></FormLabel>
                        <FormControl>
                          <Input {...field} type="date" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="data_fim"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Data de Término</FormLabel>
                        <FormControl>
                          <Input {...field} type="date" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </form>
            </Form>
          </div>
        </ScrollArea>

        <DialogFooter className="px-6 py-4 border-t flex-shrink-0">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={updateMemberMutation.isPending}
          >
            Cancelar
          </Button>
          <Button
            onClick={form.handleSubmit(handleSubmit)}
            disabled={updateMemberMutation.isPending}
          >
            {updateMemberMutation.isPending ? 'Salvando...' : 'Salvar Alterações'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default EditCommitteeMemberModal;
