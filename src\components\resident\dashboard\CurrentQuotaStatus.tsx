
import React from 'react';
import { Clock } from 'lucide-react';
import { ResidentDashboardData } from '@/hooks/resident/useResidentDashboard';

interface CurrentQuotaStatusProps {
  currentQuota: ResidentDashboardData['currentQuota'];
  hasNoResidentData: boolean;
}

const CurrentQuotaStatus: React.FC<CurrentQuotaStatusProps> = ({ 
  currentQuota, 
  hasNoResidentData 
}) => {
  const getStatusColor = (status: 'pending' | 'paid' | 'overdue') => {
    switch (status) {
      case 'paid':
        return 'text-green-600';
      case 'pending':
        return 'text-yellow-600';
      case 'overdue':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const getStatusText = (status: 'pending' | 'paid' | 'overdue') => {
    switch (status) {
      case 'paid':
        return 'Pago';
      case 'pending':
        return 'Pendente';
      case 'overdue':
        return 'Em Atraso';
      default:
        return 'Desconhecido';
    }
  };

  return (
    <div className="dashboard-card">
      <h3 className="text-base font-medium text-gray-700 mb-4">Status da Quota Atual</h3>
      <div className="space-y-4">
        {currentQuota ? (
          <>
            <div className="flex justify-between items-center p-4 bg-gray-50 rounded-lg">
              <div>
                <p className="text-sm font-medium text-gray-800">{currentQuota.monthYear}</p>
                <p className="text-xs text-gray-500">
                  Vencimento: {currentQuota.dueDate.toLocaleDateString('pt-BR')}
                </p>
              </div>
              <div className="text-right">
                <p className="text-lg font-semibold">{currentQuota.amount.toLocaleString()} Kz</p>
                <p className={`text-sm font-medium ${getStatusColor(currentQuota.status)}`}>
                  {getStatusText(currentQuota.status)}
                </p>
              </div>
            </div>

            {currentQuota.status === 'pending' && (
              <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-center">
                  <Clock className="h-5 w-5 text-yellow-500 mr-2" />
                  <p className="text-sm text-yellow-800">
                    Quota pendente. Vencimento em {Math.ceil((currentQuota.dueDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))} dias.
                  </p>
                </div>
              </div>
            )}
          </>
        ) : (
          <div className="p-4 bg-gray-50 rounded-lg">
            <p className="text-sm text-gray-600">Nenhuma quota encontrada para o mês atual.</p>
            {hasNoResidentData && (
              <p className="text-xs text-gray-500 mt-1">
                Seus dados de morador podem não estar cadastrados no sistema.
              </p>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default CurrentQuotaStatus;
