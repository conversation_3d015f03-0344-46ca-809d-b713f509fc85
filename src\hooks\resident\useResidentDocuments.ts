
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';

export interface ResidentDocument {
  id: string;
  nome: string;
  tipo: string;
  categoria: string;
  tamanho: string;
  url_arquivo: string;
  data_upload: string;
  uploaded_by: string;
}

export const useResidentDocuments = () => {
  const { profile } = useAuth();

  return useQuery({
    queryKey: ['resident-documents', profile?.apartment_id],
    queryFn: async (): Promise<ResidentDocument[]> => {
      if (!profile?.apartment_id) {
        throw new Error('Apartamento não encontrado no perfil');
      }

      const { data, error } = await supabase
        .from('documentos_moradores')
        .select('*')
        .eq('apartment_id', profile.apartment_id)
        .order('data_upload', { ascending: false });

      if (error) throw error;

      return data || [];
    },
    enabled: !!profile?.apartment_id
  });
};
