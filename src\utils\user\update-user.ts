
import { User } from '@/types';
import { supabase } from '@/integrations/supabase/client';

export const updateUser = async (
  userId: string,
  userData: Partial<User> & { password?: string }
) => {
  try {
    // Update the profile with all available data except email
    const updateData: any = {
      name: userData.name,
      role: userData.role === 'Administrador' ? 'admin' : 'convidado',
      banned: userData.status === 'Inativo',
      updated_at: new Date().toISOString()
    };
    
    const { error: profileError } = await supabase
      .from('profiles')
      .update(updateData)
      .eq('id', userId);

    if (profileError) {
      console.error('Error updating profile:', profileError);
      throw profileError;
    }

    // For password update, we would need admin access
    if (userData.password) {
      try {
        // Use standard updateUser instead of admin API
        const { error: updatePasswordError } = await supabase.auth.updateUser({
          password: userData.password
        });
        
        if (updatePasswordError) {
          console.error('Error updating password:', updatePasswordError);
          // Don't throw here as the profile was already updated
        }
      } catch (e) {
        console.error('Error updating password:', e);
      }
    }

    return true;
  } catch (error) {
    console.error('Error in updateUser:', error);
    throw error;
  }
};
