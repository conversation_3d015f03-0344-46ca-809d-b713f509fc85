
import { supabase } from '@/integrations/supabase/client';

export interface UserPermissions {
  isAdmin: boolean;
  canViewAllUsers: boolean;
  canCreateUsers: boolean;
  canEditUsers: boolean;
  canDeleteUsers: boolean;
}

export const checkUserPermissions = async (): Promise<UserPermissions> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return {
        isAdmin: false,
        canViewAllUsers: false,
        canCreateUsers: false,
        canEditUsers: false,
        canDeleteUsers: false,
      };
    }

    // Get user profile to check role
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    const isAdmin = profile?.role === 'admin';

    return {
      isAdmin,
      canViewAllUsers: isAdmin,
      canCreateUsers: isAdmin,
      canEditUsers: isAdmin,
      canDeleteUsers: isAdmin,
    };
  } catch (error) {
    console.error('Error checking user permissions:', error);
    return {
      isAdmin: false,
      canViewAllUsers: false,
      canCreateUsers: false,
      canEditUsers: false,
      canDeleteUsers: false,
    };
  }
};
