
import React from 'react';
import { Bell } from 'lucide-react';
import { ResidentDashboardData } from '@/hooks/resident/useResidentDashboard';

interface RecentNotificationsProps {
  notifications: ResidentDashboardData['recentNotifications'];
}

const RecentNotifications: React.FC<RecentNotificationsProps> = ({ notifications }) => {
  return (
    <div className="dashboard-card">
      <h3 className="text-base font-medium text-gray-700 mb-4">Notificações Recentes</h3>
      <div className="space-y-3">
        {notifications.length > 0 ? (
          notifications.map((notification) => (
            <div key={notification.id} className="flex items-start p-3 bg-gray-50 rounded-lg">
              <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center mr-3 mt-0.5">
                <Bell size={14} className="text-blue-500" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-800 truncate">
                  {notification.title}
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  {notification.message}
                </p>
                <p className="text-xs text-gray-400 mt-1">
                  {new Date(notification.date).toLocaleDateString('pt-BR')}
                </p>
              </div>
            </div>
          ))
        ) : (
          <div className="p-4 bg-gray-50 rounded-lg">
            <p className="text-sm text-gray-600">Nenhuma notificação recente.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default RecentNotifications;
