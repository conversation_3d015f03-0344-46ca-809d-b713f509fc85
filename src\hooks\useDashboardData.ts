/**
 * Hook personalizado para gerenciar dados do Dashboard Administrativo
 *
 * Este hook centraliza todas as consultas de dados necessárias para o dashboard,
 * incluindo estatísticas, resumos financeiros, gráficos e atividades recentes.
 *
 * <AUTHOR> Prédio Azul
 * @version 1.0
 */

import { useQuery } from '@tanstack/react-query';
import {
  getDashboardStats,
  getCurrentMonthQuotasSummary,
  getCurrentMonthFinancialSummary,
  getChartData,
  getRecentActivities,
  getActiveMoradores,
  type DashboardStats,
  type QuotasSummary,
  type FinancialSummary,
  type ChartData,
  type RecentActivity
} from '@/services/dashboard';

/**
 * Interface para o retorno completo do hook useDashboardData
 * Define todos os dados e estados disponíveis para o componente Dashboard
 */
export interface DashboardData {
  stats: DashboardStats | undefined;                    // Estatísticas gerais do condomínio
  quotasSummary: QuotasSummary | undefined;             // Resumo das quotas do mês atual
  financialSummary: FinancialSummary | undefined;       // Resumo financeiro (entradas/saídas)
  chartData: ChartData | undefined;                     // Dados para gráficos
  recentActivities: RecentActivity[] | undefined;       // Lista de atividades recentes
  activeMoradores: number | undefined;                  // Número de moradores ativos
  isLoading: boolean;                                   // Estado de carregamento geral
  isError: boolean;                                     // Estado de erro geral
  error: Error | null;                                  // Objeto de erro (se houver)
  refetch: () => void;                                  // Função para recarregar todos os dados
}

/**
 * Hook principal para buscar todos os dados do dashboard
 *
 * Utiliza React Query para cache e gerenciamento de estado de múltiplas consultas.
 * Cada query tem configurações específicas de cache baseadas na frequência de mudança dos dados.
 *
 * @returns {DashboardData} Objeto com todos os dados e estados do dashboard
 */
export const useDashboardData = (): DashboardData => {
  // Query 1: Estatísticas Gerais do Condomínio
  // Inclui: quotas em atraso, arrecadação, moradores com multas, taxa de pagamento
  const {
    data: stats,
    isLoading: isLoadingStats,
    isError: isErrorStats,
    error: errorStats,
    refetch: refetchStats
  } = useQuery({
    queryKey: ['dashboard-stats'],
    queryFn: getDashboardStats,
    staleTime: 5 * 60 * 1000,        // Cache válido por 5 minutos
    gcTime: 10 * 60 * 1000,          // Garbage collection após 10 minutos
    refetchOnWindowFocus: false,      // Não recarregar ao focar janela
    retry: 2                          // Tentar novamente 2 vezes em caso de erro
  });

  // Query 2: Resumo das Quotas do Mês Atual
  // Inclui: quotas pagas, pendentes, percentuais
  const {
    data: quotasSummary,
    isLoading: isLoadingQuotas,
    isError: isErrorQuotas,
    error: errorQuotas,
    refetch: refetchQuotas
  } = useQuery({
    queryKey: ['dashboard-quotas-summary'],
    queryFn: getCurrentMonthQuotasSummary,
    staleTime: 5 * 60 * 1000,        // Cache válido por 5 minutos
    gcTime: 10 * 60 * 1000,          // Garbage collection após 10 minutos
    refetchOnWindowFocus: false,      // Não recarregar ao focar janela
    retry: 2                          // Tentar novamente 2 vezes em caso de erro
  });

  // Query 3: Resumo Financeiro do Mês Atual
  // Inclui: total de entradas, saídas, saldo atual, resultado do mês
  const {
    data: financialSummary,
    isLoading: isLoadingFinancial,
    isError: isErrorFinancial,
    error: errorFinancial,
    refetch: refetchFinancial
  } = useQuery({
    queryKey: ['dashboard-financial-summary'],
    queryFn: getCurrentMonthFinancialSummary,
    staleTime: 5 * 60 * 1000,        // Cache válido por 5 minutos
    gcTime: 10 * 60 * 1000,          // Garbage collection após 10 minutos
    refetchOnWindowFocus: false,      // Não recarregar ao focar janela
    retry: 2                          // Tentar novamente 2 vezes em caso de erro
  });

  // Query 4: Dados para Gráficos (Quotas Mensais e Fluxo de Caixa)
  // Inclui: dados históricos para gráficos de barras e área
  const {
    data: chartData,
    isLoading: isLoadingChart,
    isError: isErrorChart,
    error: errorChart,
    refetch: refetchChart
  } = useQuery({
    queryKey: ['dashboard-chart-data'],
    queryFn: getChartData,
    staleTime: 10 * 60 * 1000,       // Cache válido por 10 minutos (dados históricos mudam menos)
    gcTime: 15 * 60 * 1000,          // Garbage collection após 15 minutos
    refetchOnWindowFocus: false,      // Não recarregar ao focar janela
    retry: 2                          // Tentar novamente 2 vezes em caso de erro
  });

  // Query 5: Atividades Recentes do Sistema
  // Inclui: pagamentos, multas, novos moradores, despesas (últimas 10)
  const {
    data: recentActivities,
    isLoading: isLoadingActivities,
    isError: isErrorActivities,
    error: errorActivities,
    refetch: refetchActivities
  } = useQuery({
    queryKey: ['dashboard-recent-activities'],
    queryFn: () => getRecentActivities(10),
    staleTime: 2 * 60 * 1000,        // Cache válido por 2 minutos (atividades recentes devem ser mais atuais)
    gcTime: 5 * 60 * 1000,           // Garbage collection após 5 minutos
    refetchOnWindowFocus: false,      // Não recarregar ao focar janela
    retry: 2                          // Tentar novamente 2 vezes em caso de erro
  });

  // Query 6: Contagem de Moradores Ativos
  // Inclui: número total de moradores ativos no sistema
  const {
    data: activeMoradores,
    isLoading: isLoadingMoradores,
    isError: isErrorMoradores,
    error: errorMoradores,
    refetch: refetchMoradores
  } = useQuery({
    queryKey: ['dashboard-active-moradores'],
    queryFn: getActiveMoradores,
    staleTime: 10 * 60 * 1000,       // Cache válido por 10 minutos (dados estáveis)
    gcTime: 15 * 60 * 1000,          // Garbage collection após 15 minutos
    refetchOnWindowFocus: false,      // Não recarregar ao focar janela
    retry: 2                          // Tentar novamente 2 vezes em caso de erro
  });

  // Combinação de Estados de Carregamento
  // O dashboard está carregando se qualquer uma das queries estiver carregando
  const isLoading =
    isLoadingStats ||
    isLoadingQuotas ||
    isLoadingFinancial ||
    isLoadingChart ||
    isLoadingActivities ||
    isLoadingMoradores;

  // Combinação de Estados de Erro
  // O dashboard tem erro se qualquer uma das queries tiver erro
  const isError =
    isErrorStats ||
    isErrorQuotas ||
    isErrorFinancial ||
    isErrorChart ||
    isErrorActivities ||
    isErrorMoradores;

  // Captura do Primeiro Erro Encontrado
  // Retorna o primeiro erro para exibição ao usuário
  const error =
    errorStats ||
    errorQuotas ||
    errorFinancial ||
    errorChart ||
    errorActivities ||
    errorMoradores;

  /**
   * Função para recarregar todos os dados do dashboard
   * Executa refetch em todas as queries simultaneamente
   */
  const refetch = () => {
    refetchStats();
    refetchQuotas();
    refetchFinancial();
    refetchChart();
    refetchActivities();
    refetchMoradores();
  };

  return {
    stats,
    quotasSummary,
    financialSummary,
    chartData,
    recentActivities,
    activeMoradores,
    isLoading,
    isError,
    error: error as Error | null,
    refetch
  };
};

/**
 * Hook específico para estatísticas do dashboard
 * Útil quando só precisamos das estatísticas principais
 */
export const useDashboardStats = () => {
  return useQuery({
    queryKey: ['dashboard-stats'],
    queryFn: getDashboardStats,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
    refetchOnWindowFocus: false,
    retry: 2
  });
};

/**
 * Hook específico para dados de gráficos
 * Útil para componentes que só renderizam gráficos
 */
export const useDashboardCharts = () => {
  return useQuery({
    queryKey: ['dashboard-chart-data'],
    queryFn: getChartData,
    staleTime: 10 * 60 * 1000,
    gcTime: 15 * 60 * 1000,
    refetchOnWindowFocus: false,
    retry: 2
  });
};

/**
 * Hook específico para atividades recentes
 * Útil para componentes de notificações/atividades
 */
export const useRecentActivities = (limit: number = 10) => {
  return useQuery({
    queryKey: ['dashboard-recent-activities', limit],
    queryFn: () => getRecentActivities(limit),
    staleTime: 2 * 60 * 1000,
    gcTime: 5 * 60 * 1000,
    refetchOnWindowFocus: false,
    retry: 2
  });
};
