
import { FluxoCaixa } from '@/types';

export const useFluxoCaixaCalculations = (fluxoCaixa: FluxoCaixa[] | undefined, saldoTotal: number | undefined) => {
  const getMonthlyFlowData = () => {
    if (!fluxoCaixa || fluxoCaixa.length === 0) return [];
    
    const months = [...new Set(fluxoCaixa.map(item => {
      const date = new Date(item.data);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
    }))].sort();
    
    const lastSixMonths = months.slice(-6);
    
    return lastSixMonths.map(monthYear => {
      const [year, month] = monthYear.split('-');
      const monthName = new Date(parseInt(year), parseInt(month) - 1).toLocaleString('pt-BR', { month: 'short' });
      
      const monthEntries = fluxoCaixa.filter(item => {
        const date = new Date(item.data);
        const itemYear = date.getFullYear().toString();
        const itemMonth = String(date.getMonth() + 1).padStart(2, '0');
        return `${itemYear}-${itemMonth}` === monthYear;
      });
      
      const entrada = monthEntries
        .filter(item => item.tipo === 'entrada')
        .reduce((sum, item) => sum + item.valor, 0);
        
      const saida = monthEntries
        .filter(item => item.tipo === 'saida')
        .reduce((sum, item) => sum + item.valor, 0);
      
      return {
        name: monthName.charAt(0).toUpperCase() + monthName.slice(1, 3),
        entrada,
        saida
      };
    });
  };

  const getMonthlyTrends = () => {
    const monthlyData = getMonthlyFlowData();
    
    if (monthlyData.length < 2) {
      return {
        entradaTrend: 0,
        saidaTrend: 0
      };
    }
    
    const currentMonth = monthlyData[monthlyData.length - 1];
    const prevMonth = monthlyData[monthlyData.length - 2];
    
    const entradaTrend = prevMonth.entrada !== 0 
      ? Math.round(((currentMonth.entrada - prevMonth.entrada) / prevMonth.entrada) * 100) 
      : 100;
    
    const saidaTrend = prevMonth.saida !== 0 
      ? Math.round(((currentMonth.saida - prevMonth.saida) / prevMonth.saida) * 100) 
      : 100;
    
    return {
      entradaTrend,
      saidaTrend
    };
  };

  const getTotals = () => {
    if (!fluxoCaixa) return { totalIncome: 0, totalExpenses: 0, balance: 0 };
    
    const totalIncome = fluxoCaixa
      .filter(item => item.tipo === 'entrada')
      .reduce((sum, item) => sum + item.valor, 0);
      
    const totalExpenses = fluxoCaixa
      .filter(item => item.tipo === 'saida')
      .reduce((sum, item) => sum + item.valor, 0);
      
    return {
      totalIncome,
      totalExpenses,
      balance: saldoTotal ?? (totalIncome - totalExpenses)
    };
  };

  return {
    monthlyFlowData: getMonthlyFlowData(),
    trends: getMonthlyTrends(),
    totals: getTotals()
  };
};
