
import React from 'react';
import { format } from 'date-fns';
import {
  Wallet,
  TrendingUp,
  TrendingDown,
  PlusCircle,
  ArrowUpRight,
  ArrowDownRight,
  RotateCcw
} from 'lucide-react';

import Sidebar from '@/components/layout/Sidebar';
import Header from '@/components/layout/Header';
import { useSidebar } from '@/contexts/SidebarContext';
import { cn } from '@/lib/utils';
import DashboardCard from '@/components/dashboard/DashboardCard';

import DataTable from '@/components/tables/DataTable';
import FluxoCaixaForm from '@/components/cash-flow/FluxoCaixaForm';
import { useFluxoCaixa } from '@/hooks/useFluxoCaixa';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";

const CashFlow = () => {
  const { collapsed, isMobile } = useSidebar();
  const {
    fluxoCaixa,
    isLoading,
    monthlyFlowData,
    totals,
    trends,
    filters,
    isModalOpen,
    selectedFlow,
    flowToDelete,
    handleFilterChange,
    clearAllFilters,
    hasActiveFilters,
    openEditModal,
    openCreateModal,
    closeModal,
    handleSaveLancamento,
    handleDelete: originalHandleDelete,
    confirmDelete,
    setFlowToDelete
  } = useFluxoCaixa();

  const formatValue = (value: number) => {
    return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
  };

  // Wrapper to handle delete by ID
  const handleDelete = (id: string) => {
    const flow = fluxoCaixa?.find(f => f.id === id);
    if (flow) {
      originalHandleDelete(flow);
    }
  };

  const columns = [
    {
      header: 'Descrição',
      accessor: 'descricao',
      isSortable: true
    },
    {
      header: 'Tipo',
      accessor: 'tipo',
      isSortable: true,
      cell: (value: string) => (
        <div className="flex items-center justify-center">
          <span className={`
            inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
            ${value === 'entrada' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}
          `}>
            {value === 'entrada' ? <ArrowUpRight size={12} className="mr-1" /> : <ArrowDownRight size={12} className="mr-1" />}
            {value === 'entrada' ? 'Entrada' : 'Saída'}
          </span>
        </div>
      ),
      align: 'center' as const,
    },
    {
      header: 'Categoria',
      accessor: 'categoria',
      align: 'center' as const,
    },
    {
      header: 'Data',
      accessor: 'data',
      cell: (value: string) => format(new Date(value), 'dd/MM/yyyy'),
      align: 'center' as const,
    },
    {
      header: 'Valor',
      accessor: 'valor',
      cell: (value: number, row: any) => (
        <div className={`text-right font-semibold ${row.tipo === 'entrada' ? 'text-green-600' : 'text-red-600'}`}>
          {row.tipo === 'entrada' ? '+' : '-'} {formatValue(value)} Kz
        </div>
      ),
      align: 'right' as const,
    },
  ];

  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar />

      <div className={cn(
        "flex-1 flex flex-col transition-all duration-300 overflow-hidden",
        isMobile ? "ml-0" : (collapsed ? "ml-12" : "ml-64")
      )}>
        <div className="flex-1 overflow-y-auto pb-10">
          <div className="page-container">
            <Header
              title="Fluxo de Caixa"
              subtitle="Gerencie as entradas e saídas financeiras do condomínio"
            />

            <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 mt-6 animate-enter">
              <Card className="overflow-hidden border-l-4 border-l-indigo-500 shadow-md hover:shadow-lg transition-all">
                <CardContent className="p-0">
                  <DashboardCard
                    title="Saldo Total"
                    value={`${formatValue(totals.balance)} Kz`}
                    description="Diferença entre entradas e saídas"
                    icon={Wallet}
                    iconBgColor="bg-indigo-100"
                    iconColor="text-indigo-500"
                    className="border-none shadow-none"
                  />
                </CardContent>
              </Card>

              <Card className="overflow-hidden border-l-4 border-l-green-500 shadow-md hover:shadow-lg transition-all">
                <CardContent className="p-0">
                  <DashboardCard
                    title="Total de Entradas"
                    value={`${formatValue(totals.totalIncome)} Kz`}
                    trend={{ value: trends.entradaTrend, isPositive: trends.entradaTrend >= 0 }}
                    description="vs. mês anterior"
                    icon={TrendingUp}
                    iconBgColor="bg-green-100"
                    iconColor="text-green-500"
                    className="border-none shadow-none"
                  />
                </CardContent>
              </Card>

              <Card className="overflow-hidden border-l-4 border-l-red-500 shadow-md hover:shadow-lg transition-all">
                <CardContent className="p-0">
                  <DashboardCard
                    title="Total de Saídas"
                    value={`${formatValue(totals.totalExpenses)} Kz`}
                    trend={{ value: trends.saidaTrend, isPositive: false }}
                    description="vs. mês anterior"
                    icon={TrendingDown}
                    iconBgColor="bg-red-100"
                    iconColor="text-red-500"
                    className="border-none shadow-none"
                  />
                </CardContent>
              </Card>
            </div>

            <div className="mt-8">
              <DataTable
                columns={columns}
                data={fluxoCaixa || []}
                enableSearch={true}
                searchPlaceholder="Pesquisar registros..."
                enablePagination={true}
                actionLabel="Ações"
                isLoading={isLoading}
                onEdit={openEditModal}
                onDelete={handleDelete}
                customControls={
                  <div className="flex flex-wrap items-center gap-3">
                    {/* Filtros */}
                    <div className="flex flex-col">
                      <label className="text-sm font-medium text-gray-700 mb-1">Tipo</label>
                      <select
                        value={filters.tipo || ''}
                        onChange={(e) => handleFilterChange('tipo', e.target.value || null)}
                        className="w-32 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                      >
                        <option value="">Todos</option>
                        <option value="entrada">Entrada</option>
                        <option value="saida">Saída</option>
                      </select>
                    </div>

                    <div className="flex flex-col">
                      <label className="text-sm font-medium text-gray-700 mb-1">Mês</label>
                      <select
                        value={filters.mes || ''}
                        onChange={(e) => handleFilterChange('mes', e.target.value || null)}
                        className="w-36 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                      >
                        <option value="">Todos</option>
                        <option value="01">Janeiro</option>
                        <option value="02">Fevereiro</option>
                        <option value="03">Março</option>
                        <option value="04">Abril</option>
                        <option value="05">Maio</option>
                        <option value="06">Junho</option>
                        <option value="07">Julho</option>
                        <option value="08">Agosto</option>
                        <option value="09">Setembro</option>
                        <option value="10">Outubro</option>
                        <option value="11">Novembro</option>
                        <option value="12">Dezembro</option>
                      </select>
                    </div>

                    <div className="flex flex-col">
                      <label className="text-sm font-medium text-gray-700 mb-1">Ano</label>
                      <select
                        value={filters.ano || ''}
                        onChange={(e) => handleFilterChange('ano', e.target.value || null)}
                        className="w-24 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                      >
                        <option value="">Todos</option>
                        <option value="2025">2025</option>
                        <option value="2024">2024</option>
                        <option value="2023">2023</option>
                      </select>
                    </div>

                    {/* Botão Limpar Filtros */}
                    {hasActiveFilters && (
                      <div className="flex flex-col justify-end">
                        <button
                          onClick={clearAllFilters}
                          className="flex items-center gap-2 px-3 py-2 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg border border-gray-300 transition-all duration-200 hover:shadow-sm mt-6"
                          title="Limpar todos os filtros"
                        >
                          <RotateCcw size={16} />
                          <span>Limpar</span>
                        </button>
                      </div>
                    )}

                    {/* Botão Novo Registro */}
                    <div className="flex flex-col justify-end">
                      <Button
                        onClick={openCreateModal}
                        className="flex items-center gap-2 bg-primary text-white hover:bg-primary-600 mt-6"
                      >
                        <PlusCircle size={18} />
                        <span>Novo Registro</span>
                      </Button>
                    </div>
                  </div>
                }
              />
            </div>

            {/* Form modal for adding/editing cash flow record */}
            {isModalOpen && (
              <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
                <div className="bg-white rounded-xl shadow-2xl w-full max-w-md p-6 animate-scale-up relative">
                  <button
                    onClick={closeModal}
                    className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                  <h2 className="text-xl font-bold text-gray-800 mb-4">
                    {selectedFlow ? 'Editar Registro' : 'Novo Registro'}
                  </h2>

                  <FluxoCaixaForm
                    initialData={selectedFlow}
                    onSubmit={handleSaveLancamento}
                    onCancel={closeModal}
                  />
                </div>
              </div>
            )}

            {/* Delete Confirmation Dialog */}
            <AlertDialog open={flowToDelete !== null} onOpenChange={(open) => !open && setFlowToDelete(null)}>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Excluir Lançamento</AlertDialogTitle>
                  <AlertDialogDescription>
                    Tem certeza que deseja excluir este lançamento?
                    Esta ação não pode ser desfeita.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancelar</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={confirmDelete}
                    className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
                  >
                    Excluir
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CashFlow;
