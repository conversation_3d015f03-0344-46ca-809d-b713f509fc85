
-- Função para eliminar utilizador completo (incluindo perfil e acessos)
CREATE OR REPLACE FUNCTION public.delete_user_completely(p_email text)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
  user_record RECORD;
BEGIN
  -- Verificar se o utilizador atual é admin
  IF NOT EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() AND role = 'admin'
  ) THEN
    RAISE EXCEPTION 'Acesso negado: privilégios de administrador necessários';
  END IF;

  -- Encontrar o utilizador pelo email
  SELECT p.id, p.email, p.name 
  INTO user_record
  FROM public.profiles p 
  WHERE p.email = p_email;

  IF NOT FOUND THEN
    RAISE EXCEPTION 'Utilizador com email % não encontrado', p_email;
  END IF;

  -- Eliminar acessos de apartamento
  DELETE FROM public.apartment_access 
  WHERE user_id = user_record.id;

  -- Eliminar perfil
  DELETE FROM public.profiles 
  WHERE id = user_record.id;

  -- Log da operação
  RAISE NOTICE 'Utilizador % (%) eliminado com sucesso', user_record.name, user_record.email;

  RETURN true;
END;
$function$;

-- Função para listar utilizadores com detalhes de acesso
CREATE OR REPLACE FUNCTION public.get_user_details_by_email(p_email text)
RETURNS TABLE(
  user_id uuid,
  name text,
  email text,
  role text,
  apartment_id text,
  has_active_access boolean,
  created_at timestamp with time zone
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
BEGIN
  -- Verificar se o utilizador atual é admin
  IF NOT EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() AND role = 'admin'
  ) THEN
    RAISE EXCEPTION 'Acesso negado: privilégios de administrador necessários';
  END IF;

  RETURN QUERY
  SELECT 
    p.id as user_id,
    p.name,
    p.email,
    p.role,
    p.apartment_id,
    CASE 
      WHEN aa.is_active = true THEN true 
      ELSE false 
    END as has_active_access,
    p.created_at
  FROM public.profiles p
  LEFT JOIN public.apartment_access aa ON p.id = aa.user_id AND aa.is_active = true
  WHERE p.email = p_email;
END;
$function$;
