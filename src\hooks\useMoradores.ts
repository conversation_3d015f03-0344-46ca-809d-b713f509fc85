
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { Morador } from '@/types';
import { 
  getMoradores, 
  createMorador, 
  updateMorador, 
  deleteMorador 
} from '@/utils/supabase-helpers';

export function useMoradores() {
  const queryClient = useQueryClient();

  // Obter todos os moradores
  const { 
    data: moradores, 
    isLoading, 
    error, 
    refetch 
  } = useQuery({
    queryKey: ['moradores'],
    queryFn: getMoradores,
  });

  // Adicionar novo morador
  const { mutate: addMorador, isPending: isAddingMorador } = useMutation({
    mutationFn: (newMorador: Omit<Morador, 'id' | 'created_at' | 'updated_at'>) => {
      console.log('🔄 Hook: Iniciando criação de morador:', newMorador);
      return createMorador(newMorador);
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['moradores'] });
      toast.success('Morador adicionado com sucesso!');
      console.log('✅ Hook: Morador criado com sucesso:', data);
    },
    onError: (error) => {
      console.error('❌ Hook: Erro ao adicionar morador:', error);
      toast.error(error instanceof Error ? error.message : 'Erro ao adicionar morador.');
    },
  });

  // Atualizar morador existente
  const { mutate: editMorador, isPending: isEditingMorador } = useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<Morador> }) => {
      console.log(`🔄 Hook: Iniciando edição do morador ${id}:`, data);
      return updateMorador(id, data);
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['moradores'] });
      toast.success('Morador atualizado com sucesso!');
      console.log('✅ Hook: Morador atualizado com sucesso:', data);
    },
    onError: (error) => {
      console.error('❌ Hook: Erro ao atualizar morador:', error);
      toast.error(error instanceof Error ? error.message : 'Erro ao atualizar morador.');
    },
  });

  // Excluir morador
  const { mutate: removeMorador, isPending: isDeletingMorador } = useMutation({
    mutationFn: (id: string) => {
      console.log(`🗑️ Hook: Iniciando exclusão do morador ${id}`);
      return deleteMorador(id);
    },
    onSuccess: (success) => {
      if (success) {
        queryClient.invalidateQueries({ queryKey: ['moradores'] });
        toast.success('Morador excluído com sucesso!');
        console.log('✅ Hook: Morador excluído com sucesso');
      } else {
        toast.error('Falha ao excluir morador.');
        console.error('❌ Hook: Falha na exclusão do morador');
      }
    },
    onError: (error) => {
      console.error('❌ Hook: Erro ao excluir morador:', error);
      toast.error(error instanceof Error ? error.message : 'Erro ao excluir morador.');
    },
  });

  // Estatísticas úteis
  const stats = {
    total: moradores?.length || 0,
    ativos: moradores?.filter(m => m.status !== 'Inativo').length || 0,
    isentos: moradores?.filter(m => m.isento_quotas).length || 0,
  };

  return {
    moradores,
    isLoading,
    isAddingMorador,
    isEditingMorador,
    isDeletingMorador,
    error,
    stats,
    refetch,
    addMorador,
    editMorador,
    removeMorador
  };
}

export default useMoradores;
