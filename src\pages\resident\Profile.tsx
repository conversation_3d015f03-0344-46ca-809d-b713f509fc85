import React, { useState } from 'react';
import ResidentLayout from '@/components/layout/resident/ResidentLayout';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger 
} from '@/components/ui/dialog';
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger 
} from '@/components/ui/alert-dialog';
import { User, MapPin, Calendar, Shield, Key, Eye, EyeOff } from 'lucide-react';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';

const Profile = () => {
  const { profile, updateProfile, isFirstLogin, user } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [isChangingPassword, setIsChangingPassword] = useState(false);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [formData, setFormData] = useState({
    name: profile?.name || '',
    email: user?.email || '',
  });
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });

  const handleProfileUpdate = async () => {
    try {
      await updateProfile({
        name: formData.name,
        // Mark first login as completed if this is first login
        ...(isFirstLogin && { first_login: false })
      });
      setIsEditing(false);
      
      if (isFirstLogin) {
        toast.success('Perfil configurado com sucesso! Bem-vindo ao sistema.');
      } else {
        toast.success('Perfil atualizado com sucesso!');
      }
    } catch (error) {
      toast.error('Erro ao atualizar perfil');
    }
  };

  const handlePasswordChange = async () => {
    try {
      if (passwordData.newPassword !== passwordData.confirmPassword) {
        toast.error('As senhas não coincidem');
        return;
      }

      if (passwordData.newPassword.length < 8) {
        toast.error('A senha deve ter pelo menos 8 caracteres');
        return;
      }

      const { error } = await supabase.auth.updateUser({
        password: passwordData.newPassword
      });

      if (error) {
        throw error;
      }

      setPasswordData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: '',
      });
      setIsChangingPassword(false);
      toast.success('Senha alterada com sucesso!');

      // Mark first login as completed if this is first login
      if (isFirstLogin) {
        await updateProfile({ first_login: false });
      }
    } catch (error: any) {
      console.error('Error changing password:', error);
      toast.error('Erro ao alterar senha: ' + error.message);
    }
  };

  const handleFirstLoginSetup = () => {
    setIsEditing(true);
  };

  if (!profile) {
    return (
      <ResidentLayout title="Meu Perfil" subtitle="Carregando informações do perfil">
        <div className="flex items-center justify-center h-64">
          <p>Carregando perfil...</p>
        </div>
      </ResidentLayout>
    );
  }

  return (
    <ResidentLayout title="Meu Perfil" subtitle="Gerencie suas informações pessoais e configurações de conta">
      <div className="space-y-6">
        {/* First Login Banner */}
        {isFirstLogin && (
          <Card className="border-blue-200 bg-blue-50">
            <CardHeader>
              <CardTitle className="text-blue-900 flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Primeiro Acesso Detectado
              </CardTitle>
              <CardDescription className="text-blue-700">
                Para sua segurança, complete a configuração do seu perfil e altere sua senha.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex gap-3">
                <Button onClick={handleFirstLoginSetup} className="bg-blue-600 hover:bg-blue-700">
                  Configurar Perfil
                </Button>
                <Dialog open={isChangingPassword} onOpenChange={setIsChangingPassword}>
                  <DialogTrigger asChild>
                    <Button variant="outline" className="border-blue-200">
                      <Key className="h-4 w-4 mr-2" />
                      Alterar Senha
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Alterar Senha</DialogTitle>
                      <DialogDescription>
                        {isFirstLogin 
                          ? 'Por favor, altere sua senha temporária por uma senha segura de sua escolha.'
                          : 'Digite sua nova senha abaixo.'
                        }
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="newPassword">Nova Senha</Label>
                        <div className="relative">
                          <Input
                            id="newPassword"
                            type={showNewPassword ? 'text' : 'password'}
                            value={passwordData.newPassword}
                            onChange={(e) => setPasswordData(prev => ({ ...prev, newPassword: e.target.value }))}
                            placeholder="Digite sua nova senha"
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="absolute right-0 top-0 h-full px-3"
                            onClick={() => setShowNewPassword(!showNewPassword)}
                          >
                            {showNewPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                          </Button>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="confirmPassword">Confirmar Nova Senha</Label>
                        <Input
                          id="confirmPassword"
                          type="password"
                          value={passwordData.confirmPassword}
                          onChange={(e) => setPasswordData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                          placeholder="Confirme sua nova senha"
                        />
                      </div>
                      <div className="flex justify-end space-x-2">
                        <Button variant="outline" onClick={() => setIsChangingPassword(false)}>
                          Cancelar
                        </Button>
                        <Button onClick={handlePasswordChange}>
                          Alterar Senha
                        </Button>
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Profile Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Meu Perfil
            </CardTitle>
            <CardDescription>
              Gerencie suas informações pessoais e configurações de conta
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Avatar and Basic Info */}
            <div className="flex items-center space-x-4">
              <Avatar className="h-20 w-20">
                <AvatarImage src={profile.photo || undefined} />
                <AvatarFallback className="text-lg">
                  {profile.name?.split(' ').map(n => n[0]).join('').toUpperCase() || 'U'}
                </AvatarFallback>
              </Avatar>
              <div className="space-y-1">
                <h3 className="text-xl font-semibold">{profile.name || 'Nome não definido'}</h3>
                <p className="text-gray-600">{user?.email}</p>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">
                    {profile.role === 'resident' ? 'Morador' : profile.role}
                  </Badge>
                  {profile.apartment_id && (
                    <Badge variant="outline" className="flex items-center gap-1">
                      <MapPin className="h-3 w-3" />
                      Apt. {profile.apartment_id}
                    </Badge>
                  )}
                </div>
              </div>
            </div>

            <Separator />

            {/* Profile Information */}
            <div className="space-y-4">
              <h4 className="font-medium">Informações Pessoais</h4>
              
              {isEditing ? (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Nome Completo</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="Digite seu nome completo"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      value={formData.email}
                      disabled
                      className="bg-gray-50"
                    />
                    <p className="text-sm text-gray-500">
                      O email não pode ser alterado. Entre em contato com a administração se necessário.
                    </p>
                  </div>
                  <div className="flex space-x-2">
                    <Button onClick={handleProfileUpdate}>Salvar Alterações</Button>
                    <Button 
                      variant="outline" 
                      onClick={() => {
                        setIsEditing(false);
                        setFormData({
                          name: profile.name || '',
                          email: user?.email || '',
                        });
                      }}
                    >
                      Cancelar
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="text-sm text-gray-500">Nome</p>
                      <p className="font-medium">{profile.name || 'Não definido'}</p>
                    </div>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Email</p>
                    <p className="font-medium">{user?.email}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Apartamento</p>
                    <p className="font-medium">{profile.apartment_id || 'Não definido'}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Último acesso</p>
                    <p className="font-medium">
                      {profile.last_login 
                        ? new Date(profile.last_login).toLocaleString('pt-BR')
                        : 'Primeiro acesso'
                      }
                    </p>
                  </div>
                  <Button onClick={() => setIsEditing(true)}>
                    Editar Perfil
                  </Button>
                </div>
              )}
            </div>

            <Separator />

            {/* Security Section */}
            <div className="space-y-4">
              <h4 className="font-medium">Segurança</h4>
              <div className="space-y-3">
                <Dialog open={isChangingPassword} onOpenChange={setIsChangingPassword}>
                  <DialogTrigger asChild>
                    <Button variant="outline" className="w-full sm:w-auto">
                      <Key className="h-4 w-4 mr-2" />
                      Alterar Senha
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Alterar Senha</DialogTitle>
                      <DialogDescription>
                        Digite sua nova senha abaixo. A senha deve ter pelo menos 8 caracteres.
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="newPassword">Nova Senha</Label>
                        <div className="relative">
                          <Input
                            id="newPassword"
                            type={showNewPassword ? 'text' : 'password'}
                            value={passwordData.newPassword}
                            onChange={(e) => setPasswordData(prev => ({ ...prev, newPassword: e.target.value }))}
                            placeholder="Digite sua nova senha"
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="absolute right-0 top-0 h-full px-3"
                            onClick={() => setShowNewPassword(!showNewPassword)}
                          >
                            {showNewPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                          </Button>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="confirmPassword">Confirmar Nova Senha</Label>
                        <Input
                          id="confirmPassword"
                          type="password"
                          value={passwordData.confirmPassword}
                          onChange={(e) => setPasswordData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                          placeholder="Confirme sua nova senha"
                        />
                      </div>
                      <div className="flex justify-end space-x-2">
                        <Button variant="outline" onClick={() => setIsChangingPassword(false)}>
                          Cancelar
                        </Button>
                        <Button onClick={handlePasswordChange}>
                          Alterar Senha
                        </Button>
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </ResidentLayout>
  );
};

export default Profile;
