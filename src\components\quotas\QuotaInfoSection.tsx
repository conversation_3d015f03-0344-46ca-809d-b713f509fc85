
import React from 'react';
import { Shield, DollarSign } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Morador } from '@/types';

interface QuotaInfoSectionProps {
  moradores: Morador[];
  valorQuota: number;
  includeExempt: boolean;
  generationType: 'current' | 'custom';
  startMonth: number;
  startYear: number;
  months: { value: number; label: string }[];
}

export const QuotaInfoSection: React.FC<QuotaInfoSectionProps> = ({
  moradores,
  valorQuota,
  includeExempt,
  generationType,
  startMonth,
  startYear,
  months
}) => {
  const exemptCount = moradores.filter(m => m.isento_quotas).length;
  const activeCount = moradores.length - exemptCount;

  return (
    <div className="space-y-4">
      {/* Informações do Sistema */}
      <div className="bg-blue-50 p-3 rounded-lg space-y-2">
        <div className="text-sm text-blue-800">
          <strong>Valor configurado:</strong> {valorQuota.toLocaleString()} Kz por quota
        </div>
        <div className="text-sm text-blue-700">
          <strong>Moradores:</strong> {activeCount} ativos
          {exemptCount > 0 && (
            <span className="ml-2">
              + <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 text-xs">
                <Shield className="w-3 h-3 mr-1" />
                {exemptCount} isentos
              </Badge>
            </span>
          )}
        </div>
      </div>

      {/* Integração com Fluxo de Caixa */}
      <div className="bg-green-50 p-3 rounded-lg space-y-2">
        <div className="flex items-center gap-2 text-green-800">
          <DollarSign className="w-4 h-4" />
          <strong>Integração Automática</strong>
        </div>
        <div className="text-sm text-green-700">
          As quotas geradas serão automaticamente integradas ao fluxo de caixa:
        </div>
        <ul className="text-xs text-green-600 ml-4 space-y-1">
          <li>• Lançamento único criado para cada mês</li>
          <li>• Valor atualizado automaticamente conforme pagamentos</li>
          <li>• Multas regularizadas geram lançamentos separados</li>
        </ul>
      </div>

      {/* Resumo */}
      <div className="bg-gray-50 p-3 rounded-lg text-sm text-gray-700">
        <strong>Resumo:</strong> Serão geradas quotas para{' '}
        <strong>{includeExempt ? moradores.length : activeCount} moradores</strong>
        {generationType === 'current'
          ? ' para o mês atual'
          : ` desde ${months.find(m => m.value === startMonth)?.label}/${startYear} até hoje`
        }.
      </div>
    </div>
  );
};
