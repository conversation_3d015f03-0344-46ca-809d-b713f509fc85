
/**
 * Hook use-mobile - Detecção de Dispositivos Móveis
 *
 * Hook personalizado para detectar se o usuário está usando um dispositivo móvel
 * baseado na largura da tela. Utiliza Media Query API para detecção responsiva.
 *
 * <AUTHOR> Prédio Azul
 * @version 1.0
 */

import * as React from "react"

// Breakpoint para dispositivos móveis (768px)
const MOBILE_BREAKPOINT = 768

/**
 * Hook para detectar se o dispositivo é móvel
 *
 * Monitora mudanças na largura da tela e retorna true se for menor que 768px.
 * Utiliza Media Query API para performance otimizada.
 *
 * @returns {boolean} true se for dispositivo móvel, false caso contrário
 */
export function useIsMobile() {
  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined)

  React.useEffect(() => {
    // Cria Media Query para detectar telas menores que 768px
    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)

    // Função para atualizar estado quando a tela muda
    const onChange = () => {
      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)
    }

    // Adiciona listener para mudanças na tela
    mql.addEventListener("change", onChange)

    // Define estado inicial
    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)

    // Cleanup: remove listener quando componente desmonta
    return () => mql.removeEventListener("change", onChange)
  }, [])

  return !!isMobile
}

/**
 * Alias para compatibilidade com versões anteriores
 * @deprecated Use useIsMobile() em vez disso
 */
export const useMobile = useIsMobile;
