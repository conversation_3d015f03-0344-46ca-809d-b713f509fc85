
-- <PERSON><PERSON>, vamos verificar e corrigir a ligação entre perfis e moradores
-- Conectar o morador do apartamento 1ºD ao usuário correto
UPDATE public.moradores 
SET user_id = (
  SELECT id FROM public.profiles 
  WHERE apartment_id = '1ºD' 
  LIMIT 1
)
WHERE apartamento = '1ºD' AND user_id IS NULL;

-- Adicionar políticas RLS para a tabela moradores
ALTER TABLE public.moradores ENABLE ROW LEVEL SECURITY;

-- Política para que moradores vejam apenas seus próprios dados
CREATE POLICY "Moradores podem ver seus próprios dados" 
ON public.moradores 
FOR SELECT 
USING (user_id = auth.uid());

-- Política para que admins vejam todos os moradores
CREATE POLICY "Admins podem ver todos os moradores" 
ON public.moradores 
FOR SELECT 
USING (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() AND role = 'admin'
  )
);

-- Ad<PERSON><PERSON>r políticas RLS para a tabela quotas se não existirem
ALTER TABLE public.quotas ENABLE ROW LEVEL SECURITY;

-- Política para que moradores vejam apenas suas quotas
CREATE POLICY "Moradores podem ver suas quotas" 
ON public.quotas 
FOR SELECT 
USING (
  EXISTS (
    SELECT 1 FROM public.moradores 
    WHERE id = quotas.morador_id AND user_id = auth.uid()
  )
);

-- Política para que admins vejam todas as quotas
CREATE POLICY "Admins podem ver todas as quotas" 
ON public.quotas 
FOR SELECT 
USING (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() AND role = 'admin'
  )
);
