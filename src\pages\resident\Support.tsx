import React, { useState } from 'react';
import ResidentLayout from '@/components/layout/resident/ResidentLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  HelpCircle, 
  Phone, 
  Mail, 
  MessageSquare, 
  Clock,
  CheckCircle,
  AlertCircle,
  FileText,
  Users,
  Settings,
  Send
} from 'lucide-react';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/AuthContext';

const Support = () => {
  const { profile } = useAuth();
  const [contactForm, setContactForm] = useState({
    subject: '',
    message: '',
    priority: 'normal'
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Simular envio do formulário
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast.success('Mensagem enviada com sucesso! Entraremos em contato em breve.');
      setContactForm({ subject: '', message: '', priority: 'normal' });
    } catch (error) {
      toast.error('Erro ao enviar mensagem. Tente novamente.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const faqItems = [
    {
      question: "Como posso visualizar minhas quotas?",
      answer: "Acesse a seção 'Minhas Quotas' no menu lateral para ver todo o histórico de pagamentos, valores pendentes e comprovantes."
    },
    {
      question: "Como baixar comprovantes de pagamento?",
      answer: "Na página de quotas, clique no botão 'Comprovante' ao lado de cada quota paga para fazer o download do documento."
    },
    {
      question: "O que fazer se encontrar um erro no valor da quota?",
      answer: "Entre em contato conosco através do formulário abaixo ou pelos canais de atendimento. Nossa equipe verificará e corrigirá qualquer inconsistência."
    },
    {
      question: "Como alterar meus dados pessoais?",
      answer: "Vá para a seção 'Perfil' no menu lateral, onde você pode atualizar suas informações pessoais e alterar sua senha."
    },
    {
      question: "Posso pagar quotas em atraso?",
      answer: "Sim, quotas em atraso podem ser pagas a qualquer momento. Entre em contato para verificar valores atualizados com multas."
    }
  ];

  const contactChannels = [
    {
      icon: Phone,
      title: "Telefone",
      description: "Atendimento de segunda a sexta",
      contact: "+244 923 456 789",
      hours: "08:00 - 17:00"
    },
    {
      icon: Mail,
      title: "E-mail",
      description: "Resposta em até 24 horas",
      contact: "<EMAIL>",
      hours: "24/7"
    },
    {
      icon: MessageSquare,
      title: "WhatsApp",
      description: "Atendimento rápido",
      contact: "+244 923 456 789",
      hours: "08:00 - 20:00"
    }
  ];

  return (
    <ResidentLayout 
      title="Suporte" 
      subtitle="Estamos aqui para ajudar com suas dúvidas e necessidades"
    >
      <div className="space-y-8">
        {/* Canais de Atendimento */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <HelpCircle className="h-5 w-5" />
              Canais de Atendimento
            </CardTitle>
            <CardDescription>
              Entre em contato conosco através dos canais disponíveis
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {contactChannels.map((channel, index) => (
                <div key={index} className="text-center p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                  <channel.icon className="h-8 w-8 mx-auto mb-3 text-primary" />
                  <h3 className="font-semibold text-gray-900 mb-1">{channel.title}</h3>
                  <p className="text-sm text-gray-600 mb-2">{channel.description}</p>
                  <p className="font-medium text-primary">{channel.contact}</p>
                  <p className="text-xs text-gray-500">{channel.hours}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Formulário de Contato */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Send className="h-5 w-5" />
              Enviar Mensagem
            </CardTitle>
            <CardDescription>
              Descreva sua dúvida ou problema e entraremos em contato
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="subject">Assunto</Label>
                  <Input
                    id="subject"
                    value={contactForm.subject}
                    onChange={(e) => setContactForm(prev => ({ ...prev, subject: e.target.value }))}
                    placeholder="Descreva brevemente o assunto"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="priority">Prioridade</Label>
                  <select
                    id="priority"
                    value={contactForm.priority}
                    onChange={(e) => setContactForm(prev => ({ ...prev, priority: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  >
                    <option value="low">Baixa</option>
                    <option value="normal">Normal</option>
                    <option value="high">Alta</option>
                    <option value="urgent">Urgente</option>
                  </select>
                </div>
              </div>
              
              <div>
                <Label htmlFor="message">Mensagem</Label>
                <Textarea
                  id="message"
                  value={contactForm.message}
                  onChange={(e) => setContactForm(prev => ({ ...prev, message: e.target.value }))}
                  placeholder="Descreva detalhadamente sua dúvida ou problema..."
                  rows={5}
                  required
                />
              </div>

              <div className="flex justify-end">
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? (
                    <>
                      <Clock className="h-4 w-4 mr-2 animate-spin" />
                      Enviando...
                    </>
                  ) : (
                    <>
                      <Send className="h-4 w-4 mr-2" />
                      Enviar Mensagem
                    </>
                  )}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>

        {/* FAQ */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Perguntas Frequentes
            </CardTitle>
            <CardDescription>
              Respostas para as dúvidas mais comuns
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {faqItems.map((item, index) => (
                <div key={index} className="border-b border-gray-200 pb-4 last:border-b-0">
                  <h3 className="font-semibold text-gray-900 mb-2 flex items-start gap-2">
                    <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                    {item.question}
                  </h3>
                  <p className="text-gray-600 ml-7">{item.answer}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Informações Adicionais */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Administração
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div>
                  <p className="font-medium">Horário de Funcionamento</p>
                  <p className="text-sm text-gray-600">Segunda a Sexta: 08:00 - 17:00</p>
                  <p className="text-sm text-gray-600">Sábado: 08:00 - 12:00</p>
                </div>
                <Separator />
                <div>
                  <p className="font-medium">Localização</p>
                  <p className="text-sm text-gray-600">Edifício Prédio Azul</p>
                  <p className="text-sm text-gray-600">Rua Principal, Luanda</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Status do Sistema
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Sistema de Quotas</span>
                  <Badge variant="default" className="bg-green-100 text-green-800">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Operacional
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Portal do Morador</span>
                  <Badge variant="default" className="bg-green-100 text-green-800">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Operacional
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Sistema de Documentos</span>
                  <Badge variant="default" className="bg-green-100 text-green-800">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Operacional
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </ResidentLayout>
  );
};

export default Support;
