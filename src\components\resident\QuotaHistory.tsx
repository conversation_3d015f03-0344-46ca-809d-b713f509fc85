
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { QuotaHistory as QuotaHistoryType } from '@/types';
import QuotaHistoryFilters from './quota-history/QuotaHistoryFilters';
import QuotaHistoryTable from './quota-history/QuotaHistoryTable';
import QuotaHistoryPagination from './quota-history/QuotaHistoryPagination';

interface QuotaHistoryProps {
  quotas: QuotaHistoryType[];
  onDownloadReceipt?: (receipt: string) => void;
}

const QuotaHistory: React.FC<QuotaHistoryProps> = ({ quotas, onDownloadReceipt }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [yearFilter, setYearFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  const getMonthName = (month: number) => {
    const months = [
      'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
      'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
    ];
    return months[month - 1];
  };

  // Filtrar quotas
  const filteredQuotas = quotas.filter(quota => {
    const matchesSearch = searchTerm === '' || 
      getMonthName(quota.month).toLowerCase().includes(searchTerm.toLowerCase()) ||
      quota.year.toString().includes(searchTerm);
    
    const matchesStatus = statusFilter === 'all' || quota.status === statusFilter;
    const matchesYear = yearFilter === 'all' || quota.year.toString() === yearFilter;
    
    return matchesSearch && matchesStatus && matchesYear;
  });

  // Paginação
  const totalPages = Math.ceil(filteredQuotas.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentQuotas = filteredQuotas.slice(startIndex, endIndex);

  // Obter anos únicos para o filtro
  const availableYears = [...new Set(quotas.map(quota => quota.year))].sort((a, b) => b - a);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const resetFilters = () => {
    setSearchTerm('');
    setStatusFilter('all');
    setYearFilter('all');
    setCurrentPage(1);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-semibold">Histórico de Quotas</CardTitle>
      </CardHeader>
      <CardContent>
        <QuotaHistoryFilters
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          statusFilter={statusFilter}
          onStatusFilterChange={setStatusFilter}
          yearFilter={yearFilter}
          onYearFilterChange={setYearFilter}
          availableYears={availableYears}
          onResetFilters={resetFilters}
        />

        <QuotaHistoryTable
          quotas={currentQuotas}
          onDownloadReceipt={onDownloadReceipt}
        />

        {/* Mensagem quando não há resultados */}
        {filteredQuotas.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            Nenhuma quota encontrada com os filtros aplicados.
          </div>
        )}

        <QuotaHistoryPagination
          currentPage={currentPage}
          totalPages={totalPages}
          startIndex={startIndex}
          endIndex={endIndex}
          totalItems={filteredQuotas.length}
          onPageChange={handlePageChange}
        />
      </CardContent>
    </Card>
  );
};

export default QuotaHistory;
