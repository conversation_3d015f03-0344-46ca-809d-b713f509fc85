import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';

interface RoleBasedRouteProps {
  children: React.ReactNode;
  allowedRoles: ('admin' | 'resident' | 'convidado')[];
  redirectTo?: string;
}

const RoleBasedRoute: React.FC<RoleBasedRouteProps> = ({ 
  children, 
  allowedRoles, 
  redirectTo 
}) => {
  const { isAuthenticated, profile, loading } = useAuth();
  const location = useLocation();

  // Show loading while checking authentication
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location.pathname }} replace />;
  }

  // Check if user has required role
  if (profile?.role && !allowedRoles.includes(profile.role)) {
    // Redirect based on user role if no specific redirect is provided
    if (!redirectTo) {
      switch (profile.role) {
        case 'admin':
          return <Navigate to="/admin/dashboard" replace />;
        case 'resident':
          return <Navigate to="/resident/dashboard" replace />;
        default:
          return <Navigate to="/login" replace />;
      }
    }
    return <Navigate to={redirectTo} replace />;
  }

  return <>{children}</>;
};

export default RoleBasedRoute;
