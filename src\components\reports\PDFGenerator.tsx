/**
 * Componente PDFGenerator - Sistema Prédio Azul
 *
 * Responsável por gerar PDFs dos relatórios com formatação
 * profissional, logo da empresa e padrões angolanos.
 */

import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import { ReportData, PDFConfig } from '@/types/reports';

// Funções de formatação locais
const formatCurrencyAO = (value: number): string => {
  return new Intl.NumberFormat('pt-AO', {
    style: 'currency',
    currency: 'AOA',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(value).replace('AOA', 'Kz');
};

const formatDatePT = (date: string | Date): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleDateString('pt-PT', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });
};

// Extensão do tipo jsPDF para incluir autoTable
declare module 'jspdf' {
  interface jsPDF {
    autoTable: (options: any) => jsPDF;
  }
}

/**
 * Configuração padrão para PDFs
 */
const DEFAULT_PDF_CONFIG: PDFConfig = {
  includeWatermark: true,
  includeLogo: true,
  pageNumbers: true,
  orientation: 'portrait',
  fontSize: 10
};

/**
 * Cores do tema Prédio Azul
 */
const THEME_COLORS = {
  primary: '#0066B3',
  secondary: '#00A0E3',
  text: '#374151',
  lightGray: '#F3F4F6',
  darkGray: '#6B7280'
};

/**
 * Carrega e converte imagem para base64
 */
const loadImageAsBase64 = async (imagePath: string): Promise<string> => {
  try {
    const response = await fetch(imagePath);
    const blob = await response.blob();

    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  } catch (error) {
    console.error('Erro ao carregar imagem:', error);
    return '';
  }
};

/**
 * Adiciona cabeçalho ao PDF
 */
const addHeader = async (doc: jsPDF, reportData: ReportData, config: PDFConfig) => {
  const pageWidth = doc.internal.pageSize.width;

  // Adicionar logo se configurado
  if (config.includeLogo) {
    try {
      const logoBase64 = await loadImageAsBase64('/Predio Azul Logo.jpg');
      if (logoBase64) {
        doc.addImage(logoBase64, 'JPEG', 15, 15, 30, 20);
      }
    } catch (error) {
      console.warn('Não foi possível carregar o logo:', error);
    }
  }

  // Título principal
  doc.setFontSize(18);
  doc.setTextColor(THEME_COLORS.primary);
  doc.setFont('helvetica', 'bold');
  doc.text(reportData.title, pageWidth / 2, 25, { align: 'center' });

  // Subtítulo
  doc.setFontSize(12);
  doc.setTextColor(THEME_COLORS.text);
  doc.setFont('helvetica', 'normal');
  doc.text(reportData.subtitle, pageWidth / 2, 32, { align: 'center' });

  // Data de geração
  doc.setFontSize(10);
  doc.setTextColor(THEME_COLORS.darkGray);
  doc.text(
    `Gerado em: ${formatDatePT(reportData.generatedAt)}`,
    pageWidth - 15,
    5,
    { align: 'right' }
  );

  // Linha separadora
  doc.setDrawColor(THEME_COLORS.primary);
  doc.setLineWidth(0.5);
  doc.line(15, 40, pageWidth - 15, 40);
};

/**
 * Adiciona rodapé ao PDF
 */
const addFooter = (doc: jsPDF, config: PDFConfig) => {
  const pageHeight = doc.internal.pageSize.height;
  const pageWidth = doc.internal.pageSize.width;

  // Linha separadora
  doc.setDrawColor(THEME_COLORS.lightGray);
  doc.setLineWidth(0.3);
  doc.line(15, pageHeight - 25, pageWidth - 15, pageHeight - 25);

  // Texto do rodapé
  doc.setFontSize(8);
  doc.setTextColor(THEME_COLORS.darkGray);
  doc.setFont('helvetica', 'normal');
  doc.text(
    'Desenvolvido por CC',
    15,
    pageHeight - 15
  );

  // Número da página se configurado
  if (config.pageNumbers) {
    const pageNumber = (doc as any).internal.getCurrentPageInfo().pageNumber;
    doc.text(
      `Página ${pageNumber}`,
      pageWidth - 15,
      pageHeight - 15,
      { align: 'right' }
    );
  }
};

/**
 * Adiciona marca d'água ao PDF
 */
const addWatermark = async (doc: jsPDF) => {
  try {
    const logoBase64 = await loadImageAsBase64('/Predio Azul Logo.jpg');
    if (logoBase64) {
      const pageWidth = doc.internal.pageSize.width;
      const pageHeight = doc.internal.pageSize.height;

      // Adicionar logo como marca d'água (transparente e centralizado)
      doc.saveGraphicsState();
      doc.setGState(new (doc as any).GState({ opacity: 0.1 }));
      doc.addImage(
        logoBase64,
        'JPEG',
        pageWidth / 2 - 40,
        pageHeight / 2 - 30,
        80,
        60
      );
      doc.restoreGraphicsState();
    }
  } catch (error) {
    console.warn('Não foi possível adicionar marca d\'água:', error);
  }
};

/**
 * Gera PDF para relatório de quotas
 */
const generateQuotasPDF = async (
  doc: jsPDF,
  reportData: any,
  config: PDFConfig
) => {
  try {
    console.log('📊 Gerando PDF de quotas...');

    const data = reportData;
    let yPosition = 55;

    // Validar se os dados necessários existem
    if (!data.summary) {
      throw new Error('Dados de resumo não encontrados');
    }

    // Resumo
    doc.setFontSize(14);
    doc.setTextColor(THEME_COLORS.primary);
    doc.setFont('helvetica', 'bold');
    doc.text('Resumo', 15, yPosition);
    yPosition += 10;

    // Dados do resumo com validação
    const summaryData = [
      ['Total de Quotas', (data.summary.totalQuotas || 0).toString()],
      ['Quotas Pagas', (data.summary.paidQuotas || 0).toString()],
      ['Quotas Pendentes', (data.summary.pendingQuotas || 0).toString()],
      ['Quotas em Atraso', (data.summary.overdueQuotas || 0).toString()],
      ['Valor Total', formatCurrencyAO(data.summary.totalAmount || 0)],
      ['Valor Pago', formatCurrencyAO(data.summary.paidAmount || 0)],
      ['Valor Pendente', formatCurrencyAO(data.summary.pendingAmount || 0)],
      ['Valor de Multas', formatCurrencyAO(data.summary.finesAmount || 0)]
    ];

    autoTable(doc, {
      startY: yPosition,
      head: [['Métrica', 'Valor']],
      body: summaryData,
      theme: 'grid',
      headStyles: {
        fillColor: THEME_COLORS.primary,
        textColor: 255,
        fontSize: 10,
        fontStyle: 'bold'
      },
      bodyStyles: {
        fontSize: 9,
        textColor: THEME_COLORS.text
      },
      alternateRowStyles: {
        fillColor: THEME_COLORS.lightGray
      },
      margin: { left: 15, right: 15 },
      didDrawPage: function (data) {
        yPosition = data.cursor.y + 15;
      },
     
    });

    // Detalhes das quotas
    if (data.quotas && data.quotas.length > 0) {
      console.log('📋 Adicionando detalhes das quotas:', data.quotas.length, 'quotas');

      doc.setFontSize(14);
      doc.setTextColor(THEME_COLORS.primary);
      doc.setFont('helvetica', 'bold');
      doc.text('Detalhes das Quotas', 15, yPosition);
      yPosition += 5;

      const quotasTableData = data.quotas.map((quota: any) => [
        quota.apartmentNumber || 'N/A',
        quota.residentName || 'N/A',
        `${quota.month || 0}/${quota.year || 0}`,
        formatCurrencyAO(quota.amount || 0),
        quota.status || 'N/A',
        quota.dueDate ? formatDatePT(quota.dueDate) : 'N/A',
        quota.paymentDate ? formatDatePT(quota.paymentDate) : '-',
        formatCurrencyAO(quota.fine || 0)
      ]);

      autoTable(doc, {
        startY: yPosition,
        head: [['Apt.', 'Morador', 'Mês/Ano', 'Valor', 'Status', 'Vencimento', 'Pagamento', 'Multa']],
        body: quotasTableData,
        theme: 'grid',
        headStyles: {
          fillColor: THEME_COLORS.primary,
          textColor: 255,
          fontSize: 10,
          fontStyle: 'bold',
          halign: 'center'
        },
        bodyStyles: {
          fontSize: 9,
          textColor: THEME_COLORS.text,
          halign: 'center'
        },
        alternateRowStyles: {
          fillColor: THEME_COLORS.lightGray
        },
        margin: { left: 15, right: 15 },
        tableWidth: 'auto',
        columnStyles: {
          0: { cellWidth: 15, halign: 'center' }, // Apt
          1: { cellWidth: 40, halign: 'left' },   // Morador
          2: { cellWidth: 18, halign: 'center' }, // Mês/Ano
          3: { cellWidth: 22, halign: 'right' },  // Valor
          4: { cellWidth: 18, halign: 'center' }, // Status
          5: { cellWidth: 22, halign: 'center' }, // Vencimento
          6: { cellWidth: 22, halign: 'center' }, // Pagamento
          7: { cellWidth: 18, halign: 'right' }   // Multa
        },
        didParseCell: function(data: any) {
          // Colorir coluna Status (índice 4)
          if (data.column.index === 4) {
            const status = data.cell.text[0];
            if (status === 'Pago') {
              data.cell.styles.textColor = [34, 197, 94]; // Verde
              data.cell.styles.fontStyle = 'bold';
            } else if (status === 'Não Pago') {
              data.cell.styles.textColor = [239, 68, 68]; // Vermelho
              data.cell.styles.fontStyle = 'bold';
            }
          }

          // Colorir coluna Multa (índice 7)
          if (data.column.index === 7) {
            const multa = data.cell.text[0];
            if (multa && multa !== '0 Kz' && multa !== '-' && parseFloat(multa.replace(/[^\d,]/g, '').replace(',', '.')) > 0) {
              data.cell.styles.textColor = [249, 115, 22]; // Laranja
              data.cell.styles.fontStyle = 'bold';
            }
          }
        }
      });
    }

    console.log('✅ PDF de quotas gerado com sucesso');

  } catch (error) {
    console.error('❌ Erro ao gerar PDF de quotas:', error);
    throw error;
  }
};

/**
 * Gera PDF para relatório de moradores
 */
const generateResidentsPDF = async (
  doc: jsPDF,
  reportData: any,
  config: PDFConfig
) => {
  try {
    console.log('📊 Gerando PDF de moradores...');

    const data = reportData;
    let yPosition = 55;

    // Resumo
    doc.setFontSize(14);
    doc.setTextColor(THEME_COLORS.primary);
    doc.setFont('helvetica', 'bold');
    doc.text('Resumo', 15, yPosition);
    yPosition += 10;

    // Dados do resumo
    const summaryData = [
      ['Total de Moradores', (data.summary?.totalResidents || 0).toString()],
      ['Moradores Ativos', (data.summary?.activeResidents || 0).toString()],
      ['Moradores Inativos', (data.summary?.inactiveResidents || 0).toString()],
      ['Moradores Isentos', (data.summary?.exemptResidents || 0).toString()]
    ];

    autoTable(doc, {
      startY: yPosition,
      head: [['Métrica', 'Valor']],
      body: summaryData,
      theme: 'grid',
      headStyles: {
        fillColor: THEME_COLORS.primary,
        textColor: 255,
        fontSize: 10,
        fontStyle: 'bold'
      },
      bodyStyles: {
        fontSize: 9,
        textColor: THEME_COLORS.text
      },
      alternateRowStyles: {
        fillColor: THEME_COLORS.lightGray
      },
      margin: { left: 15, right: 15 },
      didDrawPage: function (data) {
        yPosition = data.cursor.y + 15;
      }
    });

    // Lista de moradores
    if (data.residents && data.residents.length > 0) {
      console.log('📋 Adicionando lista de moradores:', data.residents.length, 'moradores');

      doc.setFontSize(14);
      doc.setTextColor(THEME_COLORS.primary);
      doc.setFont('helvetica', 'bold');
      doc.text('Lista de Moradores', 15, yPosition);
      yPosition += 5;

      const residentsTableData = data.residents.map((resident: any) => [
        resident.apartmentNumber || 'N/A',
        resident.name || 'N/A',
        resident.status || 'N/A',
        resident.isExempt ? 'Sim' : 'Não',
        resident.phone || '-',
        resident.email || '-'
      ]);

      autoTable(doc, {
        startY: yPosition,
        head: [['Apt.', 'Nome', 'Status', 'Isento', 'Telefone', 'Email']],
        body: residentsTableData,
        theme: 'grid',
        headStyles: {
          fillColor: THEME_COLORS.primary,
          textColor: 255,
          fontSize: 10,
          fontStyle: 'bold',
          halign: 'center'
        },
        bodyStyles: {
          fontSize: 9,
          textColor: THEME_COLORS.text,
          halign: 'center'
        },
        alternateRowStyles: {
          fillColor: THEME_COLORS.lightGray
        },
        margin: { left: 15, right: 15 },
        tableWidth: 'auto',
        columnStyles: {
          0: { cellWidth: 15, halign: 'center' }, // Apt
          1: { cellWidth: 45, halign: 'center' },   // Nome
          2: { cellWidth: 25, halign: 'center' }, // Status
          3: { cellWidth: 20, halign: 'center' }, // Isento
          4: { cellWidth: 31, halign: 'center' }, // Telefone
          5: { cellWidth: 45, halign: 'center' }    // Email
        }
      });
    }

    console.log('✅ PDF de moradores gerado com sucesso');

  } catch (error) {
    console.error('❌ Erro ao gerar PDF de moradores:', error);
    throw error;
  }
};

/**
 * Gera PDF para relatório financeiro
 */
const generateFinancialPDF = async (
  doc: jsPDF,
  reportData: any,
  config: PDFConfig
) => {
  try {
    console.log('📊 Gerando PDF financeiro...');

    const data = reportData;
    let yPosition = 55;

    // Resumo
    doc.setFontSize(14);
    doc.setTextColor(THEME_COLORS.primary);
    doc.setFont('helvetica', 'bold');
    doc.text('Resumo', 15, yPosition);
    yPosition += 10;

    // Dados do resumo
    const summaryData = [
      ['Total de Entradas', formatCurrencyAO(data.summary?.totalIncome || 0)],
      ['Total de Saídas', formatCurrencyAO(data.summary?.totalExpense || 0)],
      ['Saldo', formatCurrencyAO(data.summary?.balance || 0)],
      ['Número de Entradas', (data.summary?.incomeCount || 0).toString()],
      ['Número de Saídas', (data.summary?.expenseCount || 0).toString()]
    ];

    autoTable(doc, {
      startY: yPosition,
      head: [['Métrica', 'Valor']],
      body: summaryData,
      theme: 'grid',
      headStyles: {
        fillColor: THEME_COLORS.primary,
        textColor: 255,
        fontSize: 10,
        fontStyle: 'bold'
      },
      bodyStyles: {
        fontSize: 9,
        textColor: THEME_COLORS.text
      },
      alternateRowStyles: {
        fillColor: THEME_COLORS.lightGray
      },
      margin: { left: 15, right: 15 },
      didDrawPage: function (data) {
        yPosition = data.cursor.y + 15;
      }
    });

    // Verificar se deve agrupar por mês
    if (data.monthlyBreakdown && data.monthlyBreakdown.length > 0) {
      console.log('📊 Adicionando agrupamento mensal:', data.monthlyBreakdown.length, 'meses');

      doc.setFontSize(14);
      doc.setTextColor(THEME_COLORS.primary);
      doc.setFont('helvetica', 'bold');
      doc.text('Resumo Mensal', 15, yPosition);
      yPosition += 5;

      const monthlyTableData = data.monthlyBreakdown.map((month: any) => [
        month.month || 'N/A',
        formatCurrencyAO(month.income || 0),
        formatCurrencyAO(month.expense || 0),
        formatCurrencyAO(month.balance || 0)
      ]);

      autoTable(doc, {
        startY: yPosition,
        head: [['Mês', 'Entradas', 'Saídas', 'Saldo']],
        body: monthlyTableData,
        theme: 'grid',
        headStyles: {
          fillColor: THEME_COLORS.primary,
          textColor: 255,
          fontSize: 10,
          fontStyle: 'bold',
          halign: 'center'
        },
        bodyStyles: {
          fontSize: 9,
          textColor: THEME_COLORS.text,
          halign: 'center'
        },
        alternateRowStyles: {
          fillColor: THEME_COLORS.lightGray
        },
        margin: { left: 15, right: 15 },
        tableWidth: 'auto',
        columnStyles: {
          0: { cellWidth: 50, halign: 'center' }, // Mês
          1: { cellWidth: 35, halign: 'right' },  // Entradas
          2: { cellWidth: 35, halign: 'right' },  // Saídas
          3: { cellWidth: 35, halign: 'right' }   // Saldo
        },
        didParseCell: function(data: any) {
          // Colorir coluna Saldo (índice 3)
          if (data.column.index === 3) {
            const saldoText = data.cell.text[0];
            const saldoValue = parseFloat(saldoText.replace(/[^\d,-]/g, '').replace(',', '.'));
            if (saldoValue > 0) {
              data.cell.styles.textColor = [34, 197, 94]; // Verde para saldo positivo
              data.cell.styles.fontStyle = 'bold';
            } else if (saldoValue < 0) {
              data.cell.styles.textColor = [239, 68, 68]; // Vermelho para saldo negativo
              data.cell.styles.fontStyle = 'bold';
            }
          }
        },
        didDrawPage: function (data) {
          yPosition = data.cursor.y + 15;
        }
      });
    }

    // Detalhes das transações
    if (data.transactions && data.transactions.length > 0) {
      console.log('📋 Adicionando detalhes das transações:', data.transactions.length, 'transações');

      doc.setFontSize(14);
      doc.setTextColor(THEME_COLORS.primary);
      doc.setFont('helvetica', 'bold');
      doc.text('Detalhes das Transações', 15, yPosition);
      yPosition += 5;

      const transactionsTableData = data.transactions.map((transaction: any) => [
        transaction.date ? formatDatePT(transaction.date) : 'N/A',
        transaction.type === 'entrada' ? 'Entrada' : 'Saída',
        transaction.category || 'N/A',
        transaction.description || 'N/A',
        formatCurrencyAO(transaction.amount || 0),
        transaction.responsible || '-'
      ]);

      autoTable(doc, {
        startY: yPosition,
        head: [['Data', 'Tipo', 'Categoria', 'Descrição', 'Valor']],
        body: transactionsTableData,
        theme: 'grid',
        headStyles: {
          fillColor: THEME_COLORS.primary,
          textColor: 255,
          fontSize: 10,
          fontStyle: 'bold',
          halign: 'center'
        },
        bodyStyles: {
          fontSize: 9,
          textColor: THEME_COLORS.text,
          halign: 'center'
        },
        alternateRowStyles: {
          fillColor: THEME_COLORS.lightGray
        },
        margin: { left: 15, right: 15 },
        tableWidth: 'auto',
        columnStyles: {
          0: { cellWidth: 25, halign: 'center' }, // Data
          1: { cellWidth: 23, halign: 'center' }, // Tipo
          2: { cellWidth: 28, halign: 'center' }, // Categoria
          3: { cellWidth: 74, halign: 'center' },   // Descrição
          4: { cellWidth: 30, halign: 'center' },  // Valor

        }
      });
    }

    console.log('✅ PDF financeiro gerado com sucesso');

  } catch (error) {
    console.error('❌ Erro ao gerar PDF financeiro:', error);
    throw error;
  }
};

/**
 * Gera PDF para relatório da comissão
 */
const generateCommitteePDF = async (
  doc: jsPDF,
  reportData: any,
  config: PDFConfig
) => {
  try {
    console.log('📊 Gerando PDF da comissão...');

    const data = reportData;
    let yPosition = 55;

    // Resumo
    doc.setFontSize(14);
    doc.setTextColor(THEME_COLORS.primary);
    doc.setFont('helvetica', 'bold');
    doc.text('Resumo', 15, yPosition);
    yPosition += 10;

    // Dados do resumo
    const summaryData = [
      ['Total de Membros', (data.summary?.totalMembers || 0).toString()],
      ['Membros Ativos', (data.summary?.activeMembers || 0).toString()]
    ];

    autoTable(doc, {
      startY: yPosition,
      head: [['Métrica', 'Valor']],
      body: summaryData,
      theme: 'grid',
      headStyles: {
        fillColor: THEME_COLORS.primary,
        textColor: 255,
        fontSize: 10,
        fontStyle: 'bold'
      },
      bodyStyles: {
        fontSize: 9,
        textColor: THEME_COLORS.text
      },
      alternateRowStyles: {
        fillColor: THEME_COLORS.lightGray
      },
      margin: { left: 15, right: 15 },
      didDrawPage: function (data) {
        yPosition = data.cursor.y + 15;
      }
    });

    // Lista de membros
    if (data.members && data.members.length > 0) {
      console.log('📋 Adicionando lista de membros:', data.members.length, 'membros');

      doc.setFontSize(14);
      doc.setTextColor(THEME_COLORS.primary);
      doc.setFont('helvetica', 'bold');
      doc.text('Membros da Comissão', 15, yPosition);
      yPosition += 5;

      const membersTableData = data.members.map((member: any) => [
        member.name || 'N/A',
        member.position || 'N/A',
        member.status || 'N/A',
        member.startDate ? formatDatePT(member.startDate) : 'N/A',
        member.endDate ? formatDatePT(member.endDate) : 'Em exercício',
        member.phone || '-',
        member.email || '-'
      ]);

      autoTable(doc, {
        startY: yPosition,
        head: [['Nome', 'Cargo', 'Status', 'Início', 'Fim', 'Telefone', 'Email']],
        body: membersTableData,
        theme: 'grid',
        headStyles: {
          fillColor: THEME_COLORS.primary,
          textColor: 255,
          fontSize: 10,
          fontStyle: 'bold',
          halign: 'center'
        },
        bodyStyles: {
          fontSize: 9,
          textColor: THEME_COLORS.text,
          halign: 'center'
        },
        alternateRowStyles: {
          fillColor: THEME_COLORS.lightGray
        },
        margin: { left: 15, right: 15 },
        tableWidth: 'auto',
        columnStyles: {
          0: { cellWidth: 30, halign: 'center' },   // Nome
          1: { cellWidth: 25, halign: 'center' }, // Cargo
          2: { cellWidth: 18, halign: 'center' }, // Status
          3: { cellWidth: 22, halign: 'center' }, // Início
          4: { cellWidth: 22, halign: 'center' }, // Fim
          5: { cellWidth: 25, halign: 'center' }, // Telefone
          6: { cellWidth: 38, halign: 'center' }    // Email
        }
      });
    }

    console.log('✅ PDF da comissão gerado com sucesso');

  } catch (error) {
    console.error('❌ Erro ao gerar PDF da comissão:', error);
    throw error;
  }
};

/**
 * Gera PDF para relatório de multas
 */
const generateFinesPDF = async (
  doc: jsPDF,
  reportData: any,
  config: PDFConfig
) => {
  try {
    console.log('📊 Gerando PDF de multas...');

    const data = reportData;
    let yPosition = 55;

    // Validar se os dados necessários existem
    if (!data.summary) {
      throw new Error('Dados de resumo não encontrados');
    }

    // Resumo
    doc.setFontSize(14);
    doc.setTextColor(THEME_COLORS.primary);
    doc.setFont('helvetica', 'bold');
    doc.text('Resumo', 15, yPosition);
    yPosition += 10;

    // Dados do resumo com validação
    const summaryData = [
      ['Total de Multas', (data.summary.totalFines || 0).toString()],
      ['Multas Pagas', (data.summary.paidFines || 0).toString()],
      ['Multas Pendentes', (data.summary.pendingFines || 0).toString()],
      ['Valor Total', formatCurrencyAO(data.summary.totalAmount || 0)],
      ['Valor Pago', formatCurrencyAO(data.summary.paidAmount || 0)],
      ['Valor Pendente', formatCurrencyAO(data.summary.pendingAmount || 0)]
    ];

    autoTable(doc, {
      startY: yPosition,
      head: [['Métrica', 'Valor']],
      body: summaryData,
      theme: 'grid',
      headStyles: {
        fillColor: '#F97316', // Cor laranja para multas
        textColor: 255,
        fontSize: 10,
        fontStyle: 'bold'
      },
      bodyStyles: {
        fontSize: 9,
        textColor: THEME_COLORS.text
      },
      alternateRowStyles: {
        fillColor: THEME_COLORS.lightGray
      },
      margin: { left: 15, right: 15 },
      didDrawPage: function (data) {
        yPosition = data.cursor.y + 15;
      }
    });

    // Detalhes das multas
    if (data.fines && data.fines.length > 0) {
      console.log('📋 Adicionando detalhes das multas:', data.fines.length, 'multas');

      doc.setFontSize(14);
      doc.setTextColor(THEME_COLORS.primary);
      doc.setFont('helvetica', 'bold');
      doc.text('Detalhes das Multas', 15, yPosition);
      yPosition += 5;

      // Os dados já vêm ordenados do serviço, não precisamos ordenar novamente
      console.log('📋 Dados de multas já ordenados pelo serviço. Total:', data.fines.length);
      console.log('📋 Apartamentos (primeiros 10):', data.fines.map(f => f.apartmentNumber).slice(0, 10));

      const finesTableData = data.fines.map((fine: any) => [
        fine.apartmentNumber || 'N/A',
        fine.residentName || 'N/A',
        `${fine.month || 0}/${fine.year || 0}`,
        formatCurrencyAO(fine.fineAmount || 0),
        fine.status || 'N/A',
        fine.appliedDate ? formatDatePT(fine.appliedDate) : 'N/A',
        fine.paidDate ? formatDatePT(fine.paidDate) : '-',
        fine.reason || 'N/A'
      ]);

      autoTable(doc, {
        startY: yPosition,
        head: [['Apt.', 'Morador', 'Mês/Ano', 'Valor', 'Status', 'Aplicada', 'Paga', 'Motivo']],
        body: finesTableData,
        theme: 'grid',
        headStyles: {
          fillColor: '#F97316', // Cor laranja para multas
          textColor: 255,
          fontSize: 10,
          fontStyle: 'bold',
          halign: 'center'
        },
        bodyStyles: {
          fontSize: 9,
          textColor: THEME_COLORS.text,
          halign: 'center'
        },
        alternateRowStyles: {
          fillColor: THEME_COLORS.lightGray
        },
        margin: { left: 15, right: 15 },
        tableWidth: 'auto',
        columnStyles: {
          0: { cellWidth: 15, halign: 'center' }, // Apt
          1: { cellWidth: 35, halign: 'left' },   // Morador
          2: { cellWidth: 18, halign: 'center' }, // Mês/Ano
          3: { cellWidth: 20, halign: 'right' },  // Valor
          4: { cellWidth: 18, halign: 'center' }, // Status
          5: { cellWidth: 20, halign: 'center' }, // Aplicada
          6: { cellWidth: 20, halign: 'center' }, // Paga
          7: { cellWidth: 34, halign: 'left' }    // Motivo
        },
        didParseCell: function(data: any) {
          // Colorir coluna Status (índice 4)
          if (data.column.index === 4) {
            const status = data.cell.text[0];
            if (status === 'Pago') {
              data.cell.styles.textColor = [34, 197, 94]; // Verde
              data.cell.styles.fontStyle = 'bold';
            } else if (status === 'Não Pago') {
              data.cell.styles.textColor = [239, 68, 68]; // Vermelho
              data.cell.styles.fontStyle = 'bold';
            }
          }

          // Colorir coluna Valor (índice 3) - sempre laranja para multas
          if (data.column.index === 3) {
            data.cell.styles.textColor = [249, 115, 22]; // Laranja
            data.cell.styles.fontStyle = 'bold';
          }
        }
      });
    }

    console.log('✅ PDF de multas gerado com sucesso');

  } catch (error) {
    console.error('❌ Erro ao gerar PDF de multas:', error);
    throw error;
  }
};

/**
 * Função principal para gerar PDF
 */
export const generatePDF = async (
  reportData: ReportData,
  customConfig?: Partial<PDFConfig>
): Promise<string> => {
  try {
    console.log('🔄 Iniciando geração de PDF:', reportData.type);

    // Validar dados de entrada
    if (!reportData || !reportData.type) {
      throw new Error('Dados do relatório inválidos');
    }

    const config = { ...DEFAULT_PDF_CONFIG, ...customConfig };

    // Criar novo documento PDF
    const doc = new jsPDF({
      orientation: config.orientation || 'portrait',
      unit: 'mm',
      format: 'a4'
    });

    console.log('📄 Documento PDF criado');

    // Adicionar marca d'água se configurado
    if (config.includeWatermark) {
      try {
        await addWatermark(doc);
        console.log('🎨 Marca d\'água adicionada');
      } catch (error) {
        console.warn('⚠️ Erro ao adicionar marca d\'água:', error);
      }
    }

    // Adicionar cabeçalho
    try {
      await addHeader(doc, reportData, config);
      console.log('📋 Cabeçalho adicionado');
    } catch (error) {
      console.warn('⚠️ Erro ao adicionar cabeçalho:', error);
    }

    // Gerar conteúdo específico do relatório
    console.log('📊 Gerando conteúdo do relatório:', reportData.type);

    const reportType = reportData.type;
    
    if (reportType === 'quotas') {
      await generateQuotasPDF(doc, reportData, config);
    } else if (reportType === 'residents') {
      await generateResidentsPDF(doc, reportData, config);
    } else if (reportType === 'financial') {
      await generateFinancialPDF(doc, reportData, config);
    } else if (reportType === 'committee') {
      await generateCommitteePDF(doc, reportData, config);
    } else if (reportType === 'fines') {
      await generateFinesPDF(doc, reportData, config);
    } else {
      throw new Error(`Tipo de relatório não suportado: ${reportType}`);
    }

    // Adicionar rodapé
    try {
      addFooter(doc, config);
      console.log('📄 Rodapé adicionado');
    } catch (error) {
      console.warn('⚠️ Erro ao adicionar rodapé:', error);
    }

    // Gerar nome do arquivo
    const timestamp = new Date().toISOString().slice(0, 10);
    const filename = `relatorio_${reportData.type}_${timestamp}.pdf`;

    console.log('💾 Salvando PDF:', filename);

    // Salvar PDF
    doc.save(filename);

    console.log('✅ PDF gerado com sucesso:', filename);
    return filename;

  } catch (error) {
    console.error('❌ Erro ao gerar PDF:', error);

    // Melhor tratamento de erro
    if (error instanceof Error) {
      throw new Error(`Erro ao gerar PDF: ${error.message}`);
    }
    throw new Error('Erro desconhecido ao gerar PDF');
  }
};
