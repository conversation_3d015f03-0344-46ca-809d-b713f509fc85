// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://qrcegsdhbwgjtkebqjok.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFyY2Vnc2RoYndnanRrZWJxam9rIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDI5MTIzNDMsImV4cCI6MjA1ODQ4ODM0M30.r94EU08552kYer4TIGBxlg4h-dkqXt6_tOb1wzSm_lc";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);