import React from 'react';
import { cn } from '@/lib/utils';
import { useSidebar } from '@/contexts/SidebarContext';
import ResidentSidebar from './ResidentSidebar';
import Header from '../Header';

interface ResidentLayoutProps {
  children: React.ReactNode;
  title: string;
  subtitle?: string;
}

const ResidentLayout: React.FC<ResidentLayoutProps> = ({ 
  children, 
  title, 
  subtitle 
}) => {
  const { collapsed, isMobile } = useSidebar();

  return (
    <div className="flex h-screen bg-gray-50">
      <ResidentSidebar />

      <div className={cn(
        "flex-1 flex flex-col transition-all duration-300 overflow-hidden",
        isMobile ? "ml-0" : (collapsed ? "ml-12" : "ml-64")
      )}>
        <div className="flex-1 overflow-y-auto pb-10">
          <div className="page-container">
            <Header
              title={title}
              subtitle={subtitle}
            />
            {children}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResidentLayout;
