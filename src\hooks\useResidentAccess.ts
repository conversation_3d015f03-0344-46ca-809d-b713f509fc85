import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  getResidentsWithAccessStatus,
  createResidentUser,
  revokeResidentAccess,
  type CreateResidentUserParams,
  type ResidentWithAccess
} from '@/utils/resident-access-helpers';
import { toast } from 'sonner';

export const useResidentAccess = () => {
  const queryClient = useQueryClient();

  const { data: residentsWithAccess = [], isLoading, error, refetch } = useQuery({
    queryKey: ['residents-with-access'],
    queryFn: getResidentsWithAccessStatus,
    staleTime: 5 * 60 * 1000, // 5 minutos
  });

  const createUserMutation = useMutation({
    mutationFn: async (params: CreateResidentUserParams) => {
      console.log('🎯 [useResidentAccess] createUserMutation called with params:', params);
      const result = await createResidentUser(params);
      console.log('✅ [useResidentAccess] createResidentUser returned:', result);

      // SOLUÇÃO TEMPORÁRIA: Se a Edge Function não retornou a senha, adicionar ela aqui
      if (result && result.success && !result.password && params.password) {
        console.log('🔧 [useResidentAccess] Adding password to result (Edge Function fix)');
        result.password = params.password;
      }

      return result;
    },
    onSuccess: (data) => {
      console.log('🎉 [useResidentAccess] createUserMutation onSuccess called with data:', data);

      if (data && data.success === true && data.user_id) {
        console.log('✅ [useResidentAccess] Success validation passed, data includes:', {
          success: data.success,
          user_id: data.user_id,
          email: data.email,
          name: data.name,
          apartment_id: data.apartment_id,
          hasPassword: !!data.password,
          passwordLength: data.password?.length || 0
        });

        queryClient.invalidateQueries({ queryKey: ['residents-with-access'] });
        queryClient.invalidateQueries({ queryKey: ['moradores'] });
        toast.success(`Acesso criado para ${data.name}!`);
      } else {
        console.error('❌ [useResidentAccess] Invalid server response:', data);
        toast.error('Erro: Resposta inválida do servidor');
      }
    },
    onError: (error: Error) => {
      console.error('❌ [useResidentAccess] createUserMutation onError called:', error);
      const errorMessage = error.message || 'Erro ao criar acesso';
      toast.error(errorMessage);
    }
  });

  const revokeAccessMutation = useMutation({
    mutationFn: ({ userId, apartmentId }: { userId: string; apartmentId: string }) =>
      revokeResidentAccess(userId, apartmentId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['residents-with-access'] });
      queryClient.invalidateQueries({ queryKey: ['moradores'] });
      toast.success('Acesso removido com sucesso!');
    },
    onError: (error: Error) => {
      console.error('Error revoking access:', error);
      toast.error(`Erro ao remover acesso: ${error.message}`);
    }
  });



  const createUser = (params: CreateResidentUserParams) => {
    // Reset previous errors
    createUserMutation.reset();

    // Validação básica antes de chamar a mutação
    if (!params.email || !params.password || !params.name || !params.apartment_id) {
      toast.error('Erro: Parâmetros inválidos fornecidos');
      return;
    }

    createUserMutation.mutate(params);
  };



  return {
    residentsWithAccess,
    isLoading,
    error,
    refetch,
    createUser,
    isCreatingUser: createUserMutation.isPending,
    createUserError: createUserMutation.error,
    createUserStatus: createUserMutation.status,
    revokeAccess: revokeAccessMutation.mutate,
    isRevokingAccess: revokeAccessMutation.isPending,
  };
};
