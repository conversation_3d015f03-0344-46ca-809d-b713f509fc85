
import React, { createContext, useContext } from 'react';
import { AuthContextType, Profile } from '@/types/auth';
import { useAuthState } from '@/hooks/useAuthState';
import { signInWithCredentials, signUpWithCredentials, signOutUser } from '@/services/auth-service';
import { updateProfileData } from '@/services/profile-service';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user, profile, session, loading, setProfile } = useAuthState();

  // Update user profile
  const updateProfile = async (updates: Partial<Profile>) => {
    if (!user) {
      throw new Error('No user logged in');
    }

    const updatedProfile = await updateProfileData(user.id, updates);
    setProfile(updatedProfile);
  };

  const signIn = async (email: string, password: string) => {
    const result = await signInWithCredentials(email, password);
    
    // If there's an error, throw it so the UI can handle it
    if (result.error) {
      throw result.error;
    }
    
    return result;
  };

  const signUp = async (email: string, password: string, name: string) => {
    const result = await signUpWithCredentials(email, password, name);
    
    // If there's an error, throw it so the UI can handle it
    if (result.error) {
      throw result.error;
    }
    
    return result;
  };

  const signOut = async () => {
    // Clear local state
    setProfile(null);
    
    await signOutUser();
  };

  const isAuthenticated = !!user && !!session;
  const isFirstLogin = profile?.first_login ?? false;

  const value: AuthContextType = {
    user,
    profile,
    session,
    isAuthenticated,
    isFirstLogin,
    loading,
    signIn,
    signUp,
    signOut,
    updateProfile,
  };

  console.log('🔍 AuthProvider state:', {
    user: user?.email,
    profileRole: profile?.role,
    isAuthenticated,
    loading
  });

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
