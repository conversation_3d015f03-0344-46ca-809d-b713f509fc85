
import { User, Session } from '@supabase/supabase-js';

export interface Profile {
  id: string;
  name?: string;
  email?: string;
  role?: 'admin' | 'resident' | 'convidado';
  apartment_id?: string;
  photo?: string;
  banned?: boolean;
  first_login?: boolean;
  last_login?: string;
  created_at?: string;
  updated_at?: string;
}

export interface AuthContextType {
  user: User | null;
  profile: Profile | null;
  session: Session | null;
  isAuthenticated: boolean;
  isFirstLogin: boolean;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<{ error: any }>;
  signUp: (email: string, password: string, name: string) => Promise<{ error: any }>;
  signOut: () => Promise<void>;
  updateProfile: (updates: Partial<Profile>) => Promise<void>;
}
