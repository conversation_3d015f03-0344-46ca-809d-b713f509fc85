
import React from 'react';
import { ChevronRightIcon, HomeIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { DocumentFolder } from '@/types';

interface BreadcrumbsProps {
  folders: DocumentFolder[];
  onNavigate: (folderId: string | null) => void;
}

const Breadcrumbs: React.FC<BreadcrumbsProps> = ({ folders, onNavigate }) => {
  return (
    <nav className="flex" aria-label="Breadcrumb">
      <ol className="inline-flex items-center space-x-1 md:space-x-2">
        <li className="inline-flex items-center">
          <Button
            variant="ghost"
            size="sm"
            className="flex items-center text-gray-600 hover:text-primary"
            onClick={() => onNavigate(null)}
          >
            <HomeIcon className="w-4 h-4 mr-2" />
            <span className="hidden sm:inline">Início</span>
          </Button>
        </li>
        
        {folders.map((folder, index) => (
          <li key={folder.id} className="flex items-center">
            <ChevronRightIcon className="w-4 h-4 text-gray-400" />
            <Button
              variant="ghost"
              size="sm"
              className={`ml-1 flex items-center ${
                index === folders.length - 1 ? 'font-medium text-primary' : 'text-gray-600 hover:text-primary'
              }`}
              onClick={() => onNavigate(folder.id)}
            >
              <span className="max-w-[150px] truncate">{folder.name}</span>
            </Button>
          </li>
        ))}
      </ol>
    </nav>
  );
};

export default Breadcrumbs;
