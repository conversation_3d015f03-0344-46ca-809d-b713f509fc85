
-- Criar tabela para comunicados
CREATE TABLE public.comunicados (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  titulo TEXT NOT NULL,
  conteudo TEXT NOT NULL,
  tipo TEXT NOT NULL DEFAULT 'geral',
  prioridade TEXT NOT NULL DEFAULT 'media',
  autor TEXT NOT NULL,
  data_publicacao TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  fixado BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Criar tabela para rastrear comunicados lidos pelos moradores
CREATE TABLE public.comunicados_lidos (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  comunicado_id UUID NOT NULL REFERENCES public.comunicados(id) ON DELETE CASCADE,
  usuario_id UUID NOT NULL,
  data_leitura TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  UNIQUE(comunicado_id, usuario_id)
);

-- Criar tabela para documentos dos moradores
CREATE TABLE public.documentos_moradores (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  nome TEXT NOT NULL,
  tipo TEXT NOT NULL,
  categoria TEXT NOT NULL,
  tamanho TEXT NOT NULL,
  url_arquivo TEXT NOT NULL,
  apartment_id TEXT NOT NULL,
  data_upload TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  uploaded_by UUID NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Habilitar RLS
ALTER TABLE public.comunicados ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.comunicados_lidos ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.documentos_moradores ENABLE ROW LEVEL SECURITY;

-- Políticas RLS para comunicados (todos podem ler)
CREATE POLICY "Todos podem ver comunicados" 
  ON public.comunicados 
  FOR SELECT 
  USING (true);

-- Políticas RLS para comunicados_lidos (usuário pode ver/criar apenas os seus)
CREATE POLICY "Usuários podem ver seus próprios registros de leitura" 
  ON public.comunicados_lidos 
  FOR SELECT 
  USING (auth.uid() = usuario_id);

CREATE POLICY "Usuários podem criar seus próprios registros de leitura" 
  ON public.comunicados_lidos 
  FOR INSERT 
  WITH CHECK (auth.uid() = usuario_id);

-- Políticas RLS para documentos (moradores veem apenas do seu apartamento)
CREATE POLICY "Moradores podem ver documentos do seu apartamento" 
  ON public.documentos_moradores 
  FOR SELECT 
  USING (
    apartment_id = (
      SELECT p.apartment_id 
      FROM public.profiles p 
      WHERE p.id = auth.uid()
    )
  );

-- Admins podem ver todos os documentos
CREATE POLICY "Admins podem ver todos os documentos" 
  ON public.documentos_moradores 
  FOR SELECT 
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles p 
      WHERE p.id = auth.uid() AND p.role = 'admin'
    )
  );
