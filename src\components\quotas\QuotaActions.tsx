
import { useState } from 'react';
import { toast } from 'sonner';
import { Quota } from '@/types';
import { useConfirmDialog } from '@/hooks/useConfirmDialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface QuotaActionsProps {
  quotas?: Quota[];
  setQuotaPaid: (params: { id: string; paymentDate: string }) => void;
  regularizeQuotaFine: (id: string) => void;
  addQuota: (quota: Omit<Quota, 'id' | 'created_at' | 'updated_at'>) => void;
  editQuota: (params: { id: string; data: Partial<Quota> }) => void;
  removeQuota: (id: string) => void;
  onCloseModal?: () => void;
}

export const useQuotaActions = ({
  quotas,
  setQuotaPaid,
  regularizeQuotaFine,
  addQuota,
  editQuota,
  removeQuota,
  onCloseModal
}: QuotaActionsProps) => {
  const [paymentModal, setPaymentModal] = useState<{
    isOpen: boolean;
    quota: Quota | null;
  }>({ isOpen: false, quota: null });

  const [fineRegularizationModal, setFineRegularizationModal] = useState<{
    isOpen: boolean;
    quota: Quota | null;
  }>({ isOpen: false, quota: null });

  const { dialogState, showConfirmDialog, hideConfirmDialog, handleConfirm } = useConfirmDialog();



  const handleOpenPaymentModal = (quota: Quota) => {
    setPaymentModal({
      isOpen: true,
      quota: quota
    });
  };

  const handleConfirmPayment = (paymentDate: string) => {
    if (paymentModal.quota) {
      console.log('💳 Liquidando quota:', paymentModal.quota.id, 'na data:', paymentDate);
      setQuotaPaid({ id: paymentModal.quota.id, paymentDate });
      setPaymentModal({ isOpen: false, quota: null });
    }
  };

  const handleOpenFineRegularizationModal = (quota: Quota) => {
    setFineRegularizationModal({
      isOpen: true,
      quota: quota
    });
  };

  const handleConfirmFineRegularization = (regularizationDate: string) => {
    if (fineRegularizationModal.quota) {
      console.log('🎯 Regularizando multa:', fineRegularizationModal.quota.id, 'na data:', regularizationDate);
      regularizeQuotaFine(fineRegularizationModal.quota.id);
      setFineRegularizationModal({ isOpen: false, quota: null });
    }
  };

  const handleSaveQuota = async (quotaData: Partial<Quota>) => {
    console.log('💾 Salvando dados da quota:', quotaData);

    try {
      if (quotaData.id) {
        if (typeof quotaData.id !== 'string' || !quotaData.id.trim()) {
          console.error('❌ ID de quota inválido:', quotaData.id);
          toast.error('ID de quota inválido. Não foi possível atualizar.');
          return;
        }

        const { id, ...dataToUpdate } = quotaData;
        console.log(`🔄 Atualizando quota ID ${id}:`, dataToUpdate);
        editQuota({ id, data: dataToUpdate });
      } else {
        const requiredFields = ['morador_id', 'mes', 'ano', 'valor', 'data_vencimento'];
        const missingFields = requiredFields.filter(field => !quotaData[field as keyof Quota]);

        if (missingFields.length > 0) {
          console.error('❌ Campos obrigatórios ausentes:', missingFields);
          toast.error(`Campos obrigatórios ausentes: ${missingFields.join(', ')}`);
          return;
        }

        const { id, ...quotaWithoutId } = quotaData;
        const newQuotaData = {
          ...quotaWithoutId,
          status: quotaData.status || 'Não Pago',
        };

        console.log('➕ Adicionando nova quota:', newQuotaData);
        addQuota(newQuotaData as Omit<Quota, 'id' | 'created_at' | 'updated_at'>);
      }
    } catch (err) {
      console.error('💥 Erro ao salvar quota:', err);
      toast.error('Ocorreu um erro ao salvar a quota.');
    }
  };

  return {
    paymentModal,
    fineRegularizationModal,
    handleOpenPaymentModal,
    handleConfirmPayment,
    handleOpenFineRegularizationModal,
    handleConfirmFineRegularization,
    handleSaveQuota,
    setPaymentModal,
    setFineRegularizationModal,
    confirmDialog: (
      <AlertDialog open={dialogState.isOpen} onOpenChange={hideConfirmDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{dialogState.title}</AlertDialogTitle>
            <AlertDialogDescription>
              {dialogState.description}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={hideConfirmDialog}>
              {dialogState.cancelText}
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirm}
              className="bg-orange-600 hover:bg-orange-700"
            >
              {dialogState.confirmText}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    )
  };
};
