
import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Morador } from '@/types';
import { ExemptionManager } from '@/components/resident/ExemptionManager';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';

const editResidentSchema = z.object({
  nome: z.string().min(1, "Nome é obrigatório"),
  apartamento: z.string().min(1, "Apartamento é obrigatório"),
  email: z.string().email("Email inválido").optional().or(z.literal('')),
  telefone: z.string().optional().or(z.literal('')),
  isento_quotas: z.boolean().optional(),
  motivo_isencao: z.string().optional()
}).refine((data) => {
  if (data.isento_quotas && !data.motivo_isencao?.trim()) {
    return false;
  }
  return true;
}, {
  message: "Motivo da isenção é obrigatório quando morador está isento",
  path: ["motivo_isencao"]
});

type EditResidentFormValues = z.infer<typeof editResidentSchema>;

interface EditResidentModalProps {
  isOpen: boolean;
  onClose: () => void;
  resident: Morador | null;
  onSave: (data: Partial<Morador>) => void;
}

const EditResidentModal: React.FC<EditResidentModalProps> = ({
  isOpen,
  onClose,
  resident,
  onSave
}) => {
  const form = useForm<EditResidentFormValues>({
    resolver: zodResolver(editResidentSchema),
    defaultValues: {
      nome: '',
      apartamento: '',
      email: '',
      telefone: '',
      isento_quotas: false,
      motivo_isencao: ''
    }
  });

  useEffect(() => {
    if (resident) {
      form.reset({
        nome: resident.nome || '',
        apartamento: resident.apartamento || '',
        email: resident.email || '',
        telefone: resident.telefone || '',
        isento_quotas: resident.isento_quotas || false,
        motivo_isencao: resident.motivo_isencao || ''
      });
    }
  }, [resident, form]);

  const handleSubmit = (data: EditResidentFormValues) => {
    const submitData: Partial<Morador> = {
      nome: data.nome,
      apartamento: data.apartamento,
      email: data.email || '',
      telefone: data.telefone || '',
      isento_quotas: data.isento_quotas || false,
      motivo_isencao: data.isento_quotas ? data.motivo_isencao || '' : null
    };

    onSave(submitData);
  };

  const handleClose = () => {
    form.reset();
    onClose();
  };

  const isExempt = form.watch('isento_quotas');

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md max-h-[90vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle>Editar Morador</DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto pr-2 min-h-0">
          <Form {...form}>
            <form id="edit-resident-form" onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4 pb-4">
              <FormField
                control={form.control}
                name="nome"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Nome <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Nome completo" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="apartamento"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Apartamento <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Ex: A101" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>E-mail</FormLabel>
                    <FormControl>
                      <Input {...field} type="email" placeholder="<EMAIL>" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="telefone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Telefone</FormLabel>
                    <FormControl>
                      <Input {...field} type="tel" placeholder="+244 123 456 789" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="border-t pt-4">
                <ExemptionManager
                  isExempt={isExempt || false}
                  exemptionReason={form.watch('motivo_isencao') || ''}
                  onExemptChange={(exempt) => {
                    form.setValue('isento_quotas', exempt);
                    if (!exempt) {
                      form.setValue('motivo_isencao', '');
                    }
                  }}
                  onReasonChange={(reason) => form.setValue('motivo_isencao', reason)}
                />
              </div>
            </form>
          </Form>
        </div>

        <DialogFooter className="flex-shrink-0 pt-4 border-t bg-white">
          <Button type="button" variant="outline" onClick={handleClose}>
            Cancelar
          </Button>
          <Button type="submit" form="edit-resident-form">
            Salvar Alterações
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default EditResidentModal;
