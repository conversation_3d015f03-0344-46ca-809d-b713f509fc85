
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';

export interface Communication {
  id: string;
  titulo: string;
  conteudo: string;
  tipo: string;
  prioridade: string;
  autor: string;
  data_publicacao: string;
  fixado: boolean;
  lida: boolean;
}

export const useResidentCommunications = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  const communications = useQuery({
    queryKey: ['resident-communications', user?.id],
    queryFn: async (): Promise<Communication[]> => {
      // Buscar todos os comunicados
      const { data: comunicados, error } = await supabase
        .from('comunicados')
        .select(`
          *,
          comunicados_lidos!left(
            data_leitura
          )
        `)
        .order('fixado', { ascending: false })
        .order('data_publicacao', { ascending: false });

      if (error) throw error;

      // Processar dados para incluir status de leitura
      return comunicados?.map(com => ({
        id: com.id,
        titulo: com.titulo,
        conteudo: com.conteudo,
        tipo: com.tipo,
        prioridade: com.prioridade,
        autor: com.autor,
        data_publicacao: com.data_publicacao,
        fixado: com.fixado,
        lida: Array.isArray(com.comunicados_lidos) && com.comunicados_lidos.length > 0
      })) || [];
    },
    enabled: !!user?.id
  });

  const markAsRead = useMutation({
    mutationFn: async (comunicadoId: string) => {
      if (!user?.id) throw new Error('Usuário não autenticado');

      const { error } = await supabase
        .from('comunicados_lidos')
        .upsert({
          comunicado_id: comunicadoId,
          usuario_id: user.id
        });

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['resident-communications'] });
    }
  });

  return {
    communications: communications.data || [],
    isLoading: communications.isLoading,
    error: communications.error,
    markAsRead
  };
};
