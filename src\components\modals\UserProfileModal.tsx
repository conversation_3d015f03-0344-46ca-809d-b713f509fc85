import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { User, X, Upload } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { useMutation } from '@tanstack/react-query';
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';

const profileSchema = z.object({
  name: z.string().min(1, "Nome é obrigatório"),
  newPassword: z.string().optional(),
  confirmPassword: z.string().optional(),
}).refine(data => {
  if (data.newPassword && data.newPassword !== data.confirmPassword) {
    return false;
  }
  return true;
}, {
  message: "As senhas não correspondem",
  path: ["confirmPassword"],
});

type ProfileFormValues = z.infer<typeof profileSchema>;

interface UserProfileModalProps {
  isOpen: boolean;
  onClose: () => void;
  onProfileUpdate: () => void;
}

const UserProfileModal: React.FC<UserProfileModalProps> = ({
  isOpen,
  onClose,
  onProfileUpdate
}) => {
  const { user } = useAuth();
  const [userPhoto, setUserPhoto] = useState<string | null>(null);
  const [oldPhotoPath, setOldPhotoPath] = useState<string | null>(null);
  const [photoFile, setPhotoFile] = useState<File | null>(null);
  const [loading, setLoading] = useState(false);

  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      name: '',
      newPassword: '',
      confirmPassword: '',
    }
  });

  useEffect(() => {
    if (isOpen && user?.id) {
      fetchUserProfile();
    }
  }, [isOpen, user?.id]);

  const fetchUserProfile = async () => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('name, photo')
        .eq('id', user?.id)
        .single();

      if (error) {
        console.error('Error fetching user profile:', error);
        return;
      }

      if (data) {
        form.reset({
          name: data.name || '',
          newPassword: '',
          confirmPassword: '',
        });
        setUserPhoto(data.photo || null);
        if (data.photo) {
          const photoPath = getPhotoPathFromUrl(data.photo);
          setOldPhotoPath(photoPath);
        } else {
          setOldPhotoPath(null);
        }
      }
    } catch (error) {
      console.error('Error fetching user profile:', error);
    }
  };

  const getPhotoPathFromUrl = (url: string): string | null => {
    try {
      const urlObj = new URL(url);
      const pathParts = urlObj.pathname.split('/');
      return pathParts[pathParts.length - 1];
    } catch (e) {
      console.error('Error parsing photo URL:', e);
      return null;
    }
  };

  const deleteOldPhoto = async (path: string): Promise<boolean> => {
    try {
      const { error } = await supabase.storage
        .from('avatars')
        .remove([path]);

      if (error) {
        console.error('Error deleting old avatar:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in deleteOldPhoto:', error);
      return false;
    }
  };

  const uploadAvatar = async (userId: string, file: File): Promise<string | null> => {
    try {
      const fileExt = file.name.split('.').pop();
      const fileName = `${userId}-${Date.now()}.${fileExt}`;

      const { data, error } = await supabase.storage
        .from('avatars')
        .upload(fileName, file, {
          upsert: true
        });

      if (error) throw error;

      const { data: { publicUrl } } = supabase.storage
        .from('avatars')
        .getPublicUrl(fileName);

      return publicUrl;
    } catch (error) {
      console.error('Error uploading avatar:', error);
      toast.error('Erro ao fazer upload da imagem: ' + (error as Error).message);
      return null;
    }
  };

  const updateProfileMutation = useMutation({
    mutationFn: async (values: ProfileFormValues & { photoUrl?: string | null }) => {
      if (!user?.id) throw new Error('User ID is missing');

      const { error: profileError } = await supabase
        .from('profiles')
        .update({
          name: values.name,
          photo: values.photoUrl,
          updated_at: new Date().toISOString()
        })
        .eq('id', user.id);

      if (profileError) throw profileError;

      if (values.newPassword) {
        const { error: passwordError } = await supabase.auth.updateUser({
          password: values.newPassword
        });

        if (passwordError) throw passwordError;
      }

      if (values.photoUrl && oldPhotoPath) {
        await deleteOldPhoto(oldPhotoPath);
      }

      return true;
    },
    onSuccess: () => {
      toast.success('Perfil atualizado com sucesso');
      onProfileUpdate();
      onClose();
      form.reset();
      setPhotoFile(null);
    },
    onError: (error: any) => {
      console.error('Error updating profile:', error);
      toast.error(`Erro ao atualizar perfil: ${error.message}`);
    }
  });

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setPhotoFile(file);

      const reader = new FileReader();
      reader.onload = (e) => {
        if (e.target?.result) {
          setUserPhoto(e.target.result as string);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const handleRemovePhoto = async () => {
    setUserPhoto(null);
    setPhotoFile(null);

    if (oldPhotoPath) {
      try {
        const deleted = await deleteOldPhoto(oldPhotoPath);
        if (deleted) {
          setOldPhotoPath(null);
          toast.success('Foto removida com sucesso');
        }
      } catch (error) {
        console.error('Error removing photo:', error);
        toast.error('Erro ao remover foto');
      }
    }
  };

  const handleSubmit = async (data: ProfileFormValues) => {
    setLoading(true);
    try {
      let photoUrl = userPhoto;
      if (photoFile && user?.id) {
        photoUrl = await uploadAvatar(user.id, photoFile);
      } else if (userPhoto === null && oldPhotoPath) {
        photoUrl = null;
      }

      await updateProfileMutation.mutateAsync({
        ...data,
        photoUrl
      });
    } catch (error) {
      console.error('Error in form submission:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Editar Perfil</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            <div className="flex flex-col items-center space-y-4">
              <Avatar className="h-24 w-24 border-2 border-gray-200">
                <AvatarImage src={userPhoto || undefined} alt={form.getValues().name} />
                <AvatarFallback>
                  <User className="h-10 w-10 text-gray-400" />
                </AvatarFallback>
              </Avatar>

              <div className="flex space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  className="text-xs"
                  onClick={() => document.getElementById('photo-upload')?.click()}
                >
                  <Upload className="mr-1 h-4 w-4" />
                  Alterar Foto
                </Button>
                {userPhoto && (
                  <Button
                    type="button"
                    variant="outline"
                    className="text-xs text-red-500 hover:text-red-700"
                    onClick={handleRemovePhoto}
                  >
                    <X className="mr-1 h-4 w-4" />
                    Remover
                  </Button>
                )}
                <input
                  id="photo-upload"
                  type="file"
                  accept="image/*"
                  className="hidden"
                  onChange={handleFileChange}
                />
              </div>
            </div>

            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nome <span className="text-red-500">*</span></FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="Seu nome" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="newPassword"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nova Senha (opcional)</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      type="password"
                      placeholder="Deixe em branco para manter a senha atual"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {form.watch('newPassword') && (
              <FormField
                control={form.control}
                name="confirmPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Confirmar Nova Senha</FormLabel>
                    <FormControl>
                      <Input {...field} type="password" placeholder="Confirme a nova senha" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <DialogFooter>
              <Button type="button" variant="outline" onClick={onClose} disabled={loading}>
                Cancelar
              </Button>
              <Button type="submit" disabled={loading || updateProfileMutation.isPending}>
                {loading || updateProfileMutation.isPending ? 'Salvando...' : 'Salvar Alterações'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default UserProfileModal;
