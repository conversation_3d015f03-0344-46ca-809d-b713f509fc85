
// Função aprimorada para normalizar caracteres acentuados
export const normalizeTransactionType = (tipo: string): string => {
  console.log('🔄 normalizeTransactionType: INPUT - Raw tipo value:', JSON.stringify(tipo));
  console.log('🔄 normalizeTransactionType: INPUT - Tipo type:', typeof tipo);
  
  if (!tipo || typeof tipo !== 'string') {
    console.error('❌ normalizeTransactionType: Invalid input type:', typeof tipo, 'value:', tipo);
    return 'entrada'; // Default fallback
  }
  
  // Primeiro passo: remover acentos e normalizar
  const step1 = tipo
    .normalize("NFD")
    .replace(/[\u0300-\u036f]/g, "")
    .toLowerCase()
    .trim();
  
  console.log('🔄 normalizeTransactionType: STEP1 - After normalize/trim:', JSON.stringify(step1));
  
  // Segundo passo: mapear para valores válidos
  let normalized;
  if (step1 === 'saida' || step1 === 'saída' || step1 === 'saida' || step1.includes('saida') || step1.includes('saída')) {
    normalized = 'saida';
  } else if (step1 === 'entrada' || step1.includes('entrada')) {
    normalized = 'entrada';
  } else {
    console.warn('⚠️ normalizeTransactionType: Unexpected tipo value, defaulting to entrada. Input was:', tipo, 'normalized to:', step1);
    normalized = 'entrada'; // Default fallback
  }
  
  console.log('🔄 normalizeTransactionType: OUTPUT - Final normalized value:', JSON.stringify(normalized));
  
  return normalized;
};

export const formatNumberWithDots = (value: number): string => {
  return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
};

export const processFormValue = (valor: string | number): number => {
  console.log('💰 processFormValue: INPUT - Raw valor:', JSON.stringify(valor), 'type:', typeof valor);
  
  if (typeof valor === 'number') {
    console.log('💰 processFormValue: Already a number, returning:', valor);
    return valor;
  }
  
  if (typeof valor === 'string') {
    const processed = parseFloat(valor.replace(/\./g, '').replace(',', '.'));
    console.log('💰 processFormValue: Processed string to number:', processed);
    return processed;
  }
  
  console.error('❌ processFormValue: Invalid valor type:', typeof valor, 'value:', valor);
  return 0; // Fallback
};

export const validateTipo = (tipo: string): boolean => {
  const isValid = tipo === 'entrada' || tipo === 'saida';
  console.log('✅ validateTipo: Validating tipo:', JSON.stringify(tipo), 'isValid:', isValid);
  return isValid;
};

// Nova função para validação final antes da inserção
export const validateCompleteFluxoCaixa = (data: any): { isValid: boolean; errors: string[] } => {
  console.log('🔍 validateCompleteFluxoCaixa: Starting validation for:', JSON.stringify(data, null, 2));
  
  const errors: string[] = [];
  
  // Validar tipo
  if (!data.tipo) {
    errors.push('Tipo é obrigatório');
  } else if (!validateTipo(data.tipo)) {
    errors.push(`Tipo inválido: ${data.tipo}. Deve ser 'entrada' ou 'saida'`);
  }
  
  // Validar outros campos obrigatórios
  if (!data.descricao || data.descricao.trim() === '') {
    errors.push('Descrição é obrigatória');
  }
  
  if (!data.categoria || data.categoria.trim() === '') {
    errors.push('Categoria é obrigatória');
  }
  
  if (!data.valor || data.valor <= 0) {
    errors.push('Valor deve ser maior que zero');
  }
  
  if (!data.data) {
    errors.push('Data é obrigatória');
  }
  
  const isValid = errors.length === 0;
  console.log('🔍 validateCompleteFluxoCaixa: Validation result:', { isValid, errors });
  
  return { isValid, errors };
};
