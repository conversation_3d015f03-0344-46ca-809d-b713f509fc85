import { supabase } from '@/integrations/supabase/client';
import { FluxoCaixa, MembroComissao, Morador, Notificacao, Quota, Configuracao } from '@/types';
import { calculateFineAmount, calculateSituacaoMulta, MultaConfig, correctFineOnPayment } from './quota-calculations';

/**
 * Ensures the avatars storage bucket exists
 */
export async function ensureAvatarsBucket() {
  try {
    const { data: buckets } = await supabase.storage.listBuckets();
    
    if (!buckets?.find(bucket => bucket.name === 'avatars')) {
      const { error } = await supabase.storage.createBucket('avatars', {
        public: true,
        fileSizeLimit: 1024 * 1024 * 2 // 2MB
      });
      
      if (error) {
        console.error('Error creating avatars bucket:', error);
      }
    }
    
    // Also ensure documents bucket exists
    if (!buckets?.find(bucket => bucket.name === 'documentos_condominio')) {
      const { error } = await supabase.storage.createBucket('documentos_condominio', {
        public: true,
        fileSizeLimit: 1024 * 1024 * 10 // 10MB
      });
      
      if (error) {
        console.error('Error creating documents bucket:', error);
      }
    }
  } catch (error) {
    console.error('Error checking storage buckets:', error);
  }
}

/**
 * Ensures the comprovativos storage bucket exists
 */
export async function ensureComprovativosBucket() {
  try {
    const { data: buckets } = await supabase.storage.listBuckets();
    
    if (!buckets?.find(bucket => bucket.name === 'comprovativos')) {
      const { error } = await supabase.storage.createBucket('comprovativos', {
        public: true,
        fileSizeLimit: 1024 * 1024 * 5 // 5MB
      });
      
      if (error) {
        console.error('Error creating comprovativos bucket:', error);
      }
    }
  } catch (error) {
    console.error('Error checking comprovativos bucket:', error);
    return false;
  }
}

/**
 * Ensures the documentos_condominio storage bucket exists
 */
export async function ensureDocumentosBucket() {
  try {
    const { data: buckets } = await supabase.storage.listBuckets();
    
    if (!buckets?.find(bucket => bucket.name === 'documentos_condominio')) {
      const { error } = await supabase.storage.createBucket('documentos_condominio', {
        public: true,
        fileSizeLimit: 1024 * 1024 * 10 // 10MB
      });
      
      if (error) {
        console.error('Error creating documentos_condominio bucket:', error);
      }
    }
  } catch (error) {
    console.error('Error checking documentos_condominio bucket:', error);
    return false;
  }
}

/**
 * Uploads a user avatar image to Supabase Storage
 */
export const uploadAvatar = async (userId: string, file: File): Promise<string | null> => {
  try {
    await ensureAvatarsBucket();
    
    const fileExt = file.name.split('.').pop();
    const fileName = `${userId}-${Date.now()}.${fileExt}`;
    
    const { data, error } = await supabase.storage
      .from('avatars')
      .upload(fileName, file, {
        upsert: true
      });
      
    if (error) throw error;
    
    // Get public URL
    const { data: { publicUrl } } = supabase.storage
      .from('avatars')
      .getPublicUrl(fileName);
    
    return publicUrl;
  } catch (error) {
    console.error('Error uploading avatar:', error);
    return null;
  }
};

/**
 * Uploads a payment receipt file to Supabase Storage
 */
export const uploadComprovativo = async (quotaId: string, file: File): Promise<string | null> => {
  try {
    await ensureComprovativosBucket();
    
    const fileExt = file.name.split('.').pop();
    const fileName = `quota_${quotaId}_${Date.now()}.${fileExt}`;
    
    const { data, error } = await supabase.storage
      .from('comprovativos')
      .upload(fileName, file, {
        upsert: true
      });
      
    if (error) throw error;
    
    // Get public URL
    const { data: { publicUrl } } = supabase.storage
      .from('comprovativos')
      .getPublicUrl(fileName);
    
    return publicUrl;
  } catch (error) {
    console.error('Error uploading comprovativo:', error);
    return null;
  }
};

/**
 * Uploads a document file to Supabase Storage
 */
export const uploadDocumento = async (path: string, file: File): Promise<string | null> => {
  try {
    await ensureDocumentosBucket();
    
    const { data, error } = await supabase.storage
      .from('documentos_condominio')
      .upload(path, file, {
        upsert: true
      });
      
    if (error) throw error;
    
    // Get public URL
    const { data: { publicUrl } } = supabase.storage
      .from('documentos_condominio')
      .getPublicUrl(path);
    
    return publicUrl;
  } catch (error) {
    console.error('Error uploading document:', error);
    return null;
  }
};

// MORADORES (RESIDENTS)

export const getMoradores = async (): Promise<Morador[]> => {
  try {
    const { data, error } = await supabase
      .from('moradores')
      .select('*')
      .order('apartamento', { ascending: true });
      
    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error fetching moradores:', error);
    return [];
  }
};

export const getMoradorById = async (id: string): Promise<Morador | null> => {
  try {
    const { data, error } = await supabase
      .from('moradores')
      .select('*')
      .eq('id', id)
      .single();
      
    if (error) throw error;
    return data;
  } catch (error) {
    console.error(`Error fetching morador with id ${id}:`, error);
    return null;
  }
};

export const createMorador = async (morador: Omit<Morador, 'id' | 'created_at' | 'updated_at'>): Promise<Morador | null> => {
  try {
    const { data, error } = await supabase
      .from('moradores')
      .insert([morador])
      .select()
      .single();
      
    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error creating morador:', error);
    return null;
  }
};

export const updateMorador = async (id: string, morador: Partial<Morador>): Promise<Morador | null> => {
  try {
    const { data, error } = await supabase
      .from('moradores')
      .update(morador)
      .eq('id', id)
      .select()
      .single();
      
    if (error) throw error;
    return data;
  } catch (error) {
    console.error(`Error updating morador with id ${id}:`, error);
    return null;
  }
};

export const deleteMorador = async (id: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('moradores')
      .delete()
      .eq('id', id);
      
    if (error) throw error;
    return true;
  } catch (error) {
    console.error(`Error deleting morador with id ${id}:`, error);
    return false;
  }
};

// MEMBROS COMISSÃO (COMMITTEE MEMBERS)

export const getMembrosComissao = async (): Promise<MembroComissao[]> => {
  try {
    const { data, error } = await supabase
      .from('membros_comissao')
      .select('*')
      .order('nome', { ascending: true });
      
    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error fetching membros_comissao:', error);
    return [];
  }
};

export const getMembroComissaoById = async (id: string): Promise<MembroComissao | null> => {
  try {
    const { data, error } = await supabase
      .from('membros_comissao')
      .select('*')
      .eq('id', id)
      .single();
      
    if (error) throw error;
    return data;
  } catch (error) {
    console.error(`Error fetching membro_comissao with id ${id}:`, error);
    return null;
  }
};

export const createMembroComissao = async (membro: Omit<MembroComissao, 'id' | 'created_at' | 'updated_at'>): Promise<MembroComissao | null> => {
  try {
    const { data, error } = await supabase
      .from('membros_comissao')
      .insert([membro])
      .select()
      .single();
      
    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error creating membro_comissao:', error);
    return null;
  }
};

export const updateMembroComissao = async (id: string, membro: Partial<MembroComissao>): Promise<MembroComissao | null> => {
  try {
    const { data, error } = await supabase
      .from('membros_comissao')
      .update(membro)
      .eq('id', id)
      .select()
      .single();
      
    if (error) throw error;
    return data;
  } catch (error) {
    console.error(`Error updating membro_comissao with id ${id}:`, error);
    return null;
  }
};

export const deleteMembroComissao = async (id: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('membros_comissao')
      .delete()
      .eq('id', id);
      
    if (error) throw error;
    return true;
  } catch (error) {
    console.error(`Error deleting membro_comissao with id ${id}:`, error);
    return false;
  }
};

// CONFIGURAÇÕES - Funções para buscar configurações de multas
export const getMultaConfig = async (): Promise<{ valorMulta: number, diasAtrasoMulta: number }> => {
  try {
    const valorMulta = await getConfiguracaoByNome('valor_multa_atraso');
    const diasAtrasoMulta = await getConfiguracaoByNome('dias_atraso_multa');
    
    return {
      valorMulta: valorMulta ? parseInt(valorMulta) : 1000,
      diasAtrasoMulta: diasAtrasoMulta ? parseInt(diasAtrasoMulta) : 0
    };
  } catch (error) {
    console.error('Error fetching multa config:', error);
    return { valorMulta: 1000, diasAtrasoMulta: 0 };
  }
};

// QUOTAS - Funções corrigidas com cálculo adequado de multas

export const getQuotas = async (): Promise<Quota[]> => {
  try {
    console.log('🔍 Buscando quotas...');
    
    const { data, error } = await supabase
      .from('quotas')
      .select('*, moradores(nome, apartamento)')
      .order('ano', { ascending: false })
      .order('mes', { ascending: false });
      
    if (error) {
      console.error('❌ Erro ao buscar quotas:', error);
      throw error;
    }
    
    console.log(`✅ ${data?.length || 0} quotas encontradas`);
    
    return (data || []).map(item => {
      const { moradores, ...quotaData } = item;

      return {
        ...quotaData,
        ...(moradores ? { moradores: moradores as unknown as Morador } : {}),
        multa: quotaData.multa || 0,
        numero_multas: quotaData.numero_multas || 0,
        // CORRIGIDO: Manter a situação exatamente como está salva no banco
        situacao: quotaData.situacao || 'Não Regularizada'
      } as Quota;
    });
  } catch (error) {
    console.error('💥 Erro crítico ao buscar quotas:', error);
    return [];
  }
};

export const createQuota = async (quota: Omit<Quota, 'id' | 'created_at' | 'updated_at'>): Promise<Quota | null> => {
  try {
    console.log('💰 Criando nova quota:', quota);
    
    if (!quota.morador_id || !quota.mes || !quota.ano || !quota.valor) {
      throw new Error('Campos obrigatórios ausentes');
    }

    // Buscar configurações de multa
    const config = await getMultaConfig();
    const multaConfig: MultaConfig = {
      valorMulta: config.valorMulta,
      diasAtrasoMulta: config.diasAtrasoMulta
    };

    // CORRIGIDO: Usar funções atualizadas de cálculo
    const multaCalculada = calculateFineAmount(
      quota.data_pagamento,
      quota.data_vencimento,
      multaConfig
    );

    const situacaoCalculada = calculateSituacaoMulta(
      multaCalculada,
      quota.data_pagamento,
      quota.data_vencimento
    );

    console.log(`💸 Multa calculada: ${multaCalculada} Kz, Situação: ${situacaoCalculada}`);
    
    const dbFields: any = {
      morador_id: quota.morador_id,
      mes: quota.mes,
      ano: quota.ano,
      valor: quota.valor,
      status: quota.status || 'Não Pago',
      data_vencimento: quota.data_vencimento,
      data_pagamento: quota.data_pagamento === '' ? null : quota.data_pagamento,
      multa: multaCalculada,
      numero_multas: multaCalculada > 0 ? 1 : 0,
      situacao: situacaoCalculada,
      comprovativo: quota.comprovativo || null,
      fluxo_caixa_id: quota.fluxo_caixa_id || null // Incluir referência ao fluxo de caixa
    };
    
    console.log('📝 Dados para inserção:', dbFields);
    
    const { data, error } = await supabase
      .from('quotas')
      .insert([dbFields])
      .select()
      .single();
      
    if (error) {
      console.error('❌ Erro ao criar quota:', error);
      throw error;
    }
    
    console.log('✅ Quota criada com sucesso:', data);
    
    return {
      ...data,
      multa: data.multa || 0,
      numero_multas: data.numero_multas || 0,
      situacao: data.situacao || 'Não Regularizada'
    } as Quota;
  } catch (error) {
    console.error('💥 Erro crítico ao criar quota:', error);
    return null;
  }
};

export const updateQuota = async (id: string, quota: Partial<Quota>): Promise<Quota | null> => {
  try {
    console.log(`🔄 Atualizando quota ${id}:`, quota);
    
    // Buscar configurações de multa para cálculos
    const config = await getMultaConfig();
    const multaConfig: MultaConfig = {
      valorMulta: config.valorMulta,
      diasAtrasoMulta: config.diasAtrasoMulta
    };

    // CORRIGIDO: Usar funções atualizadas de cálculo
    let multaCalculada = quota.multa || 0;
    let situacaoCalculada = quota.situacao;

    // Recalcular multa e situação se necessário
    if (quota.data_vencimento) {
      multaCalculada = calculateFineAmount(
        quota.data_pagamento || null,
        quota.data_vencimento,
        multaConfig
      );

      situacaoCalculada = calculateSituacaoMulta(
        multaCalculada,
        quota.data_pagamento || null,
        quota.data_vencimento
      );

      console.log(`💸 Multa recalculada: ${multaCalculada} Kz, Situação: ${situacaoCalculada}`);
    }
    
    const dbFields: any = {
      morador_id: quota.morador_id,
      mes: quota.mes,
      ano: quota.ano,
      valor: quota.valor,
      status: quota.status,
      data_vencimento: quota.data_vencimento,
      data_pagamento: quota.data_pagamento === '' ? null : quota.data_pagamento,
      multa: multaCalculada,
      numero_multas: quota.numero_multas,
      situacao: situacaoCalculada,
      comprovativo: quota.comprovativo,
      fluxo_caixa_id: quota.fluxo_caixa_id // Incluir referência ao fluxo de caixa
    };
    
    // Remove campos undefined
    Object.keys(dbFields).forEach(key => {
      if (dbFields[key] === undefined) {
        delete dbFields[key];
      }
    });
    
    console.log('📝 Dados para atualização:', dbFields);
    
    const { data, error } = await supabase
      .from('quotas')
      .update(dbFields)
      .eq('id', id)
      .select()
      .single();
      
    if (error) {
      console.error(`❌ Erro ao atualizar quota ${id}:`, error);
      throw error;
    }
    
    console.log('✅ Quota atualizada com sucesso:', data);
    
    return {
      ...data,
      multa: data.multa || 0,
      numero_multas: data.numero_multas || 0,
      situacao: data.situacao || 'Não Regularizada'
    } as Quota;
  } catch (error) {
    console.error(`💥 Erro crítico ao atualizar quota ${id}:`, error);
    return null;
  }
};

/**
 * Marca quota como paga e recalcula multa baseada na data de pagamento
 * CORRIGIDO: Agora recalcula multa automaticamente baseada na data de pagamento vs data de vencimento
 */
export const markQuotaAsPaid = async (id: string, paymentDate: string): Promise<Quota | null> => {
  try {
    console.log(`💳 Marcando quota ${id} como paga em ${paymentDate}`);

    // Primeiro, buscar a quota atual para obter data de vencimento
    const { data: currentQuota, error: fetchError } = await supabase
      .from('quotas')
      .select('data_vencimento')
      .eq('id', id)
      .single();

    if (fetchError || !currentQuota) {
      console.error(`❌ Erro ao buscar quota ${id}:`, fetchError);
      throw fetchError || new Error('Quota não encontrada');
    }

    // Buscar configurações de multa
    const config = await getMultaConfig();
    const multaConfig: MultaConfig = {
      valorMulta: config.valorMulta,
      diasAtrasoMulta: config.diasAtrasoMulta
    };

    // Recalcular multa baseada na data de pagamento vs data de vencimento
    const { newFineAmount } = correctFineOnPayment(
      paymentDate,
      currentQuota.data_vencimento,
      multaConfig
    );

    // Calcular nova situação baseada na multa recalculada
    const novaSituacao = calculateSituacaoMulta(
      newFineAmount,
      paymentDate,
      currentQuota.data_vencimento
    );

    console.log(`🔧 Recalculando multa: ${newFineAmount} Kz, Situação: ${novaSituacao}`);

    // Atualizar quota com status, data de pagamento, multa recalculada e situação
    const { data, error } = await supabase
      .from('quotas')
      .update({
        status: 'Pago',
        data_pagamento: paymentDate,
        multa: newFineAmount,
        situacao: novaSituacao
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error(`❌ Erro ao marcar quota ${id} como paga:`, error);
      throw error;
    }

    console.log(`✅ Quota ${id} marcada como paga em ${paymentDate} com multa recalculada: ${newFineAmount} Kz`);
    return data as Quota;
  } catch (error) {
    console.error(`💥 Erro crítico ao marcar quota ${id} como paga:`, error);
    return null;
  }
};

/**
 * Regulariza multa (apenas altera situacao)
 */
export const regularizeFine = async (id: string): Promise<Quota | null> => {
  try {
    console.log(`🎯 Regularizando multa da quota ${id}`);
    
    // Buscar quota atual
    const { data: quotaAtual, error: fetchError } = await supabase
      .from('quotas')
      .select('*')
      .eq('id', id)
      .single();
    
    if (fetchError || !quotaAtual) {
      console.error('❌ Erro ao buscar quota atual:', fetchError);
      throw fetchError || new Error('Quota não encontrada');
    }
    
    // Verificar se quota está paga
    if (quotaAtual.status !== 'Pago') {
      throw new Error('Não é possível regularizar multa de quota não paga');
    }
    
    const { data, error } = await supabase
      .from('quotas')
      .update({
        situacao: 'Regularizada'
      })
      .eq('id', id)
      .select()
      .single();
      
    if (error) {
      console.error(`❌ Erro ao regularizar multa da quota ${id}:`, error);
      throw error;
    }
    
    console.log('✅ Multa regularizada:', data);
    return data as Quota;
  } catch (error) {
    console.error(`💥 Erro crítico ao regularizar multa da quota ${id}:`, error);
    return null;
  }
};

export const deleteQuota = async (id: string): Promise<boolean> => {
  try {
    console.log(`🗑️ Iniciando exclusão da quota ${id}`);

    // Primeiro, verificar se a quota existe e obter informações completas
    const { data: quotaExistente, error: fetchError } = await supabase
      .from('quotas')
      .select('*, moradores(nome)')
      .eq('id', id)
      .single();

    if (fetchError) {
      console.error(`❌ Erro ao verificar quota ${id}:`, fetchError);
      throw fetchError;
    }

    if (!quotaExistente) {
      console.warn(`⚠️ Quota ${id} não encontrada`);
      return false;
    }

    console.log(`📋 Quota encontrada para exclusão:`, quotaExistente);

    // Verificar se a quota tem lançamento associado e se foi paga
    if (quotaExistente.fluxo_caixa_id && quotaExistente.status === 'Pago') {
      console.log(`💰 Quota paga com lançamento associado: ${quotaExistente.fluxo_caixa_id}`);

      // Verificar quantas quotas do mesmo mês estão associadas ao mesmo lançamento
      const { data: quotasDoMes, error: quotasError } = await supabase
        .from('quotas')
        .select('id, valor, status')
        .eq('fluxo_caixa_id', quotaExistente.fluxo_caixa_id)
        .eq('mes', quotaExistente.mes)
        .eq('ano', quotaExistente.ano);

      if (quotasError) {
        console.error('❌ Erro ao buscar quotas do mês:', quotasError);
        throw quotasError;
      }

      const quotasPagas = quotasDoMes?.filter(q => q.status === 'Pago') || [];
      console.log(`📊 Quotas pagas no mês: ${quotasPagas.length}`);

      if (quotasPagas.length === 1) {
        // Esta é a única quota paga do mês - excluir também o lançamento
        console.log('🗑️ Única quota paga do mês - excluindo lançamento associado');

        const { error: deleteLancamentoError } = await supabase
          .from('fluxo_caixa')
          .delete()
          .eq('id', quotaExistente.fluxo_caixa_id);

        if (deleteLancamentoError) {
          console.error('❌ Erro ao excluir lançamento:', deleteLancamentoError);
          throw deleteLancamentoError;
        }

        console.log('✅ Lançamento excluído com sucesso');
      } else if (quotasPagas.length > 1) {
        // Existem outras quotas pagas - apenas decrementar o valor do lançamento
        console.log('📉 Decrementando valor do lançamento');

        const { data: lancamento, error: lancamentoError } = await supabase
          .from('fluxo_caixa')
          .select('valor')
          .eq('id', quotaExistente.fluxo_caixa_id)
          .single();

        if (lancamentoError || !lancamento) {
          console.error('❌ Erro ao buscar lançamento:', lancamentoError);
          throw lancamentoError;
        }

        const novoValor = Math.max(0, lancamento.valor - quotaExistente.valor);

        const { error: updateError } = await supabase
          .from('fluxo_caixa')
          .update({ valor: novoValor })
          .eq('id', quotaExistente.fluxo_caixa_id);

        if (updateError) {
          console.error('❌ Erro ao atualizar valor do lançamento:', updateError);
          throw updateError;
        }

        console.log(`✅ Valor do lançamento atualizado de ${lancamento.valor} para ${novoValor}`);
      }
    }

    // Proceder com a exclusão da quota
    const { error: deleteError } = await supabase
      .from('quotas')
      .delete()
      .eq('id', id);

    if (deleteError) {
      console.error(`❌ Erro ao excluir quota ${id}:`, deleteError);
      throw deleteError;
    }

    console.log(`✅ Quota ${id} excluída com sucesso`);
    return true;
  } catch (error) {
    console.error(`💥 Erro crítico ao excluir quota ${id}:`, error);
    return false;
  }
};



// FLUXO CAIXA (CASH FLOW)

export const getFluxoCaixa = async (): Promise<FluxoCaixa[]> => {
  try {
    console.log('🔄 getFluxoCaixa: Starting query...');
    
    const { data, error } = await supabase
      .from('fluxo_caixa')
      .select('*')
      .order('data', { ascending: false });
      
    if (error) {
      console.error('❌ getFluxoCaixa: Query error:', error);
      throw error;
    }
    
    console.log('📊 getFluxoCaixa: Raw query result:', data?.length || 0, 'records');
    console.log('📊 getFluxoCaixa: Data breakdown by type:', {
      entradas: data?.filter(item => item.tipo === 'entrada').length || 0,
      saidas: data?.filter(item => item.tipo === 'saida').length || 0
    });
    
    // Type casting to ensure the 'tipo' field is of the correct type
    const processedData = (data || []).map(item => {
      console.log('🔄 getFluxoCaixa: Processing item:', item.id, 'tipo:', item.tipo);
      return {
        ...item,
        tipo: item.tipo as 'entrada' | 'saida'
      };
    });
    
    const breakdown = {
      entradas: processedData.filter(item => item.tipo === 'entrada').length,
      saidas: processedData.filter(item => item.tipo === 'saida').length
    };
    
    console.log('📊 getFluxoCaixa: Data breakdown:', breakdown);
    
    return processedData;
  } catch (error) {
    console.error('💥 getFluxoCaixa: Error fetching fluxo_caixa:', error);
    return [];
  }
};

export const createLancamento = async (lancamento: Omit<FluxoCaixa, 'id' | 'created_at' | 'updated_at'>): Promise<FluxoCaixa | null> => {
  try {
    console.log('💰 createLancamento: Starting with data:', lancamento);
    console.log('💰 createLancamento: Tipo received:', lancamento.tipo);
    console.log('💰 createLancamento: Tipo type:', typeof lancamento.tipo);
    console.log('💰 createLancamento: Full object:', JSON.stringify(lancamento, null, 2));
    
    // Validate required fields
    if (!lancamento.tipo) {
      console.error('❌ createLancamento: Missing tipo field');
      throw new Error('Tipo é obrigatório');
    }
    
    if (lancamento.tipo !== 'entrada' && lancamento.tipo !== 'saida') {
      console.error('❌ createLancamento: Invalid tipo value:', lancamento.tipo);
      throw new Error('Tipo deve ser "entrada" ou "saida"');
    }
    
    if (!lancamento.descricao) {
      console.error('❌ createLancamento: Missing descricao field');
      throw new Error('Descrição é obrigatória');
    }
    
    if (!lancamento.categoria) {
      console.error('❌ createLancamento: Missing categoria field');
      throw new Error('Categoria é obrigatória');
    }
    
    if (!lancamento.valor || lancamento.valor <= 0) {
      console.error('❌ createLancamento: Invalid valor field:', lancamento.valor);
      throw new Error('Valor deve ser maior que zero');
    }
    
    if (!lancamento.data) {
      console.error('❌ createLancamento: Missing data field');
      throw new Error('Data é obrigatória');
    }
    
    console.log('✅ createLancamento: All validations passed');
    
    const insertData = {
      tipo: lancamento.tipo,
      categoria: lancamento.categoria,
      descricao: lancamento.descricao,
      valor: lancamento.valor,
      data: lancamento.data,
      responsavel_id: lancamento.responsavel_id || null,
      anexo: lancamento.anexo || null
    };
    
    console.log('📝 createLancamento: Data being inserted:', insertData);
    console.log('📝 createLancamento: Insert tipo:', insertData.tipo);
    
    const { data, error } = await supabase
      .from('fluxo_caixa')
      .insert([insertData])
      .select()
      .single();
      
    if (error) {
      console.error('❌ createLancamento: Insert error:', error);
      console.error('❌ createLancamento: Error code:', error.code);
      console.error('❌ createLancamento: Error message:', error.message);
      throw error;
    }
    
    console.log('🎉 createLancamento: Insert successful!');
    console.log('📊 createLancamento: Returned data:', data);
    console.log('📊 createLancamento: Returned tipo:', data?.tipo);
    
    // Type casting to ensure the 'tipo' field is of the correct type
    const result = {
      ...data,
      tipo: data.tipo as 'entrada' | 'saida'
    };
    
    console.log('✅ createLancamento: Final result:', result);
    
    return result;
  } catch (error) {
    console.error('💥 createLancamento: Critical error creating lancamento:', error);
    console.error('💥 createLancamento: Error stack:', error instanceof Error ? error.stack : 'No stack');
    return null;
  }
};

export const updateLancamento = async (id: string, lancamento: Partial<FluxoCaixa>): Promise<FluxoCaixa | null> => {
  try {
    console.log(`Updating lancamento ${id} with data:`, lancamento);
    
    // Extract only fields that exist in the database table
    const dbFields: any = {
      tipo: lancamento.tipo,
      categoria: lancamento.categoria,
      descricao: lancamento.descricao,
      valor: lancamento.valor,
      data: lancamento.data,
      responsavel_id: lancamento.responsavel_id || null,
      anexo: lancamento.anexo || null
    };
    
    // Remove any undefined fields
    Object.keys(dbFields).forEach(key => {
      if (dbFields[key] === undefined) {
        delete dbFields[key];
      }
    });
    
    const { data, error } = await supabase
      .from('fluxo_caixa')
      .update(dbFields)
      .eq('id', id)
      .select()
      .single();
      
    if (error) {
      console.error(`Error updating lancamento with id ${id}:`, error);
      throw error;
    }
    
    console.log('Lancamento updated successfully:', data);
    
    // Type casting to ensure the 'tipo' field is of the correct type
    return {
      ...data,
      tipo: data.tipo as 'entrada' | 'saida'
    };
  } catch (error) {
    console.error(`Error updating lancamento with id ${id}:`, error);
    return null;
  }
};

export const deleteLancamento = async (id: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('fluxo_caixa')
      .delete()
      .eq('id', id);
      
    if (error) throw error;
    return true;
  } catch (error) {
    console.error(`Error deleting lancamento with id ${id}:`, error);
    return false;
  }
};

export const calcularSaldo = async (): Promise<number> => {
  try {
    const { data, error } = await supabase
      .rpc('calcular_saldo');
      
    if (error) throw error;
    return data || 0;
  } catch (error) {
    console.error('Error calculating saldo:', error);
    return 0;
  }
};

// NOTIFICAÇÕES (NOTIFICATIONS)

export const getNotificacoes = async (): Promise<Notificacao[]> => {
  try {
    const { data, error } = await supabase
      .from('notificacoes')
      .select('*')
      .order('created_at', { ascending: false });
      
    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error fetching notificacoes:', error);
    return [];
  }
};

export const getNotificacoesUsuario = async (usuarioId: string): Promise<Notificacao[]> => {
  try {
    const { data, error } = await supabase
      .from('notificacoes')
      .select('*')
      .eq('usuario_id', usuarioId)
      .order('created_at', { ascending: false });
      
    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error(`Error fetching notificacoes for usuario ${usuarioId}:`, error);
    return [];
  }
};

export const marcarNotificacaoComoLida = async (id: string): Promise<Notificacao | null> => {
  try {
    const { data, error } = await supabase
      .from('notificacoes')
      .update({ lida: true })
      .eq('id', id)
      .select()
      .single();
      
    if (error) throw error;
    return data;
  } catch (error) {
    console.error(`Error marking notificacao ${id} as read:`, error);
    return null;
  }
};

export const marcarTodasNotificacoesComoLidas = async (usuarioId: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('notificacoes')
      .update({ lida: true })
      .eq('usuario_id', usuarioId)
      .eq('lida', false);
      
    if (error) throw error;
    return true;
  } catch (error) {
    console.error(`Error marking all notificacoes as read for usuario ${usuarioId}:`, error);
    return false;
  }
};

// CONFIGURAÇÕES (SETTINGS)

export const getConfiguracoes = async (): Promise<Configuracao[]> => {
  try {
    const { data, error } = await supabase
      .from('configuracoes')
      .select('*')
      .order('created_at', { ascending: true });
      
    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error fetching configuracoes:', error);
    return [];
  }
};

export const getConfiguracaoByNome = async (nome: string): Promise<string | null> => {
  try {
    const { data, error } = await supabase
      .from('configuracoes')
      .select('valor')
      .eq('nome', nome)
      .single();
    
    if (error) throw error;
    return data?.valor || null;
  } catch (error) {
    console.error(`Error fetching configuracao ${nome}:`, error);
    return null;
  }
};

export const updateConfiguracao = async (nome: string, valor: string): Promise<boolean> => {
  try {
    // First, check if the configuration exists
    const { data: existingConfig, error: checkError } = await supabase
      .from('configuracoes')
      .select('id')
      .eq('nome', nome)
      .maybeSingle();
    
    if (checkError) throw checkError;
    
    // If config exists, update it; otherwise, insert it
    if (existingConfig) {
      const { error } = await supabase
        .from('configuracoes')
        .update({ 
          valor, 
          updated_at: new Date().toISOString() 
        })
        .eq('nome', nome);
      
      if (error) throw error;
    } else {
      const { error } = await supabase
        .from('configuracoes')
        .insert({ 
          nome, 
          valor,
          descricao: `Configuração ${nome}` 
        });
      
      if (error) throw error;
    }
    
    return true;
  } catch (error) {
    console.error(`Error updating configuracao ${nome}:`, error);
    return false;
  }
};

// Batch save multiple configurations at once
export const saveConfiguracoes = async (configs: { nome: string, valor: string }[]): Promise<boolean> => {
  try {
    for (const config of configs) {
      await updateConfiguracao(config.nome, config.valor);
    }
    return true;
  } catch (error) {
    console.error('Error saving multiple configuracoes:', error);
    return false;
  }
};

// Function to check if the quotas table has the required columns
export const ensureQuotaColumns = async (): Promise<boolean> => {
  try {
    const { data: quotasQuery } = await supabase
      .from('quotas')
      .select('multa, numero_multas, situacao, comprovativo')
      .limit(1);

    const quotaColumnsExist = quotasQuery && quotasQuery.length >= 0;

    if (!quotaColumnsExist) {
      console.log('Need to add required columns to quotas table');
    }

    return true;
  } catch (error) {
    console.error('Error ensuring quota columns:', error);
    return false;
  }
};

/**
 * Ensures the comprovativos bucket exists with proper permissions
 */
export const ensureComprovativosBucketWithPolicy = async () => {
  try {
    await ensureComprovativosBucket();
    return true;
  } catch (error) {
    console.error('Error ensuring comprovativos bucket with policies:', error);
    return false;
  }
};
