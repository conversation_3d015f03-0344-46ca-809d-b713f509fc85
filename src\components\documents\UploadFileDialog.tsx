
import React, { useState, useRef } from 'react';
import { 
  <PERSON><PERSON>, 
  DialogContent, 
  DialogDescription, 
  Di<PERSON>Footer, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { FileUpIcon, XIcon } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface UploadFileDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onUpload: (file: File) => void;
  allowedTypes?: string[];
  maxSize?: number; // in MB
}

const UploadFileDialog: React.FC<UploadFileDialogProps> = ({
  isOpen,
  onClose,
  onUpload,
  allowedTypes = ['application/pdf', 'image/png', 'image/jpeg'],
  maxSize = 2 // 2MB default limit
}) => {
  const [file, setFile] = useState<File | null>(null);
  const [dragActive, setDragActive] = useState<boolean>(false);
  const { toast } = useToast();
  const inputRef = useRef<HTMLInputElement | null>(null);

  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const validateFile = (file: File): boolean => {
    // Check file type
    if (!allowedTypes.includes(file.type)) {
      toast({
        title: 'Tipo de arquivo inválido',
        description: `Apenas ${allowedTypes.map(t => t.split('/')[1]).join(', ')} são permitidos.`,
        variant: 'destructive'
      });
      return false;
    }

    // Check file size
    const fileSizeInMB = file.size / (1024 * 1024);
    if (fileSizeInMB > maxSize) {
      toast({
        title: 'Arquivo muito grande',
        description: `O tamanho máximo permitido é de ${maxSize}MB.`,
        variant: 'destructive'
      });
      return false;
    }

    return true;
  };

  const handleFileDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const newFile = e.dataTransfer.files[0];
      if (validateFile(newFile)) {
        setFile(newFile);
      }
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const newFile = e.target.files[0];
      if (validateFile(newFile)) {
        setFile(newFile);
      }
    }
  };

  const handleUpload = () => {
    if (file) {
      onUpload(file);
      handleReset();
    }
  };

  const handleReset = () => {
    setFile(null);
    if (inputRef.current) {
      inputRef.current.value = '';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Enviar Arquivo</DialogTitle>
          <DialogDescription>
            Selecione um arquivo para fazer upload.
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          <div 
            className={`border-2 border-dashed p-6 rounded-lg text-center ${
              dragActive ? 'border-primary bg-primary/5' : 'border-gray-300'
            }`}
            onDragEnter={handleDragEnter}
            onDragLeave={handleDragLeave}
            onDragOver={handleDragOver}
            onDrop={handleFileDrop}
          >
            {!file ? (
              <>
                <FileUpIcon className="mx-auto h-12 w-12 text-gray-400" />
                <div className="mt-4">
                  <p className="text-sm text-gray-600">
                    Arraste um arquivo ou clique para selecionar
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    PDF, PNG ou JPG até {maxSize}MB
                  </p>
                </div>
                <Label htmlFor="file-upload" className="sr-only">
                  Escolher arquivo
                </Label>
                <Input
                  id="file-upload"
                  ref={inputRef}
                  type="file"
                  accept={allowedTypes.join(',')}
                  onChange={handleFileChange}
                  className="hidden"
                />
                <Button
                  variant="outline"
                  onClick={() => inputRef.current?.click()}
                  className="mt-4"
                >
                  Selecionar arquivo
                </Button>
              </>
            ) : (
              <div className="flex items-center justify-between bg-gray-50 p-3 rounded">
                <div className="flex items-center">
                  <FileUpIcon className="h-6 w-6 text-primary mr-3" />
                  <div>
                    <p className="text-sm font-medium truncate max-w-[200px]">
                      {file.name}
                    </p>
                    <p className="text-xs text-gray-500">
                      {file.size < 1024 * 1024
                        ? `${(file.size / 1024).toFixed(1)} KB`
                        : `${(file.size / (1024 * 1024)).toFixed(1)} MB`}
                    </p>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleReset}
                >
                  <XIcon className="h-4 w-4" />
                </Button>
              </div>
            )}
          </div>
        </div>
        
        <DialogFooter className="flex justify-between sm:justify-between">
          <Button variant="outline" onClick={onClose}>
            Cancelar
          </Button>
          <Button 
            type="submit" 
            onClick={handleUpload} 
            disabled={!file}
          >
            Enviar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default UploadFileDialog;
