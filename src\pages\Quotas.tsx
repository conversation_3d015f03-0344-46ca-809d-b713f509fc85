
import React, { useState, useEffect } from 'react';
import Sidebar from '@/components/layout/Sidebar';
import { toast } from 'sonner';
import { useQuotas } from '@/hooks/useQuotas';
import { useSidebar } from '@/contexts/SidebarContext';
import { cn } from '@/lib/utils';
import { Quota, Morador } from '@/types';
import { useQuery } from '@tanstack/react-query';
import { getMoradores } from '@/utils/supabase-helpers';
import { ensureQuotaColumns, ensureComprovativosBucketWithPolicy } from '@/utils/supabase-helpers';
import { createQuotaColumns } from '@/components/quotas/QuotaColumns';
import { useQuotaActions } from '@/components/quotas/QuotaActions';
import QuotasPageHeader from '@/components/quotas/QuotasPageHeader';
import QuotasFiltersSection from '@/components/quotas/QuotasFiltersSection';
import QuotasTableSection from '@/components/quotas/QuotasTableSection';
import QuotasModals from '@/components/quotas/QuotasModals';

const Quotas = () => {
  const { collapsed, isMobile } = useSidebar();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedQuota, setSelectedQuota] = useState<Partial<Quota> | null>(null);
  const [quotaToDelete, setQuotaToDelete] = useState<Quota | null>(null);
  const [activeTab, setActiveTab] = useState('all');
  const [selectedMorador, setSelectedMorador] = useState<Morador | null>(null);
  const [selectedStatus, setSelectedStatus] = useState<string | null>(null);
  const [selectedFineStatus, setSelectedFineStatus] = useState<string | null>(null);
  
  const {
    quotas,
    isLoading,
    isAddingQuota,
    isEditingQuota,
    isDeletingQuota,
    isRegularizingFine,
    isGeneratingQuotas,
    error,
    addQuota,
    editQuota,
    removeQuota,
    setQuotaPaid,
    regularizeQuotaFine,
    generateQuotas
  } = useQuotas({
    onAddSuccess: () => setIsModalOpen(false),
    onEditSuccess: () => setIsModalOpen(false)
  });

  // Get moradores for the filter
  const { data: moradores = [] } = useQuery({
    queryKey: ['moradores'],
    queryFn: getMoradores,
  });

  const {
    paymentModal,
    fineRegularizationModal,
    handleOpenPaymentModal,
    handleConfirmPayment,
    handleOpenFineRegularizationModal,
    handleConfirmFineRegularization,
    handleSaveQuota,
    setPaymentModal,
    setFineRegularizationModal,
    confirmDialog
  } = useQuotaActions({
    quotas,
    setQuotaPaid,
    regularizeQuotaFine,
    addQuota,
    editQuota,
    removeQuota
  });

  // Verify and setup database schema on component mount
  useEffect(() => {
    const setupDatabase = async () => {
      try {
        const columnsResult = await ensureQuotaColumns();
        if (columnsResult) {
          console.log('Quota columns verified and created if needed');
        } else {
          console.error('Failed to verify quota columns');
        }

        const bucketResult = await ensureComprovativosBucketWithPolicy();
        if (bucketResult) {
          console.log('Comprovativos bucket verified and created if needed');
        } else {
          console.error('Failed to verify comprovativos bucket');
        }
      } catch (err) {
        console.error('Error setting up database:', err);
      }
    };

    setupDatabase();
  }, []);

  useEffect(() => {
    if (error) {
      console.error("Erro ao carregar quotas:", error);
      toast.error("Erro ao carregar as quotas. Por favor, tente novamente.");
    }
  }, [error]);

  const handleGenerateQuotas = (quotasToGenerate: any[]) => {
    console.log('🎯 Página: Iniciando geração de quotas:', quotasToGenerate.length);
    generateQuotas(quotasToGenerate);
  };

  const filterQuotasByTab = (quotasData: Quota[] = []) => {
    if (!quotasData) return [];

    const currentDate = new Date();
    const currentMonth = currentDate.getMonth() + 1;
    const currentYear = currentDate.getFullYear();

    let filteredQuotas = quotasData;

    // Apply all filters in sequence
    if (selectedMorador) {
      filteredQuotas = filteredQuotas.filter(quota =>
        quota.morador_id === selectedMorador.id
      );
    }

    if (selectedStatus) {
      filteredQuotas = filteredQuotas.filter(quota =>
        quota.status === selectedStatus
      );
    }

    if (selectedFineStatus) {
      if (selectedFineStatus === 'Regularizada') {
        filteredQuotas = filteredQuotas.filter(quota =>
          quota.multa > 0 && quota.situacao === 'Regularizada'
        );
      } else if (selectedFineStatus === 'Não Regularizada') {
        filteredQuotas = filteredQuotas.filter(quota =>
          quota.multa > 0 && quota.situacao === 'Não Regularizada'
        );
      }
    }

    // Then filter by tab
    switch (activeTab) {
      case 'current':
        return filteredQuotas.filter(quota =>
          quota.mes === currentMonth && quota.ano === currentYear
        );
      case 'overdue':
        return filteredQuotas.filter(quota => {
          const dueDate = new Date(quota.data_vencimento);
          return quota.status !== 'Pago' && dueDate < currentDate;
        });
      case 'fines':
        return filteredQuotas.filter(quota => quota.multa > 0);
      default:
        return filteredQuotas;
    }
  };

  const openEditModal = (quota: Quota) => {
    console.log('🔄 Abrindo modal de edição para quota:', quota);
    setSelectedQuota(quota);
    setIsModalOpen(true);
  };

  const handleDelete = (id: string) => {
    const quota = quotas?.find(q => q.id === id);
    if (quota) {
      setQuotaToDelete(quota);
    }
  };

  const columns = createQuotaColumns({
    onOpenPaymentModal: handleOpenPaymentModal,
    onOpenFineRegularizationModal: handleOpenFineRegularizationModal,
    isRegularizingFine,
    onEdit: openEditModal,
    onDelete: handleDelete
  });

  const confirmDelete = () => {
    if (quotaToDelete) {
      console.log('🗑️ Confirmando exclusão da quota:', quotaToDelete.id);
      removeQuota(quotaToDelete.id);
      setQuotaToDelete(null);
    }
  };

  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar />

      <div className={cn(
        "flex-1 flex flex-col transition-all duration-300 overflow-hidden",
        isMobile ? "ml-0" : (collapsed ? "ml-12" : "ml-64")
      )}>
        <div className="flex-1 overflow-y-auto pb-10">
          <div className="page-container">
            <QuotasPageHeader />

            <QuotasFiltersSection
              onNewQuota={() => {
                setSelectedQuota(null);
                setIsModalOpen(true);
              }}
              onGenerateQuotas={handleGenerateQuotas}
              isGeneratingQuotas={isGeneratingQuotas}
              moradores={moradores}
              selectedMorador={selectedMorador}
              onMoradorChange={setSelectedMorador}
              selectedStatus={selectedStatus}
              onStatusChange={setSelectedStatus}
              selectedFineStatus={selectedFineStatus}
              onFineStatusChange={setSelectedFineStatus}
            />

            <QuotasTableSection
              quotas={quotas}
              activeTab={activeTab}
              onTabChange={setActiveTab}
              columns={columns}
              formatQuotasForTable={filterQuotasByTab}
              isLoading={isLoading}
              isDeletingQuota={isDeletingQuota}
              isRegularizingFine={isRegularizingFine}
              isGeneratingQuotas={isGeneratingQuotas}
            />

            <QuotasModals
              isModalOpen={isModalOpen}
              onCloseModal={() => setIsModalOpen(false)}
              selectedQuota={selectedQuota}
              onSaveQuota={handleSaveQuota}
              isAddingQuota={isAddingQuota}
              isEditingQuota={isEditingQuota}
              paymentModal={paymentModal}
              onClosePaymentModal={() => setPaymentModal({ isOpen: false, quota: null })}
              onConfirmPayment={handleConfirmPayment}
              fineRegularizationModal={fineRegularizationModal}
              onCloseFineModal={() => setFineRegularizationModal({ isOpen: false, quota: null })}
              onConfirmFineRegularization={handleConfirmFineRegularization}
              isRegularizingFine={isRegularizingFine}
              quotaToDelete={quotaToDelete}
              onCancelDelete={() => setQuotaToDelete(null)}
              onConfirmDelete={confirmDelete}
              confirmDialog={confirmDialog}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Quotas;
