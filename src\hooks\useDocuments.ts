
import { useState, useCallback, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { DocumentFolder, DocumentFile } from '@/types';
import { ensureDocumentosBucket } from '@/utils/supabase-helpers';

export const useDocuments = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [currentFolder, setCurrentFolder] = useState<string | null>(null);
  const [breadcrumbs, setBreadcrumbs] = useState<DocumentFolder[]>([]);
  const [isGridView, setIsGridView] = useState(true);

  // Ensure bucket exists when the hook is initialized
  useEffect(() => {
    const createBucketIfNotExists = async () => {
      try {
        await ensureDocumentosBucket();
      } catch (error) {
        console.error('Error creating documents bucket:', error);
      }
    };
    
    createBucketIfNotExists();
  }, []);

  // Fetch folders from the current directory
  const fetchFolders = useCallback(async (parentId: string | null) => {
    try {
      console.log('Fetching folders for parent ID:', parentId);

      let query = supabase
        .from('document_folders')
        .select('*');
        
      if (parentId === null) {
        query = query.is('parent_id', null);
      } else {
        query = query.eq('parent_id', parentId);
      }
      
      const { data, error } = await query;
      
      if (error) {
        console.error('Error fetching folders:', error);
        throw error;
      }
      
      console.log('Folders fetched successfully:', data);
      return data || [];
    } catch (error) {
      console.error('Error fetching folders:', error);
      throw error;
    }
  }, []);

  // Fetch files from the current directory
  const fetchFiles = useCallback(async (parentId: string | null) => {
    try {
      let path = '';
      
      if (parentId) {
        path = `${parentId}/`;
      }
      
      console.log('Fetching files from path:', path);
      
      const { data, error } = await supabase
        .storage
        .from('documentos_condominio')
        .list(path, {
          sortBy: { column: 'name', order: 'asc' }
        });
      
      if (error) {
        console.error('Error fetching files:', error);
        throw error;
      }
      
      console.log('Storage response:', data);
      
      // Transform data to match our interface
      const files: DocumentFile[] = (data || [])
        .filter(item => !item.id.endsWith('/')) // Filter out directories
        .map(item => ({
          name: item.name,
          id: item.id,
          metadata: {
            mimetype: item.metadata?.mimetype || 'application/octet-stream',
            size: item.metadata?.size || 0
          },
          created_at: item.created_at,
          updated_at: item.updated_at || item.created_at,
          fullPath: parentId ? `${parentId}/${item.name}` : item.name,
          isFolder: false
        }));
      
      console.log('Transformed files:', files);
      return files;
    } catch (error) {
      console.error('Error fetching files:', error);
      return [];
    }
  }, []);

  // Combined query for folders and files
  const { data: documents, isLoading, error, refetch } = useQuery({
    queryKey: ['documents', currentFolder],
    queryFn: async () => {
      console.log('Fetching documents for folder:', currentFolder);
      
      try {
        const [folders, files] = await Promise.all([
          fetchFolders(currentFolder),
          fetchFiles(currentFolder)
        ]);
        
        console.log('Folders found:', folders);
        console.log('Files found:', files);
        
        // Convert folders to match DocumentFile interface for unified display
        const folderItems: DocumentFile[] = folders.map((folder: DocumentFolder) => ({
          name: folder.name,
          id: folder.id,
          metadata: {
            mimetype: 'folder',
            size: 0
          },
          created_at: folder.created_at,
          updated_at: folder.updated_at,
          fullPath: folder.id,
          isFolder: true
        }));
        
        return [...folderItems, ...files];
      } catch (error) {
        console.error('Error fetching documents:', error);
        toast({
          title: 'Erro',
          description: 'Não foi possível carregar os documentos.',
          variant: 'destructive'
        });
        return [];
      }
    }
  });

  // Fetch folder details for breadcrumb navigation
  const fetchFolderDetails = useCallback(async (folderId: string) => {
    try {
      const { data, error } = await supabase
        .from('document_folders')
        .select('*')
        .eq('id', folderId)
        .single();
      
      if (error) {
        console.error('Error fetching folder details:', error);
        throw error;
      }
      
      return data;
    } catch (error) {
      console.error(`Error fetching folder details for ID ${folderId}:`, error);
      throw error;
    }
  }, []);

  // Navigate to a folder and update breadcrumbs
  const navigateToFolder = useCallback(async (folderId: string | null) => {
    console.log('Navigating to folder:', folderId);
    setCurrentFolder(folderId);
    
    if (folderId === null) {
      // Root directory
      setBreadcrumbs([]);
    } else {
      try {
        const folder = await fetchFolderDetails(folderId);
        
        // Find the parent chain for breadcrumbs
        let parentChain: DocumentFolder[] = [folder];
        let currentParent = folder.parent_id;
        
        while (currentParent) {
          const parent = await fetchFolderDetails(currentParent);
          parentChain.unshift(parent);
          currentParent = parent.parent_id;
        }
        
        setBreadcrumbs(parentChain);
      } catch (error) {
        console.error('Error navigating to folder:', error);
        toast({
          title: 'Erro',
          description: 'Não foi possível navegar para esta pasta.',
          variant: 'destructive'
        });
      }
    }
    
    // Refresh the documents list
    queryClient.invalidateQueries({ queryKey: ['documents', folderId] });
  }, [fetchFolderDetails, queryClient, toast]);

  // Create a new folder
  const createFolderMutation = useMutation({
    mutationFn: async (name: string) => {
      const { data: userData, error: userError } = await supabase.auth.getUser();
      
      if (userError) {
        console.error('Error getting user:', userError);
        throw userError;
      }
      
      console.log('Creating folder with name:', name, 'in parent folder:', currentFolder);
      
      const { data, error } = await supabase
        .from('document_folders')
        .insert({
          name,
          parent_id: currentFolder,
          created_by: userData.user?.id
        })
        .select()
        .single();
      
      if (error) {
        console.error('Error creating folder:', error);
        throw error;
      }
      
      console.log('Folder created:', data);
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['documents', currentFolder] });
      toast({
        title: 'Pasta criada',
        description: 'Pasta criada com sucesso!',
      });
    },
    onError: (error) => {
      console.error('Error creating folder:', error);
      toast({
        title: 'Erro',
        description: 'Não foi possível criar a pasta.',
        variant: 'destructive'
      });
    }
  });

  // Upload a file
  const uploadFileMutation = useMutation({
    mutationFn: async (file: File) => {
      let path = file.name;
      
      if (currentFolder) {
        path = `${currentFolder}/${file.name}`;
      }
      
      console.log('Uploading file:', file.name, 'to path:', path);
      
      const { data, error } = await supabase
        .storage
        .from('documentos_condominio')
        .upload(path, file, {
          upsert: false,
        });
      
      if (error) {
        console.error('Error uploading file:', error);
        throw error;
      }
      
      console.log('File uploaded successfully:', data);
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['documents', currentFolder] });
      toast({
        title: 'Arquivo enviado',
        description: 'Arquivo enviado com sucesso!',
      });
    },
    onError: (error) => {
      console.error('Error uploading file:', error);
      toast({
        title: 'Erro',
        description: 'Não foi possível enviar o arquivo.',
        variant: 'destructive'
      });
    }
  });

  // Delete a file
  const deleteFileMutation = useMutation({
    mutationFn: async (path: string) => {
      console.log('Deleting file at path:', path);
      
      const { error } = await supabase
        .storage
        .from('documentos_condominio')
        .remove([path]);
      
      if (error) {
        console.error('Error deleting file:', error);
        throw error;
      }
      
      console.log('File deleted successfully');
      return path;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['documents', currentFolder] });
      toast({
        title: 'Arquivo excluído',
        description: 'Arquivo excluído com sucesso!',
      });
    },
    onError: (error) => {
      console.error('Error deleting file:', error);
      toast({
        title: 'Erro',
        description: 'Não foi possível excluir o arquivo.',
        variant: 'destructive'
      });
    }
  });

  // Delete a folder
  const deleteFolderMutation = useMutation({
    mutationFn: async (folderId: string) => {
      console.log('Deleting folder with ID:', folderId);
      
      const { error } = await supabase
        .from('document_folders')
        .delete()
        .eq('id', folderId);
      
      if (error) {
        console.error('Error deleting folder:', error);
        throw error;
      }
      
      console.log('Folder deleted successfully');
      return folderId;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['documents', currentFolder] });
      toast({
        title: 'Pasta excluída',
        description: 'Pasta excluída com sucesso!',
      });
    },
    onError: (error) => {
      console.error('Error deleting folder:', error);
      toast({
        title: 'Erro',
        description: 'Não foi possível excluir a pasta.',
        variant: 'destructive'
      });
    }
  });

  // Get public URL for a file
  const getFileUrl = useCallback((path: string) => {
    const { data } = supabase
      .storage
      .from('documentos_condominio')
      .getPublicUrl(path);
    
    return data.publicUrl;
  }, []);

  // Toggle between grid and list view
  const toggleView = useCallback(() => {
    setIsGridView(prev => !prev);
  }, []);

  return {
    documents,
    isLoading,
    error,
    currentFolder,
    breadcrumbs,
    isGridView,
    navigateToFolder,
    createFolder: createFolderMutation.mutate,
    uploadFile: uploadFileMutation.mutate,
    deleteFile: deleteFileMutation.mutate,
    deleteFolder: deleteFolderMutation.mutate,
    getFileUrl,
    toggleView,
    refetch
  };
};
