
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { FluxoCaixa } from '@/types';
import { createLancamento, updateLancamento, deleteLancamento } from '@/utils/supabase-helpers';
import { normalizeTransactionType, processFormValue, validateCompleteFluxoCaixa } from '@/utils/cash-flow-helpers';

type FluxoCaixaForm = Omit<FluxoCaixa, 'id' | 'created_at' | 'updated_at'>;

export const useFluxoCaixaMutations = () => {
  const queryClient = useQueryClient();

  const createMutation = useMutation({
    mutationFn: async (lancamento: FluxoCaixaForm) => {
      console.log('💰 useFluxoCaixaMutations: Creating lancamento with data:', JSON.stringify(lancamento, null, 2));
      console.log('💰 useFluxoCaixaMutations: Tipo being sent to createLancamento:', JSON.stringify(lancamento.tipo));
      
      const result = await createLancamento(lancamento);
      console.log('✅ useFluxoCaixaMutations: CreateLancamento result:', result);
      return result;
    },
    onSuccess: (result) => {
      console.log('🎉 useFluxoCaixaMutations: Lancamento created successfully:', result);
      queryClient.invalidateQueries({ queryKey: ['fluxo-caixa'] });
      queryClient.invalidateQueries({ queryKey: ['saldo-total'] });
      toast.success('Lançamento criado com sucesso!');
    },
    onError: (error) => {
      console.error('❌ useFluxoCaixaMutations: Error creating lancamento:', error);
      toast.error('Erro ao criar lançamento. Por favor, tente novamente.');
    }
  });

  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<FluxoCaixa> }) => 
      updateLancamento(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['fluxo-caixa'] });
      queryClient.invalidateQueries({ queryKey: ['saldo-total'] });
      toast.success('Lançamento atualizado com sucesso!');
    },
    onError: (error) => {
      console.error('Error updating lancamento:', error);
      toast.error('Erro ao atualizar lançamento');
    }
  });

  const deleteMutation = useMutation({
    mutationFn: (id: string) => deleteLancamento(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['fluxo-caixa'] });
      queryClient.invalidateQueries({ queryKey: ['saldo-total'] });
      toast.success('Lançamento excluído com sucesso!');
    },
    onError: (error) => {
      console.error('Error deleting lancamento:', error);
      toast.error('Erro ao excluir lançamento');
    }
  });

  const handleSaveLancamento = async (data: any): Promise<Partial<FluxoCaixa>> => {
    console.log('💾 useFluxoCaixaMutations: handleSaveLancamento START');
    console.log('💾 useFluxoCaixaMutations: Raw input data:', JSON.stringify(data, null, 2));
    
    try {
      // Passo 1: Processar valor se necessário
      let processedData = { ...data };
      
      if (typeof processedData.valor === 'string') {
        const originalValor = processedData.valor;
        processedData.valor = processFormValue(processedData.valor);
        console.log('💰 useFluxoCaixaMutations: Valor processed from', JSON.stringify(originalValor), 'to', processedData.valor);
      }
      
      // Passo 2: Normalizar tipo - PONTO CRÍTICO
      console.log('🔄 useFluxoCaixaMutations: BEFORE normalization - tipo:', JSON.stringify(processedData.tipo));
      processedData.tipo = normalizeTransactionType(processedData.tipo);
      console.log('🔄 useFluxoCaixaMutations: AFTER normalization - tipo:', JSON.stringify(processedData.tipo));
      
      // Passo 3: Validação final completa
      const validation = validateCompleteFluxoCaixa(processedData);
      if (!validation.isValid) {
        console.error('❌ useFluxoCaixaMutations: VALIDATION FAILED:', validation.errors);
        const errorMessage = `Erro de validação: ${validation.errors.join(', ')}`;
        toast.error(errorMessage);
        throw new Error(errorMessage);
      }
      
      // Passo 4: Garantia final - forçar valores válidos se necessário
      if (processedData.tipo !== 'entrada' && processedData.tipo !== 'saida') {
        console.warn('⚠️ useFluxoCaixaMutations: FORCING tipo to entrada due to invalid value:', processedData.tipo);
        processedData.tipo = 'entrada';
      }
      
      console.log('✅ useFluxoCaixaMutations: FINAL processed data:', JSON.stringify(processedData, null, 2));
      console.log('✅ useFluxoCaixaMutations: FINAL tipo value:', JSON.stringify(processedData.tipo));
      
      return processedData;
      
    } catch (error) {
      console.error('💥 useFluxoCaixaMutations: CRITICAL ERROR in handleSaveLancamento:', error);
      throw error;
    }
  };

  return {
    createMutation,
    updateMutation,
    deleteMutation,
    handleSaveLancamento
  };
};
