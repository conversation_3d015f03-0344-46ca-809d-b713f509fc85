
import React from 'react';
import ResidentLayout from '@/components/layout/resident/ResidentLayout';
import { AlertTriangle, Loader2 } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useResidentDashboard } from '@/hooks/resident/useResidentDashboard';
import DashboardCards from '@/components/resident/dashboard/DashboardCards';
import CurrentQuotaStatus from '@/components/resident/dashboard/CurrentQuotaStatus';
import RecentNotifications from '@/components/resident/dashboard/RecentNotifications';
import DashboardWarning from '@/components/resident/dashboard/DashboardWarning';
import ResidentCharts from '@/components/resident/dashboard/ResidentCharts';

const ResidentDashboard = () => {
  const { profile } = useAuth();
  const { data: dashboardData, isLoading, error } = useResidentDashboard();

  if (isLoading) {
    return (
      <ResidentLayout
        title={`Bem-vindo, ${profile?.name || 'Morador'}`}
        subtitle="Acompanhe sua situação no condomínio"
      >
        <div className="flex items-center justify-center h-64">
          <div className="flex items-center space-x-2">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span>Carregando...</span>
          </div>
        </div>
      </ResidentLayout>
    );
  }

  if (error) {
    console.error('❌ Erro no dashboard:', error);
    return (
      <ResidentLayout
        title={`Bem-vindo, ${profile?.name || 'Morador'}`}
        subtitle="Acompanhe sua situação no condomínio"
      >
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <p className="text-gray-600 mb-2">Erro ao carregar dados do dashboard</p>
            <p className="text-sm text-gray-500">
              {error instanceof Error ? error.message : 'Erro desconhecido'}
            </p>
            <p className="text-xs text-gray-400 mt-2">
              Apartamento: {profile?.apartment_id || 'Não definido'}
            </p>
          </div>
        </div>
      </ResidentLayout>
    );
  }

  const { currentQuota, summary, recentNotifications } = dashboardData || {
    currentQuota: null,
    summary: { totalPending: 0, totalFines: 0, monthsOverdue: 0, paymentRate: 0 },
    recentNotifications: []
  };

  // Verificar se não há dados do morador
  const hasNoResidentData = !currentQuota && summary.totalPending === 0 && summary.totalFines === 0;

  return (
    <ResidentLayout
      title={`Bem-vindo, ${profile?.name || 'Morador'}`}
      subtitle="Acompanhe sua situação no condomínio"
    >
      {hasNoResidentData && (
        <DashboardWarning apartmentId={profile?.apartment_id} />
      )}

      <DashboardCards currentQuota={currentQuota} summary={summary} />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-8">
        <CurrentQuotaStatus
          currentQuota={currentQuota}
          hasNoResidentData={hasNoResidentData}
        />
        <RecentNotifications notifications={recentNotifications} />
      </div>

      {/* Gráficos de Análise Visual */}
      <div className="mt-8">
        <ResidentCharts />
      </div>
    </ResidentLayout>
  );
};

export default ResidentDashboard;
