
import React from 'react';

interface QuotaFiltersProps {
  onFilter: (filters: {
    status?: string;
    mes?: string;
    ano?: string;
    periodo?: string;
  }) => void;
}

export const quotaFilters = [
  {
    name: 'Status',
    options: [
      { value: 'Pago', label: 'Pago' },
      { value: 'Não Pago', label: 'Não Pago' },
    ],
    onFilter: (value: string) => {
      console.log('Filtering by status:', value);
    },
  },
  {
    name: '<PERSON><PERSON><PERSON>',
    options: [
      { value: '1', label: 'Janeiro' },
      { value: '2', label: 'Fevereiro' },
      { value: '3', label: 'Março' },
      { value: '4', label: 'Abril' },
      { value: '5', label: 'Mai<PERSON>' },
      { value: '6', label: 'Junho' },
      { value: '7', label: 'Julho' },
      { value: '8', label: 'Agosto' },
      { value: '9', label: '<PERSON><PERSON><PERSON>' },
      { value: '10', label: '<PERSON><PERSON><PERSON>' },
      { value: '11', label: '<PERSON>em<PERSON>' },
      { value: '12', label: 'Dezembro' },
    ],
    onFilter: (value: string) => {
      console.log('Filtering by month:', value);
    },
  },
  {
    name: 'Ano',
    options: [
      { value: '2025', label: '2025' },
      { value: '2024', label: '2024' },
      { value: '2023', label: '2023' },
    ],
    onFilter: (value: string) => {
      console.log('Filtering by year:', value);
    },
  },
  {
    name: 'Período',
    options: [
      { value: 'current-month', label: 'Mês Atual' },
      { value: 'last-month', label: 'Mês Anterior' },
      { value: 'current-quarter', label: 'Trimestre Atual' },
      { value: 'last-quarter', label: 'Trimestre Anterior' },
      { value: 'current-year', label: 'Ano Atual' },
      { value: 'last-year', label: 'Ano Anterior' },
    ],
    onFilter: (value: string) => {
      console.log('Filtering by period:', value);
    },
  },
];
