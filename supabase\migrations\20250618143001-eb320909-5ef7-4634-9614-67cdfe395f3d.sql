
-- Atualizar moradores existentes para ter o user_id correto baseado no apartment_access ativo
UPDATE public.moradores 
SET user_id = aa.user_id,
    updated_at = now()
FROM public.apartment_access aa 
WHERE moradores.apartamento = aa.apartment_id 
  AND aa.is_active = true 
  AND moradores.user_id IS NULL;

-- Criar função para manter consistência entre moradores e apartment_access
CREATE OR REPLACE FUNCTION public.sync_morador_user_id()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Quando um acesso é concedido (insert ou update para ativo)
  IF (TG_OP = 'INSERT' AND NEW.is_active = true) OR 
     (TG_OP = 'UPDATE' AND NEW.is_active = true AND (OLD.is_active = false OR OLD.is_active IS NULL)) THEN
    
    -- Atualizar o morador correspondente com o user_id
    UPDATE public.moradores 
    SET user_id = NEW.user_id,
        updated_at = now()
    WHERE apartamento = NEW.apartment_id 
      AND (user_id IS NULL OR user_id != NEW.user_id);
    
  -- Quando um acesso é revogado
  ELSIF (TG_OP = 'UPDATE' AND NEW.is_active = false AND OLD.is_active = true) OR TG_OP = 'DELETE' THEN
    
    -- Remover o user_id do morador correspondente
    UPDATE public.moradores 
    SET user_id = NULL,
        updated_at = now()
    WHERE apartamento = COALESCE(NEW.apartment_id, OLD.apartment_id) 
      AND user_id = COALESCE(NEW.user_id, OLD.user_id);
      
  END IF;
  
  RETURN COALESCE(NEW, OLD);
END;
$$;

-- Criar trigger para manter sincronização automática
DROP TRIGGER IF EXISTS sync_morador_user_id_trigger ON public.apartment_access;
CREATE TRIGGER sync_morador_user_id_trigger
  AFTER INSERT OR UPDATE OR DELETE ON public.apartment_access
  FOR EACH ROW
  EXECUTE FUNCTION public.sync_morador_user_id();

-- Função simplificada para verificar e corrigir inconsistências
CREATE OR REPLACE FUNCTION public.fix_morador_user_id_consistency()
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Verificar se o usuário atual é admin
  IF NOT public.is_current_user_admin() THEN
    RAISE EXCEPTION 'Acesso negado: privilégios de administrador necessários';
  END IF;

  -- Corrigir moradores que deveriam ter user_id mas não têm
  UPDATE public.moradores 
  SET user_id = aa.user_id,
      updated_at = now()
  FROM public.apartment_access aa 
  WHERE moradores.apartamento = aa.apartment_id 
    AND aa.is_active = true 
    AND moradores.user_id IS NULL;
  
  -- Corrigir moradores que têm user_id mas não deveriam ter (acesso inativo)
  UPDATE public.moradores 
  SET user_id = NULL,
      updated_at = now()
  WHERE user_id IS NOT NULL 
    AND NOT EXISTS (
      SELECT 1 FROM public.apartment_access aa 
      WHERE aa.apartment_id = moradores.apartamento 
        AND aa.user_id = moradores.user_id 
        AND aa.is_active = true
    );
  
  RETURN true;
END;
$$;
