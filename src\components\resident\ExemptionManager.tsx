
import React from 'react';
import { Checkbox } from '@/components/ui/checkbox';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { AlertCircle } from 'lucide-react';

interface ExemptionManagerProps {
  isExempt: boolean;
  exemptionReason: string;
  onExemptChange: (exempt: boolean) => void;
  onReasonChange: (reason: string) => void;
  readonly?: boolean;
}

export const ExemptionManager: React.FC<ExemptionManagerProps> = ({
  isExempt,
  exemptionReason,
  onExemptChange,
  onReasonChange,
  readonly = false
}) => {
  if (readonly && !isExempt) {
    return null;
  }

  if (readonly && isExempt) {
    return (
      <div className="space-y-2">
        <div className="flex items-center gap-2">
          <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
            <AlertCircle className="w-3 h-3 mr-1" />
            Isento de Quotas
          </Badge>
        </div>
        {exemptionReason && (
          <p className="text-sm text-gray-600 bg-yellow-50 p-2 rounded border">
            <strong>Motivo:</strong> {exemptionReason}
          </p>
        )}
      </div>
    );
  }

  return (
    <div className="space-y-3">
      <div className="flex items-center space-x-2">
        <Checkbox
          id="isento_quotas"
          checked={isExempt}
          onCheckedChange={onExemptChange}
        />
        <Label htmlFor="isento_quotas" className="text-sm font-medium">
          Isento de Quotas
        </Label>
      </div>

      {isExempt && (
        <div className="space-y-2">
          <Label htmlFor="motivo_isencao" className="text-sm font-medium">
            Motivo da Isenção <span className="text-red-500">*</span>
          </Label>
          <Textarea
            id="motivo_isencao"
            value={exemptionReason}
            onChange={(e) => onReasonChange(e.target.value)}
            placeholder="Descreva o motivo da isenção (ex: Membro da Comissão, Coordenador, etc.)"
            className="min-h-[80px]"
          />
          {isExempt && !exemptionReason.trim() && (
            <p className="text-xs text-red-500">
              O motivo da isenção é obrigatório
            </p>
          )}
        </div>
      )}
    </div>
  );
};
