import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { ResidentNotification } from '@/types';

// Mock data - em produção, isso viria de uma API
const mockNotifications: ResidentNotification[] = [
  {
    id: '1',
    type: 'quota_paid',
    title: 'Quota de Junho Confirmada',
    message: 'Seu pagamento de 30.000 Kz foi registrado com sucesso. Obrigado pela pontualidade!',
    read: false,
    createdAt: new Date('2024-06-15T10:30:00'),
    priority: 'medium'
  },
  {
    id: '2',
    type: 'reminder',
    title: 'Lembrete: Quota de Julho',
    message: 'Sua quota de julho no valor de 30.000 Kz vence em 5 dias (15/07/2024). Não se esqueça de efetuar o pagamento.',
    read: false,
    createdAt: new Date('2024-07-10T09:00:00'),
    priority: 'high'
  },
  {
    id: '3',
    type: 'announcement',
    title: '<PERSON>união de Condomínio Agendada',
    message: 'Fica marcada reunião de condomínio para o dia 20/07/2024 às 19h no salão de festas. Sua presença é importante.',
    read: true,
    createdAt: new Date('2024-07-08T14:20:00'),
    priority: 'medium'
  },
  {
    id: '4',
    type: 'announcement',
    title: 'Manutenção do Elevador',
    message: 'O elevador principal estará em manutenção nos dias 22 e 23/07. Pedimos desculpas pelo inconveniente.',
    read: true,
    createdAt: new Date('2024-07-05T16:45:00'),
    priority: 'low'
  },
  {
    id: '5',
    type: 'quota_paid',
    title: 'Quota de Maio Confirmada',
    message: 'Seu pagamento de 30.000 Kz foi registrado com sucesso.',
    read: true,
    createdAt: new Date('2024-05-12T11:15:00'),
    priority: 'low'
  },
  {
    id: '6',
    type: 'fine_applied',
    title: 'Multa Aplicada - Quota de Abril',
    message: 'Foi aplicada uma multa de 2.000 Kz devido ao atraso no pagamento da quota de abril.',
    read: true,
    createdAt: new Date('2024-04-21T08:30:00'),
    priority: 'high'
  }
];

export const useResidentNotifications = () => {
  const { profile } = useAuth();
  const [notifications, setNotifications] = useState<ResidentNotification[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Simular carregamento de dados
  useEffect(() => {
    const fetchNotifications = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Simular delay de API
        await new Promise(resolve => setTimeout(resolve, 800));
        
        // Em produção, aqui faria a chamada para a API
        // const response = await api.get(`/residents/${profile?.id}/notifications`);
        // setNotifications(response.data);
        
        setNotifications(mockNotifications);
      } catch (err) {
        setError('Erro ao carregar notificações');
        console.error('Error fetching notifications:', err);
      } finally {
        setLoading(false);
      }
    };

    if (profile?.id) {
      fetchNotifications();
    }
  }, [profile?.id]);

  // Marcar notificação como lida
  const markAsRead = async (id: string) => {
    try {
      // Em produção: await api.patch(`/notifications/${id}/read`);
      
      setNotifications(prev => 
        prev.map(notification => 
          notification.id === id 
            ? { ...notification, read: true }
            : notification
        )
      );
      
      return true;
    } catch (error) {
      console.error('Error marking notification as read:', error);
      return false;
    }
  };

  // Marcar notificação como não lida
  const markAsUnread = async (id: string) => {
    try {
      // Em produção: await api.patch(`/notifications/${id}/unread`);
      
      setNotifications(prev => 
        prev.map(notification => 
          notification.id === id 
            ? { ...notification, read: false }
            : notification
        )
      );
      
      return true;
    } catch (error) {
      console.error('Error marking notification as unread:', error);
      return false;
    }
  };

  // Marcar todas como lidas
  const markAllAsRead = async () => {
    try {
      // Em produção: await api.patch(`/residents/${profile?.id}/notifications/read-all`);
      
      setNotifications(prev => 
        prev.map(notification => ({ ...notification, read: true }))
      );
      
      return true;
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      return false;
    }
  };

  // Excluir notificação
  const deleteNotification = async (id: string) => {
    try {
      // Em produção: await api.delete(`/notifications/${id}`);
      
      setNotifications(prev => 
        prev.filter(notification => notification.id !== id)
      );
      
      return true;
    } catch (error) {
      console.error('Error deleting notification:', error);
      return false;
    }
  };

  // Obter notificações não lidas
  const getUnreadNotifications = (): ResidentNotification[] => {
    return notifications.filter(notification => !notification.read);
  };

  // Obter notificações por tipo
  const getNotificationsByType = (type: ResidentNotification['type']): ResidentNotification[] => {
    return notifications.filter(notification => notification.type === type);
  };

  // Obter notificações por prioridade
  const getNotificationsByPriority = (priority: ResidentNotification['priority']): ResidentNotification[] => {
    return notifications.filter(notification => notification.priority === priority);
  };

  // Obter notificações recentes (últimos 7 dias)
  const getRecentNotifications = (): ResidentNotification[] => {
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    
    return notifications.filter(notification => 
      notification.createdAt >= sevenDaysAgo
    );
  };

  // Obter estatísticas das notificações
  const getNotificationStats = () => {
    const total = notifications.length;
    const unread = notifications.filter(n => !n.read).length;
    const byType = {
      quota_paid: notifications.filter(n => n.type === 'quota_paid').length,
      fine_applied: notifications.filter(n => n.type === 'fine_applied').length,
      reminder: notifications.filter(n => n.type === 'reminder').length,
      announcement: notifications.filter(n => n.type === 'announcement').length
    };
    const byPriority = {
      high: notifications.filter(n => n.priority === 'high').length,
      medium: notifications.filter(n => n.priority === 'medium').length,
      low: notifications.filter(n => n.priority === 'low').length
    };

    return {
      total,
      unread,
      read: total - unread,
      byType,
      byPriority
    };
  };

  // Atualizar notificações
  const refreshNotifications = async () => {
    setLoading(true);
    try {
      // Recarregar dados
      await new Promise(resolve => setTimeout(resolve, 500));
      // Em produção: const response = await api.get(`/residents/${profile?.id}/notifications`);
      // setNotifications(response.data);
    } catch (err) {
      setError('Erro ao atualizar notificações');
    } finally {
      setLoading(false);
    }
  };

  // Criar nova notificação (para testes)
  const addNotification = (notification: Omit<ResidentNotification, 'id' | 'createdAt'>) => {
    const newNotification: ResidentNotification = {
      ...notification,
      id: Date.now().toString(),
      createdAt: new Date()
    };
    
    setNotifications(prev => [newNotification, ...prev]);
  };

  return {
    notifications,
    loading,
    error,
    markAsRead,
    markAsUnread,
    markAllAsRead,
    deleteNotification,
    getUnreadNotifications,
    getNotificationsByType,
    getNotificationsByPriority,
    getRecentNotifications,
    getNotificationStats,
    refreshNotifications,
    addNotification
  };
};
