import React, { useState, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { CheckCircle, ChevronDown, X } from 'lucide-react';

interface StatusFilterProps {
  selectedStatus: string | null;
  onStatusChange: (status: string | null) => void;
  placeholder?: string;
  className?: string;
}

const statusOptions = [
  { value: null, label: 'Todos os status' },
  { value: 'Pago', label: 'Pago' },
  { value: 'Não Pago', label: 'Não Pago' }
];

const StatusFilter: React.FC<StatusFilterProps> = ({
  selectedStatus,
  onStatusChange,
  placeholder = "Filtrar por status...",
  className
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleStatusSelect = (status: string | null) => {
    onStatusChange(status);
    setIsOpen(false);
  };

  const handleClear = () => {
    onStatusChange(null);
  };

  const selectedOption = statusOptions.find(option => option.value === selectedStatus);

  return (
    <div className={cn("relative", className)} ref={dropdownRef}>
      <div className="flex flex-col">
        <label className="text-sm font-medium text-gray-700 mb-1">
          Status
        </label>
        <div className="relative">
          <div
            className={cn(
              "flex items-center w-full px-3 py-2 border border-gray-300 rounded-md bg-white cursor-pointer",
              "focus-within:outline-none focus-within:ring-2 focus-within:ring-primary-500 focus-within:border-primary-500",
              "hover:border-gray-400 transition-colors"
            )}
            onClick={() => setIsOpen(!isOpen)}
          >
            <CheckCircle size={16} className="text-gray-400 mr-2 flex-shrink-0" />
            
            <div className="flex items-center justify-between w-full">
              <span className="text-sm text-gray-900 truncate">
                {selectedOption?.label || placeholder}
              </span>
              
              <div className="flex items-center ml-2">
                {selectedStatus && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleClear();
                    }}
                    className="mr-1 p-1 hover:bg-gray-100 rounded-full transition-colors"
                    title="Limpar filtro"
                  >
                    <X size={14} className="text-gray-400" />
                  </button>
                )}
                <ChevronDown 
                  size={16} 
                  className={cn(
                    "text-gray-400 transition-transform",
                    isOpen && "transform rotate-180"
                  )} 
                />
              </div>
            </div>
          </div>

          {/* Dropdown */}
          {isOpen && (
            <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg">
              {statusOptions.map((option) => (
                <div
                  key={option.value || 'all'}
                  className={cn(
                    "px-3 py-2 hover:bg-gray-50 cursor-pointer",
                    selectedStatus === option.value && "bg-primary-50 text-primary-700"
                  )}
                  onClick={() => handleStatusSelect(option.value)}
                >
                  <div className="flex items-center">
                    <CheckCircle size={14} className="mr-2 text-gray-400" />
                    <span className="text-sm">{option.label}</span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default StatusFilter;
