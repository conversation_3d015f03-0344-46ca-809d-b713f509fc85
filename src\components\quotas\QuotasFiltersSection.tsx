
import React from 'react';
import { QuotaTableControls } from '@/components/quotas/QuotaTableControls';
import { Morador } from '@/types';

interface QuotasFiltersSectionProps {
  onNewQuota: () => void;
  onGenerateQuotas: (quotas: any[]) => void;
  isGeneratingQuotas: boolean;
  moradores: Morador[];
  selectedMorador: Morador | null;
  onMoradorChange: (morador: Morador | null) => void;
  selectedStatus: string | null;
  onStatusChange: (status: string | null) => void;
  selectedFineStatus: string | null;
  onFineStatusChange: (status: string | null) => void;
}

const QuotasFiltersSection: React.FC<QuotasFiltersSectionProps> = ({
  onNewQuota,
  onGenerateQuotas,
  isGeneratingQuotas,
  moradores,
  selectedMorador,
  onMoradorChange,
  selectedStatus,
  onStatusChange,
  selectedFineStatus,
  onFineStatusChange
}) => {
  return (
    <div className="mt-6 animate-enter">
      <QuotaTableControls
        onNewQuota={onNewQuota}
        onGenerateQuotas={onGenerateQuotas}
        isGenerating={isGeneratingQuotas}
        moradores={moradores}
        selectedMorador={selectedMorador}
        onMoradorChange={onMoradorChange}
        selectedStatus={selectedStatus}
        onStatusChange={onStatusChange}
        selectedFineStatus={selectedFineStatus}
        onFineStatusChange={onFineStatusChange}
      />
    </div>
  );
};

export default QuotasFiltersSection;
