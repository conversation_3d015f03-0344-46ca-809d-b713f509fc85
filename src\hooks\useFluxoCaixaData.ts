
import { useQuery } from '@tanstack/react-query';
import { getFluxoCaixa, calcularSaldo } from '@/utils/supabase-helpers';

export const useFluxoCaixaData = () => {
  const { data: fluxoCaixa, isLoading: isLoadingFluxo } = useQuery({
    queryKey: ['fluxo-caixa'],
    queryFn: async () => {
      console.log('🔄 useFluxoCaixaData: Fetching cash flow data...');
      const result = await getFluxoCaixa();
      console.log('📊 useFluxoCaixaData: Cash flow data fetched:', result.length, 'records');
      console.log('📊 useFluxoCaixaData: Data breakdown by type:', {
        entradas: result.filter(item => item.tipo === 'entrada').length,
        saidas: result.filter(item => item.tipo === 'saida').length
      });
      return result;
    }
  });

  const { data: saldoTotal, isLoading: isLoadingSaldo } = useQuery({
    queryKey: ['saldo-total'],
    queryFn: calcularSaldo
  });

  return {
    fluxoCaixa,
    saldoTotal,
    isLoading: isLoadingFluxo || isLoadingSaldo
  };
};
