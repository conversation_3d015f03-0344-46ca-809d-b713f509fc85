
import React from 'react';
import { format } from 'date-fns';
import { CheckCircle, Clock, AlertCircle, FileText, DollarSign, Ban, X, Edit, Trash2 } from 'lucide-react';
import { Quota } from '@/types';

interface QuotaColumnsProps {
  onOpenPaymentModal: (quota: Quota) => void;
  onOpenFineRegularizationModal: (quota: Quota) => void;
  isRegularizingFine: boolean;
  onEdit?: (row: any) => void;
  onDelete?: (id: string) => void;
}

const getMonthName = (month: number): string => {
  const months = [
    'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
    'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
  ];
  return months[month - 1] || 'N/A';
};

export const createQuotaColumns = ({
  onOpenPaymentModal,
  onOpenFineRegularizationModal,
  isRegularizingFine,
  onEdit,
  onDelete
}: QuotaColumnsProps) => [
  {
    header: 'Nome',
    accessor: 'moradores',
    isSortable: true,
    cell: (value: any, row: any) => (
      <div className="flex flex-col">
        <span className="font-medium">{value?.nome || 'N/A'}</span>
        <span className="text-xs text-gray-500">{value?.apartamento || 'N/A'}</span>
      </div>
    ),
  },
  {
    header: 'Período',
    accessor: 'periodo',
    isSortable: true,
    cell: (value: any, row: any) => (
      <div className="flex flex-col">
        <span className="font-medium">{getMonthName(row.mes)}</span>
        <span className="text-xs text-gray-500">{row.ano}</span>
      </div>
    ),
    align: 'center' as const,
  },
  {
    header: 'Status da Quota',
    accessor: 'status',
    isSortable: true,
    cell: (value: string, row: any) => (
      <div className="flex items-center justify-center">
        <span className={`
          inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
          ${value === 'Pago' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}
        `}>
          {value === 'Pago' ? <CheckCircle size={12} className="mr-1" /> : <Clock size={12} className="mr-1" />}
          {value}
        </span>
      </div>
    ),
    align: 'center' as const,
  },
  {
    header: 'Situação da Multa',
    accessor: 'situacao',
    cell: (value: string, row: any) => {
      const situacao = row.multa > 0 ? (value || 'Não Regularizada') : 'Regularizada';
      return (
        <div className="flex items-center justify-center">
          <span className={`
            inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
            ${situacao === 'Regularizada' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}
          `}>
            {situacao === 'Regularizada' ? <CheckCircle size={12} className="mr-1" /> : <AlertCircle size={12} className="mr-1" />}
            {situacao}
          </span>
        </div>
      );
    },
    align: 'center' as const,
  },
  {
    header: 'Data Vencimento',
    accessor: 'data_vencimento',
    cell: (value: string) => (
      <div className="text-center">
        {value ? format(new Date(value), 'dd/MM/yyyy') : '-'}
      </div>
    ),
    align: 'center' as const,
  },
  {
    header: 'Data Pagamento',
    accessor: 'data_pagamento',
    cell: (value: string) => (
      <div className="text-center">
        {value ? format(new Date(value), 'dd/MM/yyyy') : '-'}
      </div>
    ),
    align: 'center' as const,
  },
  {
    header: 'Valor Quota',
    accessor: 'valor',
    cell: (value: number) => (
      <div className="text-right">
        {value?.toLocaleString('pt-AO')} Kz
      </div>
    ),
    align: 'right' as const,
  },
  {
    header: 'Multa',
    accessor: 'multa',
    cell: (value: number, row: any) => {
      const isPaid = row.status === 'Pago';
      const isRegularized = row.situacao === 'Regularizada';
      const hasFine = value > 0;
      
      if (!hasFine) {
        return (
          <div className="text-right">
            <span className="text-gray-500">0 Kz</span>
          </div>
        );
      }
      
      // Multa regularizada - verde com traço
      if (isRegularized && hasFine) {
        return (
          <div className="text-right">
            <span className="text-green-600 font-semibold flex items-center justify-end">
              <CheckCircle size={12} className="mr-1" />
              <span className="line-through decoration-2">
                {value.toLocaleString('pt-AO')} Kz
              </span>
            </span>
          </div>
        );
      }
      
      // Quota paga mas multa não regularizada - laranja sem traço
      if (isPaid && !isRegularized && hasFine) {
        return (
          <div className="text-right">
            <span className="text-orange-600 font-semibold flex items-center justify-end">
              <AlertCircle size={12} className="mr-1" />
              {value.toLocaleString('pt-AO')} Kz
            </span>
          </div>
        );
      }
      
      // Multa ativa (não pago) - vermelho sem traço
      return (
        <div className="text-right">
          <span className="text-red-600 font-semibold flex items-center justify-end">
            <X size={12} className="mr-1" />
            {value.toLocaleString('pt-AO')} Kz
          </span>
        </div>
      );
    },
    align: 'right' as const,
  },
  {
    header: 'Comprovativo',
    accessor: 'comprovativo',
    cell: (value: string) => (
      <div className="text-center">
        {value ? (
          <a
            href={value}
            target="_blank"
            rel="noreferrer"
            className="inline-flex items-center text-blue-600 hover:text-blue-800"
          >
            <FileText size={16} className="mr-1" />
            Ver
          </a>
        ) : (
          '-'
        )}
      </div>
    ),
    align: 'center' as const,
  },
  {
    header: 'Total a Pagar',
    accessor: 'total',
    cell: (value: number, row: any) => {
      const isRegularized = row.situacao === 'Regularizada';
      const multaValue = isRegularized ? 0 : (row.multa || 0);
      const total = (row.valor || 0) + multaValue;
      
      return (
        <div className="text-right font-semibold">
          {total.toLocaleString('pt-AO')} Kz
        </div>
      );
    },
    align: 'right' as const,
  },
  {
    header: 'Ações',
    accessor: 'actions',
    cell: (value: any, row: any) => (
      <div className="flex items-center justify-center gap-1">
        {/* Botão para liquidar quota (se não pago) */}
        {row.status !== 'Pago' && (
          <button
            onClick={() => onOpenPaymentModal(row)}
            className="p-1 text-green-600 hover:text-green-800 hover:bg-green-100 rounded transition-colors"
            title="Liquidar Quota"
          >
            <DollarSign size={16} />
          </button>
        )}

        {/* Botão para regularizar multa com modal de data */}
        {row.status === 'Pago' && row.multa > 0 && row.situacao !== 'Regularizada' && (
          <button
            onClick={() => onOpenFineRegularizationModal(row)}
            className="p-1 text-orange-600 hover:text-orange-800 hover:bg-orange-100 rounded transition-colors"
            title="Regularizar Multa"
            disabled={isRegularizingFine}
          >
            <Ban size={16} />
          </button>
        )}

        {/* Botão de editar - sempre visível */}
        {onEdit && (
          <button
            onClick={() => onEdit(row)}
            className="p-1 text-blue-600 hover:text-blue-800 hover:bg-blue-100 rounded transition-colors"
            title="Editar Quota"
          >
            <Edit size={16} />
          </button>
        )}

        {/* Botão de excluir - sempre visível */}
        {onDelete && (
          <button
            onClick={() => onDelete(row.id)}
            className="p-1 text-red-600 hover:text-red-800 hover:bg-red-100 rounded transition-colors"
            title="Excluir Quota"
          >
            <Trash2 size={16} />
          </button>
        )}
      </div>
    ),
    align: 'center' as const,
  },
];
