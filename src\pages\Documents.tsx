
import React, { useState } from 'react';
import {
  FolderPlusIcon,
  UploadIcon,
  LayoutGridIcon,
  ListIcon,
  RefreshCwIcon
} from 'lucide-react';
import { useDocuments } from '@/hooks/useDocuments';
import { useAuth } from '@/contexts/AuthContext';
import { useSidebar } from '@/contexts/SidebarContext';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Table, TableHeader, TableRow, TableHead, TableBody } from '@/components/ui/table';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import Header from '@/components/layout/Header';
import FileCard from '@/components/documents/FileCard';
import FileRow from '@/components/documents/FileRow';
import Breadcrumbs from '@/components/documents/Breadcrumbs';
import UploadFileDialog from '@/components/documents/UploadFileDialog';
import CreateFolderDialog from '@/components/documents/CreateFolderDialog';
import { DocumentFile } from '@/types';

// Import Sidebar component
import Sidebar from '@/components/layout/Sidebar';

const Documents: React.FC = () => {
  const { collapsed, isMobile } = useSidebar();
  const {
    documents,
    isLoading,
    currentFolder,
    breadcrumbs,
    isGridView,
    navigateToFolder,
    createFolder,
    uploadFile,
    deleteFile,
    deleteFolder,
    getFileUrl,
    toggleView,
    refetch
  } = useDocuments();

  const [isCreateFolderOpen, setIsCreateFolderOpen] = useState(false);
  const [isUploadFileOpen, setIsUploadFileOpen] = useState(false);
  const [deleteItemConfirm, setDeleteItemConfirm] = useState<DocumentFile | null>(null);

  const { user } = useAuth();
  const { toast } = useToast();

  const handleOpenDocument = (document: DocumentFile) => {
    if (document.isFolder) {
      navigateToFolder(document.id);
    } else {
      window.open(getFileUrl(document.fullPath), '_blank');
    }
  };

  const handleDeleteDocument = (document: DocumentFile) => {
    setDeleteItemConfirm(document);
  };

  const confirmDelete = () => {
    if (!deleteItemConfirm) return;

    if (deleteItemConfirm.isFolder) {
      deleteFolder(deleteItemConfirm.id);
    } else {
      deleteFile(deleteItemConfirm.fullPath);
    }

    setDeleteItemConfirm(null);
  };

  const handleCreateFolder = (name: string) => {
    createFolder(name);
    setIsCreateFolderOpen(false);
  };

  const handleUploadFile = (file: File) => {
    uploadFile(file);
    setIsUploadFileOpen(false);
  };

  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar />
      <div className={cn(
        "flex-1 flex flex-col overflow-hidden transition-all duration-300",
        isMobile ? "ml-0" : (collapsed ? "ml-12" : "ml-64")
      )}>
        <div className="flex-1 flex flex-col p-6 max-w-none">
          <Header
            title="Documentos"
            subtitle="Gerencie todos os documentos do condomínio"
          />

          <div className="mb-6 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <Breadcrumbs
              folders={breadcrumbs}
              onNavigate={navigateToFolder}
            />

            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => refetch()}
              >
                <RefreshCwIcon className="h-4 w-4 mr-2" />
                Atualizar
              </Button>

              <Tabs defaultValue={isGridView ? "grid" : "list"} className="hidden sm:block">
                <TabsList>
                  <TabsTrigger value="grid" onClick={() => isGridView || toggleView()}>
                    <LayoutGridIcon className="h-4 w-4" />
                  </TabsTrigger>
                  <TabsTrigger value="list" onClick={() => !isGridView || toggleView()}>
                    <ListIcon className="h-4 w-4" />
                  </TabsTrigger>
                </TabsList>
              </Tabs>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border p-4 mb-6">
            <div className="flex justify-end space-x-2">
              <Button
                variant="outline"
                onClick={() => setIsCreateFolderOpen(true)}
              >
                <FolderPlusIcon className="h-4 w-4 mr-2" />
                Nova Pasta
              </Button>

              <Button
                onClick={() => setIsUploadFileOpen(true)}
              >
                <UploadIcon className="h-4 w-4 mr-2" />
                Enviar Arquivo
              </Button>
            </div>
          </div>

          <div className="flex-1 overflow-y-auto">
            {isLoading ? (
              <div className="text-center py-10">
                <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary mx-auto"></div>
                <p className="mt-4 text-gray-600">Carregando documentos...</p>
              </div>
            ) : documents && documents.length === 0 ? (
              <div className="text-center py-16 bg-white rounded-lg shadow-sm border">
                <FolderPlusIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-lg font-medium text-gray-900">Pasta vazia</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Esta pasta está vazia. Comece enviando arquivos ou criando pastas.
                </p>
                <div className="mt-6 flex justify-center space-x-4">
                  <Button
                    variant="outline"
                    onClick={() => setIsCreateFolderOpen(true)}
                  >
                    <FolderPlusIcon className="h-4 w-4 mr-2" />
                    Nova Pasta
                  </Button>
                  <Button
                    onClick={() => setIsUploadFileOpen(true)}
                  >
                    <UploadIcon className="h-4 w-4 mr-2" />
                    Enviar Arquivo
                  </Button>
                </div>
              </div>
            ) : isGridView ? (
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 2xl:grid-cols-10 gap-4">
                {documents?.map((document) => (
                  <FileCard
                    key={document.id}
                    file={document}
                    onOpen={() => handleOpenDocument(document)}
                    onDelete={() => handleDeleteDocument(document)}
                    getFileUrl={getFileUrl}
                  />
                ))}
              </div>
            ) : (
              <div className="bg-white rounded-lg shadow-sm border overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Nome</TableHead>
                      <TableHead>Tamanho</TableHead>
                      <TableHead>Data</TableHead>
                      <TableHead className="w-[70px]">Ações</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {documents?.map((document) => (
                      <FileRow
                        key={document.id}
                        file={document}
                        onOpen={() => handleOpenDocument(document)}
                        onDelete={() => handleDeleteDocument(document)}
                        getFileUrl={getFileUrl}
                      />
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </div>

          {/* Dialogs */}
          <CreateFolderDialog
            isOpen={isCreateFolderOpen}
            onClose={() => setIsCreateFolderOpen(false)}
            onCreateFolder={handleCreateFolder}
          />

          <UploadFileDialog
            isOpen={isUploadFileOpen}
            onClose={() => setIsUploadFileOpen(false)}
            onUpload={handleUploadFile}
            allowedTypes={['application/pdf', 'image/png', 'image/jpeg', 'image/jpg']}
            maxSize={2} // 2MB
          />

          <AlertDialog open={!!deleteItemConfirm} onOpenChange={() => setDeleteItemConfirm(null)}>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Confirmar exclusão</AlertDialogTitle>
                <AlertDialogDescription>
                  {deleteItemConfirm?.isFolder
                    ? `Tem certeza que deseja excluir a pasta "${deleteItemConfirm?.name}"?`
                    : `Tem certeza que deseja excluir o arquivo "${deleteItemConfirm?.name}"?`}
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancelar</AlertDialogCancel>
                <AlertDialogAction
                  onClick={confirmDelete}
                  className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
                >
                  Excluir
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </div>
    </div>
  );
};

export default Documents;
