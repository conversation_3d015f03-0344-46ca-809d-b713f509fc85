import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  CheckCircle,
  Clock,
  AlertTriangle,
  Download,
  Calendar,
  DollarSign
} from 'lucide-react';

interface QuotaCardProps {
  quota: {
    id: string;
    month: number;
    year: number;
    amount: number;
    status: 'paid' | 'pending' | 'overdue';
    dueDate: Date;
    paidDate?: Date;
    fine?: number;
    receipt?: string;
  };
  onDownloadReceipt?: (receipt: string) => void;
}

const QuotaCard: React.FC<QuotaCardProps> = ({ quota, onDownloadReceipt }) => {
  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'paid':
        return {
          icon: CheckCircle,
          color: 'text-green-500',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          badge: { variant: 'default' as const, className: 'bg-green-100 text-green-800', text: 'Pago' }
        };
      case 'pending':
        return {
          icon: Clock,
          color: 'text-yellow-500',
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200',
          badge: { variant: 'default' as const, className: 'bg-yellow-100 text-yellow-800', text: 'Pendente' }
        };
      case 'overdue':
        return {
          icon: AlertTriangle,
          color: 'text-red-500',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          badge: { variant: 'destructive' as const, className: '', text: 'Em Atraso' }
        };
      default:
        return {
          icon: Clock,
          color: 'text-gray-500',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
          badge: { variant: 'secondary' as const, className: '', text: 'Desconhecido' }
        };
    }
  };

  const getMonthName = (month: number) => {
    const months = [
      'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
      'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
    ];
    return months[month - 1];
  };

  const statusConfig = getStatusConfig(quota.status);
  const StatusIcon = statusConfig.icon;

  const getDaysUntilDue = () => {
    const today = new Date();
    const dueDate = new Date(quota.dueDate);
    const diffTime = dueDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const daysUntilDue = getDaysUntilDue();

  return (
    <Card className={`transition-all hover:shadow-md ${statusConfig.borderColor} border-2`}>
      <CardHeader className={`${statusConfig.bgColor} rounded-t-lg`}>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold flex items-center">
            <StatusIcon className={`h-5 w-5 mr-2 ${statusConfig.color}`} />
            {getMonthName(quota.month)}/{quota.year}
          </CardTitle>
          <Badge 
            variant={statusConfig.badge.variant}
            className={statusConfig.badge.className}
          >
            {statusConfig.badge.text}
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent className="pt-6">
        <div className="space-y-4">
          {/* Valor */}
          <div className="flex items-center justify-between">
            <div className="flex items-center text-gray-600">
              <DollarSign className="h-4 w-4 mr-2" />
              <span className="text-sm">Valor da Quota</span>
            </div>
            <span className="text-lg font-bold">{quota.amount.toLocaleString()} Kz</span>
          </div>

          {/* Multa (se houver) */}
          {quota.fine && quota.fine > 0 && (
            <div className="flex items-center justify-between">
              <div className="flex items-center text-red-600">
                <AlertTriangle className="h-4 w-4 mr-2" />
                <span className="text-sm">Multa</span>
              </div>
              <span className="text-lg font-bold text-red-600">
                {quota.fine.toLocaleString()} Kz
              </span>
            </div>
          )}

          {/* Data de Vencimento */}
          <div className="flex items-center justify-between">
            <div className="flex items-center text-gray-600">
              <Calendar className="h-4 w-4 mr-2" />
              <span className="text-sm">Vencimento</span>
            </div>
            <span className="text-sm font-medium">
              {quota.dueDate.toLocaleDateString('pt-BR')}
            </span>
          </div>

          {/* Data de Pagamento (se pago) */}
          {quota.paidDate && (
            <div className="flex items-center justify-between">
              <div className="flex items-center text-green-600">
                <CheckCircle className="h-4 w-4 mr-2" />
                <span className="text-sm">Pago em</span>
              </div>
              <span className="text-sm font-medium text-green-600">
                {quota.paidDate.toLocaleDateString('pt-BR')}
              </span>
            </div>
          )}

          {/* Aviso para quotas pendentes */}
          {quota.status === 'pending' && (
            <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="flex items-center">
                <Clock className="h-4 w-4 text-yellow-500 mr-2" />
                <span className="text-sm text-yellow-800">
                  {daysUntilDue > 0 
                    ? `Vence em ${daysUntilDue} dia${daysUntilDue > 1 ? 's' : ''}`
                    : daysUntilDue === 0 
                    ? 'Vence hoje'
                    : `Venceu há ${Math.abs(daysUntilDue)} dia${Math.abs(daysUntilDue) > 1 ? 's' : ''}`
                  }
                </span>
              </div>
            </div>
          )}

          {/* Aviso para quotas em atraso */}
          {quota.status === 'overdue' && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center">
                <AlertTriangle className="h-4 w-4 text-red-500 mr-2" />
                <span className="text-sm text-red-800">
                  Quota em atraso. Entre em contato com a administração.
                </span>
              </div>
            </div>
          )}

          {/* Botão de Download do Comprovante */}
          {quota.receipt && onDownloadReceipt && (
            <div className="pt-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onDownloadReceipt(quota.receipt!)}
                className="w-full"
              >
                <Download className="h-4 w-4 mr-2" />
                Baixar Comprovante
              </Button>
            </div>
          )}

          {/* Total (valor + multa) */}
          {quota.fine && quota.fine > 0 && (
            <div className="pt-2 border-t border-gray-200">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">Total a Pagar</span>
                <span className="text-xl font-bold text-gray-900">
                  {(quota.amount + quota.fine).toLocaleString()} Kz
                </span>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default QuotaCard;
