import React, { useState, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { CheckCircle, ChevronDown } from 'lucide-react';

interface IconStatusFilterProps {
  selectedStatus: string | null;
  onStatusChange: (status: string | null) => void;
  className?: string;
}

const statusOptions = [
  { value: null, label: 'Todos os status', color: 'text-gray-600' },
  { value: 'Pago', label: 'Pago', color: 'text-green-600' },
  { value: 'Não Pago', label: 'Não Pago', color: 'text-red-600' }
];

const IconStatusFilter: React.FC<IconStatusFilterProps> = ({
  selectedStatus,
  onStatusChange,
  className
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleStatusSelect = (status: string | null) => {
    onStatusChange(status);
    setIsOpen(false);
  };

  const selectedOption = statusOptions.find(option => option.value === selectedStatus);
  const isActive = selectedStatus !== null;

  return (
    <div className={cn("relative", className)} ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={cn(
          "flex items-center gap-2 px-3 py-2 rounded-lg border transition-all duration-200",
          "hover:shadow-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-1",
          isActive 
            ? "bg-green-50 border-green-200 text-green-700 shadow-sm" 
            : "bg-white border-gray-300 text-gray-600 hover:border-gray-400"
        )}
        title={`Filtrar por status: ${selectedOption?.label || 'Todos'}`}
      >
        <CheckCircle 
          size={18} 
          className={cn(
            "transition-colors duration-200",
            isActive ? "text-green-600" : "text-gray-500"
          )} 
        />
        <span className="text-sm font-medium">Status</span>
        {isActive && (
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
        )}
        <ChevronDown 
          size={16} 
          className={cn(
            "transition-transform duration-200",
            isOpen && "transform rotate-180"
          )} 
        />
      </button>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute z-50 mt-2 w-48 bg-white border border-gray-200 rounded-lg shadow-lg animate-in slide-in-from-top-2 duration-200">
          {statusOptions.map((option) => (
            <button
              key={option.value || 'all'}
              className={cn(
                "w-full px-3 py-2 text-left hover:bg-gray-50 transition-colors duration-150",
                "first:rounded-t-lg last:rounded-b-lg",
                selectedStatus === option.value && "bg-primary-50 text-primary-700"
              )}
              onClick={() => handleStatusSelect(option.value)}
            >
              <div className="flex items-center gap-2">
                <CheckCircle size={16} className={option.color} />
                <span className="text-sm">{option.label}</span>
                {selectedStatus === option.value && (
                  <div className="ml-auto w-2 h-2 bg-primary-500 rounded-full"></div>
                )}
              </div>
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default IconStatusFilter;
