
import { format, differenceInDays, parseISO, isAfter, isBefore, isEqual } from 'date-fns';

export interface MultaConfig {
  valorMulta: number;
  diasAtrasoMulta: number;
}

export const calculateFineAmount = (
  dataPagamento: string | null,
  dataVencimento: string,
  config: MultaConfig
): number => {
  console.log('🎯 Calculando multa:', {
    dataPagamento,
    dataVencimento,
    config
  });

  const vencimento = parseISO(dataVencimento);
  const hoje = new Date();
  
  // Se a quota foi paga
  if (dataPagamento) {
    const pagamento = parseISO(dataPagamento);
    const diffDays = differenceInDays(pagamento, vencimento);
    
    console.log('📅 Diferença em dias (pagamento vs vencimento):', diffDays);
    console.log('📋 Dias tolerância configurados:', config.diasAtrasoMulta);
    
    // Se pagou APÓS o vencimento + tolerância
    if (diffDays > config.diasAtrasoMulta) {
      console.log('💰 Aplicando multa por pagamento em atraso:', config.valorMulta, 'Kz');
      return config.valorMulta;
    }
    
    console.log('✅ Sem multa - pagamento dentro do prazo');
    return 0;
  }
  
  // Se a quota NÃO foi paga, verificar se já está em atraso
  const diffDaysFromToday = differenceInDays(hoje, vencimento);
  
  console.log('📅 Diferença em dias (hoje vs vencimento):', diffDaysFromToday);
  
  // Se já passou do vencimento + tolerância
  if (diffDaysFromToday > config.diasAtrasoMulta) {
    console.log('💰 Aplicando multa por atraso:', config.valorMulta, 'Kz');
    return config.valorMulta;
  }
  
  console.log('✅ Sem multa - ainda dentro do prazo');
  return 0;
};

export const calculateQuotaStatus = (
  dataPagamento: string | null,
  dataVencimento: string
): string => {
  if (!dataPagamento) {
    const hoje = new Date();
    const vencimento = parseISO(dataVencimento);
    return isAfter(hoje, vencimento) ? 'Não Pago' : 'Não Pago';
  }
  return 'Pago';
};

export const calculateSituacaoMulta = (
  multa: number,
  dataPagamento: string | null,
  dataVencimento: string
): string => {
  console.log('🎯 Calculando situação da multa:', { multa, dataPagamento, dataVencimento });
  
  // Se não há multa, está regularizada
  if (multa === 0) {
    console.log('✅ Regularizada - sem multa');
    return 'Regularizada';
  }
  
  // Se há multa mas a quota foi paga, a situação da multa depende de se a multa foi regularizada
  // Para este cálculo automático, assumimos que se há multa > 0, não está regularizada
  if (multa > 0) {
    console.log('❌ Não Regularizada - há multa pendente');
    return 'Não Regularizada';
  }
  
  return 'Regularizada';
};

// Validação inteligente de multa baseada na data atual
export const shouldApplyFine = (
  dataVencimento: string,
  config: MultaConfig,
  dataPagamento?: string | null
): boolean => {
  const vencimento = parseISO(dataVencimento);
  const hoje = new Date();
  
  // Se tem data de pagamento, verificar se pagou em atraso
  if (dataPagamento) {
    const pagamento = parseISO(dataPagamento);
    const diffDays = differenceInDays(pagamento, vencimento);
    return diffDays > config.diasAtrasoMulta;
  }
  
  // Se não foi pago, verificar se já passou do prazo
  const diffDaysFromToday = differenceInDays(hoje, vencimento);
  return diffDaysFromToday > config.diasAtrasoMulta;
};

// Correção automática de multa ao marcar como pago
export const correctFineOnPayment = (
  dataPagamento: string,
  dataVencimento: string,
  config: MultaConfig
): { shouldHaveFine: boolean; newFineAmount: number } => {
  const pagamento = parseISO(dataPagamento);
  const vencimento = parseISO(dataVencimento);
  
  const diffDays = differenceInDays(pagamento, vencimento);
  const shouldHaveFine = diffDays > config.diasAtrasoMulta;
  const newFineAmount = shouldHaveFine ? config.valorMulta : 0;
  
  console.log('🔧 Correção automática de multa:', {
    dataPagamento,
    dataVencimento,
    diffDays,
    shouldHaveFine,
    newFineAmount
  });
  
  return { shouldHaveFine, newFineAmount };
};



// Nova função para validar se uma quota pode ser criada
export const validateQuotaCreation = (
  mes: number,
  ano: number,
  dataVencimento?: string
): { isValid: boolean; message?: string } => {
  const currentDate = new Date();
  const currentMonth = currentDate.getMonth() + 1;
  const currentYear = currentDate.getFullYear();

  // Validar se o mês/ano não é muito futuro
  if (ano > currentYear + 1 || (ano === currentYear + 1 && mes > currentMonth)) {
    return {
      isValid: false,
      message: 'Não é possível criar quotas muito distantes no futuro'
    };
  }

  // Validar se o mês/ano não é muito antigo
  if (ano < 2020) {
    return {
      isValid: false,
      message: 'Ano muito antigo para criação de quotas'
    };
  }

  return { isValid: true };
};

// Interface para quota simplificada para validação
interface QuotaForValidation {
  id: string;
  morador_id: string;
  mes: number;
  ano: number;
  status: string;
  multa: number;
  situacao?: string;
  isento_quotas?: boolean;
}

// Função para validar pagamento sequencial
export const validateSequentialPayment = (
  quotaToPayId: string,
  allQuotas: QuotaForValidation[]
): { isValid: boolean; message?: string; unpaidQuotas?: QuotaForValidation[] } => {
  console.log('🔍 Validando pagamento sequencial para quota:', quotaToPayId);

  // Encontrar a quota que se quer pagar
  const quotaToPay = allQuotas.find(q => q.id === quotaToPayId);
  if (!quotaToPay) {
    return {
      isValid: false,
      message: 'Quota não encontrada'
    };
  }

  console.log('📋 Quota a ser paga:', {
    morador_id: quotaToPay.morador_id,
    mes: quotaToPay.mes,
    ano: quotaToPay.ano,
    status: quotaToPay.status
  });

  // Se a quota já está paga, não precisa validar
  if (quotaToPay.status === 'Pago') {
    return {
      isValid: false,
      message: 'Esta quota já foi paga'
    };
  }

  // Filtrar quotas do mesmo morador que não estão pagas
  const moradorQuotas = allQuotas.filter(q =>
    q.morador_id === quotaToPay.morador_id &&
    q.status !== 'Pago'
  );

  console.log(`👤 Quotas não pagas do morador: ${moradorQuotas.length}`);

  // Ordenar por ano e mês (mais antigas primeiro)
  const sortedQuotas = moradorQuotas.sort((a, b) => {
    if (a.ano !== b.ano) {
      return a.ano - b.ano;
    }
    return a.mes - b.mes;
  });

  // Encontrar quotas anteriores não pagas
  const unpaidPreviousQuotas = sortedQuotas.filter(q => {
    // Quota é anterior se:
    // 1. Ano é menor, ou
    // 2. Mesmo ano mas mês é menor
    return (q.ano < quotaToPay.ano) ||
           (q.ano === quotaToPay.ano && q.mes < quotaToPay.mes);
  });

  console.log(`📅 Quotas anteriores não pagas: ${unpaidPreviousQuotas.length}`);

  if (unpaidPreviousQuotas.length > 0) {
    // Criar mensagem detalhada com os meses pendentes
    const monthNames = [
      'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
      'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
    ];

    const pendingPeriods = unpaidPreviousQuotas.map(q =>
      `${monthNames[q.mes - 1]}/${q.ano}`
    ).join(', ');

    const message = unpaidPreviousQuotas.length === 1
      ? `Você deve pagar primeiro a quota de ${pendingPeriods} antes de pagar esta.`
      : `Você deve pagar primeiro as quotas de ${pendingPeriods} antes de pagar esta.`;

    console.log('❌ Pagamento sequencial violado:', message);

    return {
      isValid: false,
      message,
      unpaidQuotas: unpaidPreviousQuotas
    };
  }

  console.log('✅ Pagamento sequencial válido');
  return { isValid: true };
};

// Função para validar regularização sequencial de multas
export const validateSequentialFineRegularization = (
  quotaToRegularizeId: string,
  allQuotas: QuotaForValidation[]
): { isValid: boolean; message?: string; unpaidFines?: QuotaForValidation[] } => {
  console.log('🔍 Validando regularização sequencial de multa para quota:', quotaToRegularizeId);

  // Encontrar a quota que se quer regularizar
  const quotaToRegularize = allQuotas.find(q => q.id === quotaToRegularizeId);
  if (!quotaToRegularize) {
    return {
      isValid: false,
      message: 'Quota não encontrada'
    };
  }

  console.log('📋 Quota a ser regularizada:', {
    morador_id: quotaToRegularize.morador_id,
    mes: quotaToRegularize.mes,
    ano: quotaToRegularize.ano,
    multa: quotaToRegularize.multa
  });

  // Se a quota não tem multa, não precisa regularizar
  if (!quotaToRegularize.multa || quotaToRegularize.multa <= 0) {
    return {
      isValid: false,
      message: 'Esta quota não possui multa para regularizar'
    };
  }

  // Filtrar quotas do mesmo morador que têm multa não regularizada
  const moradorQuotasWithFines = allQuotas.filter(q =>
    q.morador_id === quotaToRegularize.morador_id &&
    q.multa > 0 &&
    q.situacao !== 'Regularizada' // Verificar se a multa não foi regularizada
  );

  console.log(`👤 Quotas com multa do morador: ${moradorQuotasWithFines.length}`);

  // Ordenar por ano e mês (mais antigas primeiro)
  const sortedQuotasWithFines = moradorQuotasWithFines.sort((a, b) => {
    if (a.ano !== b.ano) {
      return a.ano - b.ano;
    }
    return a.mes - b.mes;
  });

  // Encontrar multas anteriores não regularizadas
  const unpaidPreviousFines = sortedQuotasWithFines.filter(q => {
    // Multa é anterior se:
    // 1. Ano é menor, ou
    // 2. Mesmo ano mas mês é menor
    return (q.ano < quotaToRegularize.ano) ||
           (q.ano === quotaToRegularize.ano && q.mes < quotaToRegularize.mes);
  });

  console.log(`📅 Multas anteriores não regularizadas: ${unpaidPreviousFines.length}`);

  if (unpaidPreviousFines.length > 0) {
    // Criar mensagem detalhada com os meses pendentes
    const monthNames = [
      'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
      'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
    ];

    const pendingPeriods = unpaidPreviousFines.map(q =>
      `${monthNames[q.mes - 1]}/${q.ano}`
    ).join(', ');

    const message = unpaidPreviousFines.length === 1
      ? `Você deve regularizar primeiro a multa de ${pendingPeriods} antes de regularizar esta.`
      : `Você deve regularizar primeiro as multas de ${pendingPeriods} antes de regularizar esta.`;

    console.log('❌ Regularização sequencial violada:', message);

    return {
      isValid: false,
      message,
      unpaidFines: unpaidPreviousFines
    };
  }

  console.log('✅ Regularização sequencial válida');
  return { isValid: true };
};
