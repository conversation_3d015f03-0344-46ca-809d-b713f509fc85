
import { supabase } from '@/integrations/supabase/client';

export const deleteUser = async (userId: string): Promise<boolean> => {
  try {
    // Since we don't have admin access, just delete the profile
    // The auth user will remain but without associated profile
    const { error } = await supabase
      .from('profiles')
      .delete()
      .eq('id', userId);
    
    if (error) {
      console.error('Error deleting user profile:', error);
      throw error;
    }

    return true;
  } catch (error) {
    console.error('Error in deleteUser:', error);
    throw error;
  }
};
