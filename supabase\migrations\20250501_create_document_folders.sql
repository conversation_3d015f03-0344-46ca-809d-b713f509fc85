
-- Create document_folders table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.document_folders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    parent_id UUID REFERENCES public.document_folders(id),
    created_by UUID REFERENCES auth.users(id) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Add RLS policies to document_folders table
ALTER TABLE public.document_folders ENABLE ROW LEVEL SECURITY;

-- Allow anyone to read document folders
CREATE POLICY "Allow anyone to read document folders"
    ON public.document_folders
    FOR SELECT
    TO authenticated
    USING (true);

-- Allow authenticated users to create document folders
CREATE POLICY "Allow authenticated users to create document folders"
    ON public.document_folders
    FOR INSERT
    TO authenticated
    WITH CHECK (true);

-- Allow users to update their own document folders
CREATE POLICY "Allow users to update their own document folders"
    ON public.document_folders
    FOR UPDATE
    TO authenticated
    USING (auth.uid() = created_by)
    WITH CHECK (auth.uid() = created_by);

-- Allow users to delete their own document folders
CREATE POLICY "Allow users to delete their own document folders"
    ON public.document_folders
    FOR DELETE
    TO authenticated
    USING (auth.uid() = created_by);
