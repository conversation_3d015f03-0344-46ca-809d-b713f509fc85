
import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { cn } from '@/lib/utils';
import {
  Home,
  Users,
  UserCog,
  Banknote,
  TrendingUp,
  User,
  ClipboardList,
  Settings,
  FolderOpen,
  X
} from 'lucide-react';
import { useSidebar } from '@/contexts/SidebarContext';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

const SidebarItem = ({
  icon: Icon,
  children,
  to,
  active = false,
  collapsed = false,
  onClick
}: {
  icon: any;
  children: React.ReactNode;
  to: string;
  active?: boolean;
  collapsed?: boolean;
  onClick?: () => void;
}) => {
  const linkContent = (
    <Link
      to={to}
      onClick={onClick}
      className={cn(
        'flex items-center space-x-3 rounded-lg px-3 py-2 transition-all hover:bg-sidebar-accent',
        active ? 'bg-sidebar-accent text-primary-600 font-medium' : 'text-gray-600',
        collapsed ? 'justify-center' : ''
      )}
    >
      <Icon size={20} className={active ? 'text-primary' : 'text-gray-500'} />
      {!collapsed && <span>{children}</span>}
    </Link>
  );

  if (collapsed) {
    return (
      <Tooltip>
        <TooltipTrigger asChild>
          {linkContent}
        </TooltipTrigger>
        <TooltipContent side="right" className="ml-2">
          {children}
        </TooltipContent>
      </Tooltip>
    );
  }

  return linkContent;
};

const Sidebar = () => {
  const location = useLocation();
  const { collapsed, isMobile, mobileOpen, setMobileOpen } = useSidebar();

  const handleLinkClick = () => {
    if (isMobile) {
      setMobileOpen(false);
    }
  };

  // Mobile drawer overlay
  if (isMobile) {
    return (
      <>
        {/* Backdrop */}
        {mobileOpen && (
          <div
            className="fixed inset-0 z-40 bg-black/50 backdrop-blur-sm"
            onClick={() => setMobileOpen(false)}
          />
        )}

        {/* Mobile Sidebar Drawer */}
        <div className={cn(
          'fixed top-0 left-0 z-50 h-full w-64 bg-sidebar transition-transform duration-300 border-r border-gray-200',
          mobileOpen ? 'translate-x-0' : '-translate-x-full'
        )}>
          <div className="flex flex-col h-full">
            {/* Header with close button */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <span className="text-lg font-bold text-primary">Menu</span>
              <button
                onClick={() => setMobileOpen(false)}
                className="p-1.5 rounded-md hover:bg-gray-100 transition-colors"
              >
                <X size={20} className="text-gray-600" />
              </button>
            </div>

            {/* Navigation */}
            <div className="flex-1 overflow-y-auto py-4 px-3">
              <nav className="space-y-1">
                <SidebarItem
                  icon={Home}
                  to="/"
                  active={location.pathname === '/'}
                  collapsed={false}
                  onClick={handleLinkClick}
                >
                  Painel
                </SidebarItem>

                <SidebarItem
                  icon={Users}
                  to="/residents"
                  active={location.pathname === '/residents'}
                  collapsed={false}
                  onClick={handleLinkClick}
                >
                  Moradores
                </SidebarItem>

                <SidebarItem
                  icon={UserCog}
                  to="/committee-members"
                  active={location.pathname === '/committee-members'}
                  collapsed={false}
                  onClick={handleLinkClick}
                >
                  Comissão
                </SidebarItem>

                <SidebarItem
                  icon={Banknote}
                  to="/quotas"
                  active={location.pathname === '/quotas'}
                  collapsed={false}
                  onClick={handleLinkClick}
                >
                  Quotas
                </SidebarItem>

                <SidebarItem
                  icon={TrendingUp}
                  to="/cash-flow"
                  active={location.pathname === '/cash-flow'}
                  collapsed={false}
                  onClick={handleLinkClick}
                >
                  Fluxo de Caixa
                </SidebarItem>

                <SidebarItem
                  icon={User}
                  to="/users"
                  active={location.pathname === '/users'}
                  collapsed={false}
                  onClick={handleLinkClick}
                >
                  Usuários
                </SidebarItem>

                <SidebarItem
                  icon={FolderOpen}
                  to="/documents"
                  active={location.pathname.startsWith('/documents')}
                  collapsed={false}
                  onClick={handleLinkClick}
                >
                  Documentos
                </SidebarItem>

                <SidebarItem
                  icon={ClipboardList}
                  to="/reports"
                  active={location.pathname === '/reports'}
                  collapsed={false}
                  onClick={handleLinkClick}
                >
                  Relatórios
                </SidebarItem>

                <SidebarItem
                  icon={Settings}
                  to="/settings"
                  active={location.pathname === '/settings'}
                  collapsed={false}
                  onClick={handleLinkClick}
                >
                  Configurações
                </SidebarItem>
              </nav>
            </div>
          </div>
        </div>
      </>
    );
  }

  // Desktop sidebar
  return (
    <TooltipProvider>
      <div className={cn(
        'fixed top-14 left-0 z-10 h-[calc(100vh-3.5rem)] bg-sidebar transition-all duration-300 border-r border-gray-200',
        collapsed ? 'w-12' : 'w-64'
      )}>
        <div className="flex flex-col h-full">
          <div className={cn(
            'flex-1 overflow-y-auto py-4',
            collapsed ? 'px-1' : 'px-3'
          )}>
            <nav className="space-y-1">
              <SidebarItem
                icon={Home}
                to="/"
                active={location.pathname === '/'}
                collapsed={collapsed}
              >
                Painel
              </SidebarItem>

              <SidebarItem
                icon={Users}
                to="/residents"
                active={location.pathname === '/residents'}
                collapsed={collapsed}
              >
                Moradores
              </SidebarItem>

              <SidebarItem
                icon={UserCog}
                to="/committee-members"
                active={location.pathname === '/committee-members'}
                collapsed={collapsed}
              >
                Comissão
              </SidebarItem>

              <SidebarItem
                icon={Banknote}
                to="/quotas"
                active={location.pathname === '/quotas'}
                collapsed={collapsed}
              >
                Quotas
              </SidebarItem>

              <SidebarItem
                icon={TrendingUp}
                to="/cash-flow"
                active={location.pathname === '/cash-flow'}
                collapsed={collapsed}
              >
                Fluxo de Caixa
              </SidebarItem>

              <SidebarItem
                icon={User}
                to="/users"
                active={location.pathname === '/users'}
                collapsed={collapsed}
              >
                Usuários
              </SidebarItem>

              <SidebarItem
                icon={FolderOpen}
                to="/documents"
                active={location.pathname.startsWith('/documents')}
                collapsed={collapsed}
              >
                Documentos
              </SidebarItem>

              <SidebarItem
                icon={ClipboardList}
                to="/reports"
                active={location.pathname === '/reports'}
                collapsed={collapsed}
              >
                Relatórios
              </SidebarItem>

              <SidebarItem
                icon={Settings}
                to="/settings"
                active={location.pathname === '/settings'}
                collapsed={collapsed}
              >
                Configurações
              </SidebarItem>
            </nav>
          </div>
        </div>
      </div>
    </TooltipProvider>
  );
};

export default Sidebar;
