
import { Skeleton } from "@/components/ui/skeleton";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { RefreshCw, AlertTriangle } from "lucide-react";

const DashboardSkeleton = () => {
  return (
    <div className="space-y-6">
      {/* Header skeleton */}
      <div className="space-y-2">
        <Skeleton className="h-8 w-64" />
        <Skeleton className="h-4 w-96" />
      </div>

      {/* Stats cards skeleton */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <div key={i} className="space-y-3 p-6 border rounded-lg">
            <div className="flex items-center justify-between">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-4 w-4 rounded" />
            </div>
            <Skeleton className="h-8 w-16" />
            <Skeleton className="h-3 w-24" />
          </div>
        ))}
      </div>

      {/* Charts skeleton */}
      <div className="grid gap-4 md:grid-cols-2">
        <div className="space-y-3 p-6 border rounded-lg">
          <Skeleton className="h-6 w-48" />
          <Skeleton className="h-80 w-full" />
        </div>
        <div className="space-y-3 p-6 border rounded-lg">
          <Skeleton className="h-6 w-48" />
          <Skeleton className="h-80 w-full" />
        </div>
      </div>
    </div>
  );
};

interface DashboardErrorSkeletonProps {
  onRetry: () => void;
  message: string;
}

export const DashboardErrorSkeleton = ({ onRetry, message }: DashboardErrorSkeletonProps) => {
  return (
    <div className="flex flex-col items-center justify-center min-h-[400px] p-8">
      <div className="text-center space-y-4">
        <AlertTriangle className="h-16 w-16 text-red-500 mx-auto" />
        <h3 className="text-lg font-semibold text-gray-900">
          Erro ao carregar dashboard
        </h3>
        <p className="text-gray-600 max-w-md">
          {message}
        </p>
        <Button
          onClick={onRetry}
          className="inline-flex items-center gap-2"
        >
          <RefreshCw className="h-4 w-4" />
          Tentar novamente
        </Button>
      </div>
    </div>
  );
};

export default DashboardSkeleton;
