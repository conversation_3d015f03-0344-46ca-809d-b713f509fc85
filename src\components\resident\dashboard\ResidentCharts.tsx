import React from 'react';
import Chart from '@/components/dashboard/Chart';
import { useResidentQuotas } from '@/hooks/resident/useResidentQuotas';

interface ResidentChartsProps {
  className?: string;
}

const ResidentCharts: React.FC<ResidentChartsProps> = ({ className }) => {
  const { quotas, getPaymentStats } = useResidentQuotas();
  const stats = getPaymentStats();

  // Preparar dados para gráfico de status de quotas (pizza)
  const quotaStatusData = [
    { name: 'Pagas', value: stats.paidQuotas, color: '#10B981' },
    { name: 'Pendentes', value: stats.pendingQuotas, color: '#F59E0B' },
    { name: 'Em Atraso', value: stats.overdueQuotas, color: '#EF4444' }
  ].filter(item => item.value > 0); // Só mostrar categorias com valores

  // Preparar dados para gráfico de multas (pizza)
  const regularizedFines = quotas.filter(q => q.fine && q.fine > 0 && q.fineStatus === 'Regularizada').length;
  const pendingFines = quotas.filter(q => q.fine && q.fine > 0 && q.fineStatus !== 'Regularizada').length;
  
  const fineStatusData = [
    { name: 'Regularizadas', value: regularizedFines, color: '#10B981' },
    { name: 'Pendentes', value: pendingFines, color: '#EF4444' }
  ].filter(item => item.value > 0);

  // Preparar dados para histórico mensal (últimos 6 meses)
  const currentDate = new Date();
  const monthlyData = [];
  
  for (let i = 5; i >= 0; i--) {
    const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
    const month = date.getMonth() + 1;
    const year = date.getFullYear();
    
    const monthQuotas = quotas.filter(q => q.month === month && q.year === year);
    const paidQuotas = monthQuotas.filter(q => q.status === 'paid').length;
    const pendingQuotas = monthQuotas.filter(q => q.status === 'pending' || q.status === 'overdue').length;
    
    const monthNames = ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'];
    
    monthlyData.push({
      name: `${monthNames[month - 1]}/${year.toString().slice(-2)}`,
      pagas: paidQuotas,
      pendentes: pendingQuotas
    });
  }

  // Preparar dados para histórico de valores (últimos 6 meses)
  const valueData = [];
  
  for (let i = 5; i >= 0; i--) {
    const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
    const month = date.getMonth() + 1;
    const year = date.getFullYear();
    
    const monthQuotas = quotas.filter(q => q.month === month && q.year === year);
    const paidAmount = monthQuotas.filter(q => q.status === 'paid').reduce((sum, q) => sum + q.amount, 0);
    const pendingAmount = monthQuotas.filter(q => q.status === 'pending' || q.status === 'overdue').reduce((sum, q) => sum + q.amount, 0);
    const fineAmount = monthQuotas.filter(q => q.fineStatus !== 'Regularizada').reduce((sum, q) => sum + (q.fine || 0), 0);
    
    const monthNames = ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'];
    
    valueData.push({
      name: `${monthNames[month - 1]}/${year.toString().slice(-2)}`,
      pago: paidAmount,
      pendente: pendingAmount,
      multas: fineAmount
    });
  }

  return (
    <div className={`space-y-6 ${className}`}>
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-2">Análise Visual</h3>
        <p className="text-gray-600">Acompanhe seu histórico de pagamentos e situação financeira</p>
      </div>

      {/* Primeira linha - Gráficos de pizza */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <Chart
            type="pie"
            data={quotaStatusData}
            title="Status das Quotas"
            subtitle="Distribuição por situação de pagamento"
            dataKeys={['value']}
            height={300}
            colors={quotaStatusData.map(item => item.color)}
            showLegend={true}
            innerRadius={60}
            outerRadius={120}
            pieLabelsOnly={true}
            formatAsCount={true}
          />
        </div>

        {fineStatusData.length > 0 && (
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <Chart
              type="pie"
              data={fineStatusData}
              title="Status das Multas"
              subtitle="Distribuição por situação de regularização"
              dataKeys={['value']}
              height={300}
              colors={fineStatusData.map(item => item.color)}
              showLegend={true}
              innerRadius={60}
              outerRadius={120}
              pieLabelsOnly={true}
              formatAsCount={true}
            />
          </div>
        )}
      </div>

      {/* Segunda linha - Gráficos de histórico */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <Chart
            type="bar"
            data={monthlyData}
            title="Histórico de Quotas"
            subtitle="Últimos 6 meses - Quantidade"
            dataKeys={['pagas', 'pendentes']}
            height={250}
            colors={['#10B981', '#F59E0B']}
            showLegend={true}
            showGrid={true}
          />
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <Chart
            type="area"
            data={valueData}
            title="Histórico Financeiro"
            subtitle="Últimos 6 meses - Valores em Kz"
            dataKeys={['pago', 'pendente', 'multas']}
            height={250}
            colors={['#10B981', '#F59E0B', '#EF4444']}
            showLegend={true}
            showGrid={true}
          />
        </div>
      </div>
    </div>
  );
};

export default ResidentCharts;
