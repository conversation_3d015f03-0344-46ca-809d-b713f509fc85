
import { z } from 'zod';

export const grantAccessSchema = z.object({
  email: z.string().email('Email inválido'),
  useGeneratedPassword: z.boolean().default(true),
  customPassword: z.string().optional(),
  sendEmailToResident: z.boolean().default(false),
}).refine((data) => {
  if (!data.useGeneratedPassword && !data.customPassword?.trim()) {
    return false;
  }
  return true;
}, {
  message: "Senha personalizada é obrigatória quando não usar senha gerada",
  path: ["customPassword"]
});

export type GrantAccessFormValues = z.infer<typeof grantAccessSchema>;

export interface SuccessCredentials {
  email: string;
  password: string;
  name: string;
  apartment: string;
}

export interface GrantAccessModalProps {
  isOpen: boolean;
  onClose: () => void;
  resident: {
    nome: string;
    apartamento: string;
    email: string;
  };
  onGrantAccess: (params: { email: string; password: string; name: string; apartment_id: string }) => void;
  isLoading?: boolean;
  error?: Error | null;
  status?: 'idle' | 'pending' | 'success' | 'error';
}
