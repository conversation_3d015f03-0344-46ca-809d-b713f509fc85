
import React from 'react';
import { Info, AlertTriangle } from 'lucide-react';

interface DashboardWarningProps {
  apartmentId?: string;
  type?: 'no-data' | 'sync-issue';
}

const DashboardWarning: React.FC<DashboardWarningProps> = ({ 
  apartmentId, 
  type = 'no-data' 
}) => {
  if (type === 'sync-issue') {
    return (
      <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
        <div className="flex items-center">
          <AlertTriangle className="h-5 w-5 text-yellow-500 mr-2" />
          <div>
            <p className="text-sm text-yellow-800 font-medium">
              Dados em sincronização
            </p>
            <p className="text-xs text-yellow-600">
              Seus dados estão sendo sincronizados no sistema. Se o problema persistir, entre em contato com a administração.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
      <div className="flex items-center">
        <Info className="h-5 w-5 text-blue-500 mr-2" />
        <div>
          <p className="text-sm text-blue-800 font-medium">
            Dados do apartamento {apartmentId} não encontrados
          </p>
          <p className="text-xs text-blue-600">
            Entre em contato com a administração para que seus dados sejam cadastrados no sistema.
          </p>
        </div>
      </div>
    </div>
  );
};

export default DashboardWarning;
